
  <html lang="en">
    <head>
      <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
<style>
    .bg-blue {
        color: #ffffff;
        background-color: #1589dd;
    }
</style>
    </head>
    <body>
      <div class="container">
        
          
          
            <h2 id="kyverno-io-v2">Package: <span style="font-family: monospace">kyverno.io/v2</span></h2>
            <p></p>
          
        
        
          
            
            <h3>Resource Types:</h3>
            <ul><li>
                    <a href="#kyverno-io-v2-CleanupPolicy">CleanupPolicy</a>
                  </li><li>
                    <a href="#kyverno-io-v2-ClusterCleanupPolicy">ClusterCleanupPolicy</a>
                  </li><li>
                    <a href="#kyverno-io-v2-PolicyException">PolicyException</a>
                  </li><li>
                    <a href="#kyverno-io-v2-UpdateRequest">UpdateRequest</a>
                  </li></ul>

            
            
  <H3 id="kyverno-io-v2-CleanupPolicy">CleanupPolicy
    </H3>

  

  <p><p>CleanupPolicy defines a rule for resource cleanup.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        
          
          <tr>
            <td><code>apiVersion</code></br>string</td>
            <td><code>kyverno.io/v2</code></td>
          </tr>
          <tr>
            <td><code>kind</code></br>string</td>
            <td><code>CleanupPolicy</code></td>
          </tr>
        

        
        

  
  
    
    
  
    
    
      <tr>
        <td><code>metadata</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.ObjectMeta</span>
            
          
        </td>
        <td>
          

          

          
            Refer to the Kubernetes API documentation for the fields of the
            <code>metadata</code> field.
          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>spec</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-CleanupPolicySpec">
                <span style="font-family: monospace">CleanupPolicySpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Spec declares policy behaviors.</p>


          

          
            <br/>
            <br/>
            <table>
              

  
  
    
    
      <tr>
        <td><code>context</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ContextEntry">
                <span style="font-family: monospace">[]ContextEntry</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Context defines variables and data sources that can be used during rule execution.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>match</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-MatchResources">
                <span style="font-family: monospace">MatchResources</span>
              </a>
            
          
        </td>
        <td>
          

          <p>MatchResources defines when cleanuppolicy should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>exclude</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-MatchResources">
                <span style="font-family: monospace">MatchResources</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ExcludeResources defines when cleanuppolicy should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>schedule</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>The schedule in Cron format</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>conditions</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-AnyAllConditions">
                <span style="font-family: monospace">AnyAllConditions</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Conditions defines the conditions used to select the resources which will be cleaned up.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>deletionPropagationPolicy</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.DeletionPropagation</span>
            
          
        </td>
        <td>
          

          <p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>


          

          
        </td>
      </tr>
    
  

            </table>
          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>status</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-CleanupPolicyStatus">
                <span style="font-family: monospace">CleanupPolicyStatus</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Status contains policy runtime data.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-ClusterCleanupPolicy">ClusterCleanupPolicy
    </H3>

  

  <p><p>ClusterCleanupPolicy defines rule for resource cleanup.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        
          
          <tr>
            <td><code>apiVersion</code></br>string</td>
            <td><code>kyverno.io/v2</code></td>
          </tr>
          <tr>
            <td><code>kind</code></br>string</td>
            <td><code>ClusterCleanupPolicy</code></td>
          </tr>
        

        
        

  
  
    
    
  
    
    
      <tr>
        <td><code>metadata</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.ObjectMeta</span>
            
          
        </td>
        <td>
          

          

          
            Refer to the Kubernetes API documentation for the fields of the
            <code>metadata</code> field.
          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>spec</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-CleanupPolicySpec">
                <span style="font-family: monospace">CleanupPolicySpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Spec declares policy behaviors.</p>


          

          
            <br/>
            <br/>
            <table>
              

  
  
    
    
      <tr>
        <td><code>context</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ContextEntry">
                <span style="font-family: monospace">[]ContextEntry</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Context defines variables and data sources that can be used during rule execution.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>match</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-MatchResources">
                <span style="font-family: monospace">MatchResources</span>
              </a>
            
          
        </td>
        <td>
          

          <p>MatchResources defines when cleanuppolicy should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>exclude</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-MatchResources">
                <span style="font-family: monospace">MatchResources</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ExcludeResources defines when cleanuppolicy should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>schedule</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>The schedule in Cron format</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>conditions</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-AnyAllConditions">
                <span style="font-family: monospace">AnyAllConditions</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Conditions defines the conditions used to select the resources which will be cleaned up.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>deletionPropagationPolicy</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.DeletionPropagation</span>
            
          
        </td>
        <td>
          

          <p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>


          

          
        </td>
      </tr>
    
  

            </table>
          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>status</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-CleanupPolicyStatus">
                <span style="font-family: monospace">CleanupPolicyStatus</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Status contains policy runtime data.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-PolicyException">PolicyException
    </H3>

  

  <p><p>PolicyException declares resources to be excluded from specified policies.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        
          
          <tr>
            <td><code>apiVersion</code></br>string</td>
            <td><code>kyverno.io/v2</code></td>
          </tr>
          <tr>
            <td><code>kind</code></br>string</td>
            <td><code>PolicyException</code></td>
          </tr>
        

        
        

  
  
    
    
  
    
    
      <tr>
        <td><code>metadata</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.ObjectMeta</span>
            
          
        </td>
        <td>
          

          

          
            Refer to the Kubernetes API documentation for the fields of the
            <code>metadata</code> field.
          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>spec</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-PolicyExceptionSpec">
                <span style="font-family: monospace">PolicyExceptionSpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Spec declares policy exception behaviors.</p>


          

          
            <br/>
            <br/>
            <table>
              

  
  
    
    
      <tr>
        <td><code>background</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Background controls if exceptions are applied to existing policies during a background scan.
Optional. Default value is &quot;true&quot;. The value must be set to &quot;false&quot; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>match</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">github.com/kyverno/kyverno/api/kyverno/v2beta1.MatchResources</span>
            
          
        </td>
        <td>
          

          <p>Match defines match clause used to check if a resource applies to the exception</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>conditions</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-AnyAllConditions">
                <span style="font-family: monospace">AnyAllConditions</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Conditions are used to determine if a resource applies to the exception by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>exceptions</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-Exception">
                <span style="font-family: monospace">[]Exception</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Exceptions is a list policy/rules to be excluded</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>podSecurity</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-PodSecurityStandard">
                <span style="font-family: monospace">[]PodSecurityStandard</span>
              </a>
            
          
        </td>
        <td>
          

          <p>PodSecurity specifies the Pod Security Standard controls to be excluded.
Applicable only to policies that have validate.podSecurity subrule.</p>


          

          
        </td>
      </tr>
    
  

            </table>
          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-UpdateRequest">UpdateRequest
    </H3>

  

  <p><p>UpdateRequest is a request to process mutate and generate rules in background.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        
          
          <tr>
            <td><code>apiVersion</code></br>string</td>
            <td><code>kyverno.io/v2</code></td>
          </tr>
          <tr>
            <td><code>kind</code></br>string</td>
            <td><code>UpdateRequest</code></td>
          </tr>
        

        
        

  
  
    
    
  
    
    
      <tr>
        <td><code>metadata</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.ObjectMeta</span>
            
          
        </td>
        <td>
          

          

          
            Refer to the Kubernetes API documentation for the fields of the
            <code>metadata</code> field.
          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>spec</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-UpdateRequestSpec">
                <span style="font-family: monospace">UpdateRequestSpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ResourceSpec is the information to identify the trigger resource.</p>


          

          
            <br/>
            <br/>
            <table>
              

  
  
    
    
      <tr>
        <td><code>requestType</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-RequestType">
                <span style="font-family: monospace">RequestType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Type represents request type for background processing</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>policy</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Specifies the name of the policy.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>ruleContext</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-RuleContext">
                <span style="font-family: monospace">[]RuleContext</span>
              </a>
            
          
        </td>
        <td>
          

          <p>RuleContext is the associate context to apply rules.
optional</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>rule</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Rule is the associate rule name of the current UR.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>deleteDownstream</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>DeleteDownstream represents whether the downstream needs to be deleted.
Deprecated</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>synchronize</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Synchronize represents the sync behavior of the corresponding rule
Optional. Defaults to &quot;false&quot; if not specified.
Deprecated, will be removed in 1.14.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>resource</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ResourceSpec">
                <span style="font-family: monospace">ResourceSpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ResourceSpec is the information to identify the trigger resource.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>context</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-UpdateRequestSpecContext">
                <span style="font-family: monospace">UpdateRequestSpecContext</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Context represents admission request context.
It is used upon admission review only and is shared across rules within the same UR.</p>


          

          
        </td>
      </tr>
    
  

            </table>
          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>status</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-UpdateRequestStatus">
                <span style="font-family: monospace">UpdateRequestStatus</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Status contains statistics related to update request.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-AdmissionRequestInfoObject">AdmissionRequestInfoObject
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-UpdateRequestSpecContext">UpdateRequestSpecContext</a>)
    </p>
  

  <p><p>AdmissionRequestInfoObject stores the admission request and operation details</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>admissionRequest</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">admission/v1.AdmissionRequest</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>operation</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">admission/v1.Operation</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-AnyAllConditions">AnyAllConditions
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-CleanupPolicySpec">CleanupPolicySpec</a>, 
        <a href="#kyverno-io-v2-PolicyExceptionSpec">PolicyExceptionSpec</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>any</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-Condition">
                <span style="font-family: monospace">[]Condition</span>
              </a>
            
          
        </td>
        <td>
          

          <p>AnyConditions enable variable-based conditional rule execution. This is useful for
finer control of when an rule is applied. A condition can reference object data
using JMESPath notation.
Here, at least one of the conditions need to pass.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>all</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-Condition">
                <span style="font-family: monospace">[]Condition</span>
              </a>
            
          
        </td>
        <td>
          

          <p>AllConditions enable variable-based conditional rule execution. This is useful for
finer control of when an rule is applied. A condition can reference object data
using JMESPath notation.
Here, all of the conditions need to pass.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-CleanupPolicySpec">CleanupPolicySpec
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-CleanupPolicy">CleanupPolicy</a>, 
        <a href="#kyverno-io-v2-ClusterCleanupPolicy">ClusterCleanupPolicy</a>)
    </p>
  

  <p><p>CleanupPolicySpec stores specifications for selecting resources that the user needs to delete
and schedule when the matching resources needs deleted.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>context</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ContextEntry">
                <span style="font-family: monospace">[]ContextEntry</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Context defines variables and data sources that can be used during rule execution.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>match</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-MatchResources">
                <span style="font-family: monospace">MatchResources</span>
              </a>
            
          
        </td>
        <td>
          

          <p>MatchResources defines when cleanuppolicy should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>exclude</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-MatchResources">
                <span style="font-family: monospace">MatchResources</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ExcludeResources defines when cleanuppolicy should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>schedule</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>The schedule in Cron format</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>conditions</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-AnyAllConditions">
                <span style="font-family: monospace">AnyAllConditions</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Conditions defines the conditions used to select the resources which will be cleaned up.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>deletionPropagationPolicy</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.DeletionPropagation</span>
            
          
        </td>
        <td>
          

          <p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-CleanupPolicyStatus">CleanupPolicyStatus
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-CleanupPolicy">CleanupPolicy</a>, 
        <a href="#kyverno-io-v2-ClusterCleanupPolicy">ClusterCleanupPolicy</a>)
    </p>
  

  <p><p>CleanupPolicyStatus stores the status of the policy.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>conditions</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]meta/v1.Condition</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>lastExecutionTime</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.Time</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-Condition">Condition
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-AnyAllConditions">AnyAllConditions</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>key</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">github.com/kyverno/kyverno/api/kyverno.Any</span>
            
          
        </td>
        <td>
          

          <p>Key is the context entry (using JMESPath) for conditional rule evaluation.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>operator</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-ConditionOperator">
                <span style="font-family: monospace">ConditionOperator</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Operator is the conditional operation to perform. Valid operators are:
Equals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,
GreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,
DurationLessThanOrEquals, DurationLessThan</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>value</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">github.com/kyverno/kyverno/api/kyverno.Any</span>
            
          
        </td>
        <td>
          

          <p>Value is the conditional value, or set of values. The values can be fixed set
or can be variables declared using JMESPath.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>message</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Message is an optional display message</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-ConditionOperator">ConditionOperator
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-Condition">Condition</a>)
    </p>
  

  <p><p>ConditionOperator is the operation performed on condition key and value.</p>
</p>

  

  <H3 id="kyverno-io-v2-Exception">Exception
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-PolicyExceptionSpec">PolicyExceptionSpec</a>)
    </p>
  

  <p><p>Exception stores infos about a policy and rules</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>policyName</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>PolicyName identifies the policy to which the exception is applied.
The policy name uses the format <!-- raw HTML omitted -->/<!-- raw HTML omitted --> unless it
references a ClusterPolicy.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>ruleNames</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>RuleNames identifies the rules to which the exception is applied.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-MatchResources">MatchResources
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-CleanupPolicySpec">CleanupPolicySpec</a>)
    </p>
  

  <p></p>

  

  <H3 id="kyverno-io-v2-PolicyExceptionSpec">PolicyExceptionSpec
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-PolicyException">PolicyException</a>)
    </p>
  

  <p><p>PolicyExceptionSpec stores policy exception spec</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>background</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Background controls if exceptions are applied to existing policies during a background scan.
Optional. Default value is &quot;true&quot;. The value must be set to &quot;false&quot; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>match</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">github.com/kyverno/kyverno/api/kyverno/v2beta1.MatchResources</span>
            
          
        </td>
        <td>
          

          <p>Match defines match clause used to check if a resource applies to the exception</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>conditions</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-AnyAllConditions">
                <span style="font-family: monospace">AnyAllConditions</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Conditions are used to determine if a resource applies to the exception by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>exceptions</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-Exception">
                <span style="font-family: monospace">[]Exception</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Exceptions is a list policy/rules to be excluded</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>podSecurity</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-PodSecurityStandard">
                <span style="font-family: monospace">[]PodSecurityStandard</span>
              </a>
            
          
        </td>
        <td>
          

          <p>PodSecurity specifies the Pod Security Standard controls to be excluded.
Applicable only to policies that have validate.podSecurity subrule.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-RequestInfo">RequestInfo
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-UpdateRequestSpecContext">UpdateRequestSpecContext</a>)
    </p>
  

  <p><p>RequestInfo contains permission info carried in an admission request.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>roles</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>Roles is a list of possible role send the request.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>clusterRoles</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>ClusterRoles is a list of possible clusterRoles send the request.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>userInfo</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">authentication/v1.UserInfo</span>
            
          
        </td>
        <td>
          

          <p>UserInfo is the userInfo carried in the admission request.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>synchronize</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>DryRun indicates that modifications will definitely not be persisted for this request.
Defaults to false.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-RequestType">RequestType
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-UpdateRequestSpec">UpdateRequestSpec</a>)
    </p>
  

  <p></p>

  

  <H3 id="kyverno-io-v2-RuleContext">RuleContext
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-UpdateRequestSpec">UpdateRequestSpec</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>rule</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Rule is the associate rule name of the current UR.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>deleteDownstream</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>DeleteDownstream represents whether the downstream needs to be deleted.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>synchronize</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Synchronize represents the sync behavior of the corresponding rule
Optional. Defaults to &quot;false&quot; if not specified.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>trigger</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ResourceSpec">
                <span style="font-family: monospace">ResourceSpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ResourceSpec is the information to identify the trigger resource.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>cacheRestore</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>CacheRestore indicates whether the cache should be restored.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-UpdateRequestSpec">UpdateRequestSpec
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-UpdateRequest">UpdateRequest</a>)
    </p>
  

  <p><p>UpdateRequestSpec stores the request specification.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>requestType</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-RequestType">
                <span style="font-family: monospace">RequestType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Type represents request type for background processing</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>policy</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Specifies the name of the policy.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>ruleContext</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-RuleContext">
                <span style="font-family: monospace">[]RuleContext</span>
              </a>
            
          
        </td>
        <td>
          

          <p>RuleContext is the associate context to apply rules.
optional</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>rule</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Rule is the associate rule name of the current UR.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>deleteDownstream</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>DeleteDownstream represents whether the downstream needs to be deleted.
Deprecated</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>synchronize</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Synchronize represents the sync behavior of the corresponding rule
Optional. Defaults to &quot;false&quot; if not specified.
Deprecated, will be removed in 1.14.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>resource</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ResourceSpec">
                <span style="font-family: monospace">ResourceSpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ResourceSpec is the information to identify the trigger resource.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>context</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-UpdateRequestSpecContext">
                <span style="font-family: monospace">UpdateRequestSpecContext</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Context represents admission request context.
It is used upon admission review only and is shared across rules within the same UR.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-UpdateRequestSpecContext">UpdateRequestSpecContext
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-UpdateRequestSpec">UpdateRequestSpec</a>)
    </p>
  

  <p><p>UpdateRequestSpecContext stores the context to be shared.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>userInfo</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-RequestInfo">
                <span style="font-family: monospace">RequestInfo</span>
              </a>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>admissionRequestInfo</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-AdmissionRequestInfoObject">
                <span style="font-family: monospace">AdmissionRequestInfoObject</span>
              </a>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2-UpdateRequestState">UpdateRequestState
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-UpdateRequestStatus">UpdateRequestStatus</a>)
    </p>
  

  <p><p>UpdateRequestState defines the state of request.</p>
</p>

  

  <H3 id="kyverno-io-v2-UpdateRequestStatus">UpdateRequestStatus
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2-UpdateRequest">UpdateRequest</a>)
    </p>
  

  <p><p>UpdateRequestStatus defines the observed state of UpdateRequest</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>state</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2-UpdateRequestState">
                <span style="font-family: monospace">UpdateRequestState</span>
              </a>
            
          
        </td>
        <td>
          

          <p>State represents state of the update request.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>message</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Specifies request status message.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>generatedResources</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ResourceSpec">
                <span style="font-family: monospace">[]ResourceSpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>This will track the resources that are updated by the generate Policy.
Will be used during clean up resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>retryCount</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">int</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

          
          <hr />
        
      </div>
    </body>
  </html>

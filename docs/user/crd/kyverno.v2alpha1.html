
  <html lang="en">
    <head>
      <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
<style>
    .bg-blue {
        color: #ffffff;
        background-color: #1589dd;
    }
</style>
    </head>
    <body>
      <div class="container">
        
          
          
            <h2 id="kyverno-io-v2alpha1">Package: <span style="font-family: monospace">kyverno.io/v2alpha1</span></h2>
            <p></p>
          
        
        
          
            
            <h3>Resource Types:</h3>
            <ul><li>
                    <a href="#kyverno-io-v2alpha1-GlobalContextEntry">GlobalContextEntry</a>
                  </li></ul>

            
            
  <H3 id="kyverno-io-v2alpha1-GlobalContextEntry">GlobalContextEntry
    </H3>

  

  <p><p>GlobalContextEntry declares resources to be cached.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        
          
          <tr>
            <td><code>apiVersion</code></br>string</td>
            <td><code>kyverno.io/v2alpha1</code></td>
          </tr>
          <tr>
            <td><code>kind</code></br>string</td>
            <td><code>GlobalContextEntry</code></td>
          </tr>
        

        
        

  
  
    
    
  
    
    
      <tr>
        <td><code>metadata</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.ObjectMeta</span>
            
          
        </td>
        <td>
          

          

          
            Refer to the Kubernetes API documentation for the fields of the
            <code>metadata</code> field.
          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>spec</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2alpha1-GlobalContextEntrySpec">
                <span style="font-family: monospace">GlobalContextEntrySpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Spec declares policy exception behaviors.</p>


          

          
            <br/>
            <br/>
            <table>
              

  
  
    
    
      <tr>
        <td><code>kubernetesResource</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2alpha1-KubernetesResource">
                <span style="font-family: monospace">KubernetesResource</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Stores a list of Kubernetes resources which will be cached.
Mutually exclusive with APICall.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>apiCall</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2alpha1-ExternalAPICall">
                <span style="font-family: monospace">ExternalAPICall</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Stores results from an API call which will be cached.
Mutually exclusive with KubernetesResource.
This can be used to make calls to external (non-Kubernetes API server) services.
It can also be used to make calls to the Kubernetes API server in such cases:</p>
<ol>
<li>A POST is needed to create a resource.</li>
<li>Finer-grained control is needed. Example: To restrict the number of resources cached.</li>
</ol>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>projections</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2alpha1-GlobalContextEntryProjection">
                <span style="font-family: monospace">[]GlobalContextEntryProjection</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Projections defines the list of JMESPath expressions to extract values from the cached resource.</p>


          

          
        </td>
      </tr>
    
  

            </table>
          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>status</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2alpha1-GlobalContextEntryStatus">
                <span style="font-family: monospace">GlobalContextEntryStatus</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Status contains globalcontextentry runtime data.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2alpha1-ExternalAPICall">ExternalAPICall
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2alpha1-GlobalContextEntrySpec">GlobalContextEntrySpec</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>APICall</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-APICall">
                <span style="font-family: monospace">APICall</span>
              </a>
            
          
        </td>
        <td>
          
            <p>(Members of <code>APICall</code> are embedded into this type.)</p>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>refreshInterval</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.Duration</span>
            
          
        </td>
        <td>
          

          <p>RefreshInterval defines the interval in duration at which to poll the APICall.
The duration is a sequence of decimal numbers, each with optional fraction and a unit suffix,
such as &quot;300ms&quot;, &quot;1.5h&quot; or &quot;2h45m&quot;. Valid time units are &quot;ns&quot;, &quot;us&quot; (or &quot;µs&quot;), &quot;ms&quot;, &quot;s&quot;, &quot;m&quot;, &quot;h&quot;.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>retryLimit</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">int</span>
            
          
        </td>
        <td>
          

          <p>RetryLimit defines the number of times the APICall should be retried in case of failure.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2alpha1-GlobalContextEntryProjection">GlobalContextEntryProjection
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2alpha1-GlobalContextEntrySpec">GlobalContextEntrySpec</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>name</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Name is the name to use for the extracted value in the context.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>jmesPath</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>JMESPath is the JMESPath expression to extract the value from the cached resource.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2alpha1-GlobalContextEntrySpec">GlobalContextEntrySpec
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2alpha1-GlobalContextEntry">GlobalContextEntry</a>)
    </p>
  

  <p><p>GlobalContextEntrySpec stores policy exception spec</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>kubernetesResource</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2alpha1-KubernetesResource">
                <span style="font-family: monospace">KubernetesResource</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Stores a list of Kubernetes resources which will be cached.
Mutually exclusive with APICall.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>apiCall</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2alpha1-ExternalAPICall">
                <span style="font-family: monospace">ExternalAPICall</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Stores results from an API call which will be cached.
Mutually exclusive with KubernetesResource.
This can be used to make calls to external (non-Kubernetes API server) services.
It can also be used to make calls to the Kubernetes API server in such cases:</p>
<ol>
<li>A POST is needed to create a resource.</li>
<li>Finer-grained control is needed. Example: To restrict the number of resources cached.</li>
</ol>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>projections</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v2alpha1-GlobalContextEntryProjection">
                <span style="font-family: monospace">[]GlobalContextEntryProjection</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Projections defines the list of JMESPath expressions to extract values from the cached resource.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2alpha1-GlobalContextEntryStatus">GlobalContextEntryStatus
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2alpha1-GlobalContextEntry">GlobalContextEntry</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>ready</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated in favor of Conditions</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>conditions</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]meta/v1.Condition</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>lastRefreshTime</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.Time</span>
            
          
        </td>
        <td>
          

          <p>Indicates the time when the globalcontextentry was last refreshed successfully for the API Call</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v2alpha1-KubernetesResource">KubernetesResource
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v2alpha1-GlobalContextEntrySpec">GlobalContextEntrySpec</a>)
    </p>
  

  <p><p>KubernetesResource stores infos about kubernetes resource that should be cached</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>group</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Group defines the group of the resource.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>version</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Version defines the version of the resource.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>resource</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Resource defines the type of the resource.
Requires the pluralized form of the resource kind in lowercase. (Ex., &quot;deployments&quot;)</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>namespace</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Namespace defines the namespace of the resource. Leave empty for cluster scoped resources.
If left empty for namespaced resources, all resources from all namespaces will be cached.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

          
          <hr />
        
      </div>
    </body>
  </html>

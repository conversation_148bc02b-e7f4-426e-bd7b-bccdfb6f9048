
  <html lang="en">
    <head>
      <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
<style>
    .bg-blue {
        color: #ffffff;
        background-color: #1589dd;
    }
</style>
    </head>
    <body>
      <div class="container">
        
          
          
            <h2 id="kyverno-io-v1">Package: <span style="font-family: monospace">kyverno.io/v1</span></h2>
            <p></p>
          
        
        
          
            
            <h3>Resource Types:</h3>
            <ul><li>
                    <a href="#kyverno-io-v1-ClusterPolicy">ClusterPolicy</a>
                  </li><li>
                    <a href="#kyverno-io-v1-Policy">Policy</a>
                  </li></ul>

            
            
  <H3 id="kyverno-io-v1-ClusterPolicy">ClusterPolicy
    </H3>

  

  <p><p>ClusterPolicy declares validation, mutation, and generation behaviors for matching resources.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        
          
          <tr>
            <td><code>apiVersion</code></br>string</td>
            <td><code>kyverno.io/v1</code></td>
          </tr>
          <tr>
            <td><code>kind</code></br>string</td>
            <td><code>ClusterPolicy</code></td>
          </tr>
        

        
        

  
  
    
    
  
    
    
      <tr>
        <td><code>metadata</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.ObjectMeta</span>
            
          
        </td>
        <td>
          

          

          
            Refer to the Kubernetes API documentation for the fields of the
            <code>metadata</code> field.
          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>spec</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Spec">
                <span style="font-family: monospace">Spec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Spec declares policy behaviors.</p>


          

          
            <br/>
            <br/>
            <table>
              

  
  
    
    
      <tr>
        <td><code>rules</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Rule">
                <span style="font-family: monospace">[]Rule</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Rules is a list of Rule instances. A Policy contains multiple rules and
each rule can validate, mutate, or generate resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>applyRules</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ApplyRulesType">
                <span style="font-family: monospace">ApplyRulesType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ApplyRules controls how rules in a policy are applied. Rule are processed in
the order of declaration. When set to <code>One</code> processing stops after a rule has
been applied i.e. the rule matches and results in a pass, fail, or error. When
set to <code>All</code> all rules in the policy are processed. The default is <code>All</code>.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>failurePolicy</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-FailurePolicyType">
                <span style="font-family: monospace">FailurePolicyType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deprecated, use failurePolicy under the webhookConfiguration instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>validationFailureAction</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidationFailureAction">
                <span style="font-family: monospace">ValidationFailureAction</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deprecated, use validationFailureAction under the validate rule instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>validationFailureActionOverrides</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidationFailureActionOverride">
                <span style="font-family: monospace">[]ValidationFailureActionOverride</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deprecated, use validationFailureActionOverrides under the validate rule instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>emitWarning</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>EmitWarning enables API response warnings for mutate policy rules or validate policy rules with validationFailureAction set to Audit.
Enabling this option will extend admission request processing times. The default value is &quot;false&quot;.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>admission</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Admission controls if rules are applied during admission.
Optional. Default value is &quot;true&quot;.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>background</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Background controls if rules are applied to existing resources during a background scan.
Optional. Default value is &quot;true&quot;. The value must be set to &quot;false&quot; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>schemaValidation</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>webhookTimeoutSeconds</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">int32</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use webhookTimeoutSeconds under webhookConfiguration instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>mutateExistingOnPolicyUpdate</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use mutateExistingOnPolicyUpdate under the mutate rule instead</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>generateExistingOnPolicyUpdate</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use generateExisting instead</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>generateExisting</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use generateExisting under the generate rule instead</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>useServerSideApply</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>UseServerSideApply controls whether to use server-side apply for generate rules
If is set to &quot;true&quot; create &amp; update for generate rules will use apply instead of create/update.
Defaults to &quot;false&quot; if not specified.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>webhookConfiguration</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-WebhookConfiguration">
                <span style="font-family: monospace">WebhookConfiguration</span>
              </a>
            
          
        </td>
        <td>
          

          <p>WebhookConfiguration specifies the custom configuration for Kubernetes admission webhookconfiguration.</p>


          

          
        </td>
      </tr>
    
  

            </table>
          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>status</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-PolicyStatus">
                <span style="font-family: monospace">PolicyStatus</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Status contains policy runtime data.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Policy">Policy
    </H3>

  

  <p><p>Policy declares validation, mutation, and generation behaviors for matching resources.
See: https://kyverno.io/docs/writing-policies/ for more information.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        
          
          <tr>
            <td><code>apiVersion</code></br>string</td>
            <td><code>kyverno.io/v1</code></td>
          </tr>
          <tr>
            <td><code>kind</code></br>string</td>
            <td><code>Policy</code></td>
          </tr>
        

        
        

  
  
    
    
  
    
    
      <tr>
        <td><code>metadata</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.ObjectMeta</span>
            
          
        </td>
        <td>
          

          

          
            Refer to the Kubernetes API documentation for the fields of the
            <code>metadata</code> field.
          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>spec</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Spec">
                <span style="font-family: monospace">Spec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Spec defines policy behaviors and contains one or more rules.</p>


          

          
            <br/>
            <br/>
            <table>
              

  
  
    
    
      <tr>
        <td><code>rules</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Rule">
                <span style="font-family: monospace">[]Rule</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Rules is a list of Rule instances. A Policy contains multiple rules and
each rule can validate, mutate, or generate resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>applyRules</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ApplyRulesType">
                <span style="font-family: monospace">ApplyRulesType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ApplyRules controls how rules in a policy are applied. Rule are processed in
the order of declaration. When set to <code>One</code> processing stops after a rule has
been applied i.e. the rule matches and results in a pass, fail, or error. When
set to <code>All</code> all rules in the policy are processed. The default is <code>All</code>.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>failurePolicy</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-FailurePolicyType">
                <span style="font-family: monospace">FailurePolicyType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deprecated, use failurePolicy under the webhookConfiguration instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>validationFailureAction</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidationFailureAction">
                <span style="font-family: monospace">ValidationFailureAction</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deprecated, use validationFailureAction under the validate rule instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>validationFailureActionOverrides</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidationFailureActionOverride">
                <span style="font-family: monospace">[]ValidationFailureActionOverride</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deprecated, use validationFailureActionOverrides under the validate rule instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>emitWarning</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>EmitWarning enables API response warnings for mutate policy rules or validate policy rules with validationFailureAction set to Audit.
Enabling this option will extend admission request processing times. The default value is &quot;false&quot;.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>admission</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Admission controls if rules are applied during admission.
Optional. Default value is &quot;true&quot;.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>background</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Background controls if rules are applied to existing resources during a background scan.
Optional. Default value is &quot;true&quot;. The value must be set to &quot;false&quot; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>schemaValidation</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>webhookTimeoutSeconds</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">int32</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use webhookTimeoutSeconds under webhookConfiguration instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>mutateExistingOnPolicyUpdate</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use mutateExistingOnPolicyUpdate under the mutate rule instead</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>generateExistingOnPolicyUpdate</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use generateExisting instead</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>generateExisting</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use generateExisting under the generate rule instead</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>useServerSideApply</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>UseServerSideApply controls whether to use server-side apply for generate rules
If is set to &quot;true&quot; create &amp; update for generate rules will use apply instead of create/update.
Defaults to &quot;false&quot; if not specified.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>webhookConfiguration</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-WebhookConfiguration">
                <span style="font-family: monospace">WebhookConfiguration</span>
              </a>
            
          
        </td>
        <td>
          

          <p>WebhookConfiguration specifies the custom configuration for Kubernetes admission webhookconfiguration.</p>


          

          
        </td>
      </tr>
    
  

            </table>
          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>status</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-PolicyStatus">
                <span style="font-family: monospace">PolicyStatus</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deprecated. Policy metrics are available via the metrics endpoint</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-APICall">APICall
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ContextAPICall">ContextAPICall</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>urlPath</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>URLPath is the URL path to be used in the HTTP GET or POST request to the
Kubernetes API server (e.g. &quot;/api/v1/namespaces&quot; or  &quot;/apis/apps/v1/deployments&quot;).
The format required is the same format used by the <code>kubectl get --raw</code> command.
See https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls
for details.
It's mutually exclusive with the Service field.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>method</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Method">
                <span style="font-family: monospace">Method</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Method is the HTTP request type (GET or POST). Defaults to GET.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>data</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-RequestData">
                <span style="font-family: monospace">[]RequestData</span>
              </a>
            
          
        </td>
        <td>
          

          <p>The data object specifies the POST data sent to the server.
Only applicable when the method field is set to POST.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>service</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ServiceCall">
                <span style="font-family: monospace">ServiceCall</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Service is an API call to a JSON web service.
This is used for non-Kubernetes API server calls.
It's mutually exclusive with the URLPath field.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-AdmissionOperation">AdmissionOperation
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ResourceDescription">ResourceDescription</a>)
    </p>
  

  <p><p>AdmissionOperation can have one of the values CREATE, UPDATE, CONNECT, DELETE, which are used to match a specific action.</p>
</p>

  

  <H3 id="kyverno-io-v1-AnyAllConditions">AnyAllConditions
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Attestation">Attestation</a>, 
        <a href="#kyverno-io-v1-ForEachGeneration">ForEachGeneration</a>, 
        <a href="#kyverno-io-v1-ForEachMutation">ForEachMutation</a>, 
        <a href="#kyverno-io-v1-ForEachValidation">ForEachValidation</a>)
    </p>
  

  <p><p>AnyAllConditions consists of conditions wrapped denoting a logical criteria to be fulfilled.
AnyConditions get fulfilled when at least one of its sub-conditions passes.
AllConditions get fulfilled only when all of its sub-conditions pass.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>any</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Condition">
                <span style="font-family: monospace">[]Condition</span>
              </a>
            
          
        </td>
        <td>
          

          <p>AnyConditions enable variable-based conditional rule execution. This is useful for
finer control of when an rule is applied. A condition can reference object data
using JMESPath notation.
Here, at least one of the conditions need to pass</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>all</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Condition">
                <span style="font-family: monospace">[]Condition</span>
              </a>
            
          
        </td>
        <td>
          

          <p>AllConditions enable variable-based conditional rule execution. This is useful for
finer control of when an rule is applied. A condition can reference object data
using JMESPath notation.
Here, all of the conditions need to pass</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ApplyRulesType">ApplyRulesType
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Spec">Spec</a>)
    </p>
  

  <p><p>ApplyRulesType controls whether processing stops after one rule is applied or all rules are applied.</p>
</p>

  

  <H3 id="kyverno-io-v1-AssertionTree">AssertionTree
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Validation">Validation</a>)
    </p>
  

  <p><p>AssertionTree defines a kyverno-json assertion tree.</p>
</p>

  

  <H3 id="kyverno-io-v1-Attestation">Attestation
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ImageVerification">ImageVerification</a>)
    </p>
  

  <p><p>Attestation are checks for signed in-toto Statements that are used to verify the image.
See https://github.com/in-toto/attestation. Kyverno fetches signed attestations from the
OCI registry and decodes them into a list of Statements.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>name</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Name is the variable name.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>predicateType</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Deprecated in favour of 'Type', to be removed soon</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>type</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Type defines the type of attestation contained within the Statement.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>attestors</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-AttestorSet">
                <span style="font-family: monospace">[]AttestorSet</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Attestors specify the required attestors (i.e. authorities).</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>conditions</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-AnyAllConditions">
                <span style="font-family: monospace">[]AnyAllConditions</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Conditions are used to verify attributes within a Predicate. If no Conditions are specified
the attestation check is satisfied as long there are predicates that match the predicate type.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Attestor">Attestor
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-AttestorSet">AttestorSet</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>keys</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-StaticKeyAttestor">
                <span style="font-family: monospace">StaticKeyAttestor</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Keys specifies one or more public keys.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>certificates</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-CertificateAttestor">
                <span style="font-family: monospace">CertificateAttestor</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Certificates specifies one or more certificates.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>keyless</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-KeylessAttestor">
                <span style="font-family: monospace">KeylessAttestor</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Keyless is a set of attribute used to verify a Sigstore keyless attestor.
See https://github.com/sigstore/cosign/blob/main/KEYLESS.md.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>attestor</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>Attestor is a nested set of Attestor used to specify a more complex set of match authorities.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>annotations</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">map[string]string</span>
            
          
        </td>
        <td>
          

          <p>Annotations are used for image verification.
Every specified key-value pair must exist and match in the verified payload.
The payload may contain other key-value pairs.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>repository</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Repository is an optional alternate OCI repository to use for signatures and attestations that match this rule.
If specified Repository will override other OCI image repository locations for this Attestor.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>signatureAlgorithm</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Specify signature algorithm for public keys. Supported values are sha224, sha256, sha384 and sha512.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-AttestorSet">AttestorSet
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Attestation">Attestation</a>, 
        <a href="#kyverno-io-v1-ImageVerification">ImageVerification</a>, 
        <a href="#kyverno-io-v1-Manifests">Manifests</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>count</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">int</span>
            
          
        </td>
        <td>
          

          <p>Count specifies the required number of entries that must match. If the count is null, all entries must match
(a logical AND). If the count is 1, at least one entry must match (a logical OR). If the count contains a
value N, then N must be less than or equal to the size of entries, and at least N entries must match.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>entries</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Attestor">
                <span style="font-family: monospace">[]Attestor</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Entries contains the available attestors. An attestor can be a static key,
attributes for keyless verification, or a nested attestor declaration.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-AutogenStatus">AutogenStatus
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-PolicyStatus">PolicyStatus</a>)
    </p>
  

  <p><p>AutogenStatus contains autogen status information.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>rules</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Rule">
                <span style="font-family: monospace">[]Rule</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Rules is a list of Rule instances. It contains auto generated rules added for pod controllers</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-CEL">CEL
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Validation">Validation</a>)
    </p>
  

  <p><p>CEL allows validation checks using the Common Expression Language (https://kubernetes.io/docs/reference/using-api/cel/).</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>generate</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Generate specifies whether to generate a Kubernetes ValidatingAdmissionPolicy from the rule.
Optional. Defaults to &quot;false&quot; if not specified.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>expressions</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]admissionregistration/v1.Validation</span>
            
          
        </td>
        <td>
          

          <p>Expressions is a list of CELExpression types.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>paramKind</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">admissionregistration/v1.ParamKind</span>
            
          
        </td>
        <td>
          

          <p>ParamKind is a tuple of Group Kind and Version.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>paramRef</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">admissionregistration/v1.ParamRef</span>
            
          
        </td>
        <td>
          

          <p>ParamRef references a parameter resource.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>auditAnnotations</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]admissionregistration/v1.AuditAnnotation</span>
            
          
        </td>
        <td>
          

          <p>AuditAnnotations contains CEL expressions which are used to produce audit annotations for the audit event of the API request.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>variables</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]admissionregistration/v1.Variable</span>
            
          
        </td>
        <td>
          

          <p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.
The variables defined here will be available under <code>variables</code> in other expressions of the policy.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-CTLog">CTLog
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-CertificateAttestor">CertificateAttestor</a>, 
        <a href="#kyverno-io-v1-KeylessAttestor">KeylessAttestor</a>, 
        <a href="#kyverno-io-v1-StaticKeyAttestor">StaticKeyAttestor</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>ignoreSCT</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate
timestamp. Default is false. Set to true if this was opted out during signing.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>pubkey</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>PubKey, if set, is used to validate SCTs against a custom source.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>tsaCertChain</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must
contain the root CA certificate. Optionally may contain intermediate CA certificates, and
may contain the leaf TSA certificate if not present in the timestamurce.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-CertificateAttestor">CertificateAttestor
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Attestor">Attestor</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>cert</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Cert is an optional PEM-encoded public certificate.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>certChain</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>CertChain is an optional PEM encoded set of certificates used to verify.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>rekor</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Rekor">
                <span style="font-family: monospace">Rekor</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Rekor provides configuration for the Rekor transparency log service. If an empty object
is provided the public instance of Rekor (https://rekor.sigstore.dev) is used.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>ctlog</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-CTLog">
                <span style="font-family: monospace">CTLog</span>
              </a>
            
          
        </td>
        <td>
          

          <p>CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate
Timestamps (SCTs). If the value is unset, the default behavior by Cosign is used.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-CloneFrom">CloneFrom
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-GeneratePattern">GeneratePattern</a>)
    </p>
  

  <p><p>CloneFrom provides the location of the source resource used to generate target resources.
The resource kind is derived from the match criteria.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>namespace</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Namespace specifies source resource namespace.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>name</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Name specifies name of the resource.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Condition">Condition
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-AnyAllConditions">AnyAllConditions</a>)
    </p>
  

  <p><p>Condition defines variable-based conditional criteria for rule execution.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>key</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>Key is the context entry (using JMESPath) for conditional rule evaluation.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>operator</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ConditionOperator">
                <span style="font-family: monospace">ConditionOperator</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Operator is the conditional operation to perform. Valid operators are:
Equals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,
GreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,
DurationLessThanOrEquals, DurationLessThan</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>value</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>Value is the conditional value, or set of values. The values can be fixed set
or can be variables declared using JMESPath.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>message</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Message is an optional display message</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ConditionOperator">ConditionOperator
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Condition">Condition</a>)
    </p>
  

  <p><p>ConditionOperator is the operation performed on condition key and value.</p>
</p>

  

  <H3 id="kyverno-io-v1-ConditionsWrapper">ConditionsWrapper
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Deny">Deny</a>, 
        <a href="#kyverno-io-v1-Rule">Rule</a>, 
        <a href="#kyverno-io-v1-TargetResourceSpec">TargetResourceSpec</a>)
    </p>
  

  <p><p>ConditionsWrapper contains either the deprecated list of Conditions or the new AnyAll Conditions.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>-</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">any</span>
            
          
        </td>
        <td>
          

          <p>Conditions is a list of conditions that must be satisfied for the rule to be applied.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ConfigMapReference">ConfigMapReference
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ContextEntry">ContextEntry</a>)
    </p>
  

  <p><p>ConfigMapReference refers to a ConfigMap</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>name</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Name is the ConfigMap name.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>namespace</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Namespace is the ConfigMap namespace.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ContextAPICall">ContextAPICall
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ContextEntry">ContextEntry</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>APICall</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-APICall">
                <span style="font-family: monospace">APICall</span>
              </a>
            
          
        </td>
        <td>
          
            <p>(Members of <code>APICall</code> are embedded into this type.)</p>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>default</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>Default is an optional arbitrary JSON object that the context
value is set to, if the apiCall returns error.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>jmesPath</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>JMESPath is an optional JSON Match Expression that can be used to
transform the JSON response returned from the server. For example
a JMESPath of &quot;items | length(@)&quot; applied to the API server response
for the URLPath &quot;/apis/apps/v1/deployments&quot; will return the total count
of deployments across all namespaces.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ContextEntry">ContextEntry
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ForEachGeneration">ForEachGeneration</a>, 
        <a href="#kyverno-io-v1-ForEachMutation">ForEachMutation</a>, 
        <a href="#kyverno-io-v1-ForEachValidation">ForEachValidation</a>, 
        <a href="#kyverno-io-v1-Rule">Rule</a>, 
        <a href="#kyverno-io-v1-TargetResourceSpec">TargetResourceSpec</a>)
    </p>
  

  <p><p>ContextEntry adds variables and data sources to a rule Context. Either a
ConfigMap reference or a APILookup must be provided.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>name</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Name is the variable name.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>configMap</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ConfigMapReference">
                <span style="font-family: monospace">ConfigMapReference</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ConfigMap is the ConfigMap reference.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>apiCall</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ContextAPICall">
                <span style="font-family: monospace">ContextAPICall</span>
              </a>
            
          
        </td>
        <td>
          

          <p>APICall is an HTTP request to the Kubernetes API server, or other JSON web service.
The data returned is stored in the context with the name for the context entry.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>imageRegistry</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ImageRegistry">
                <span style="font-family: monospace">ImageRegistry</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image
details.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>variable</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Variable">
                <span style="font-family: monospace">Variable</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Variable defines an arbitrary JMESPath context variable that can be defined inline.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>globalReference</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-GlobalContextEntryReference">
                <span style="font-family: monospace">GlobalContextEntryReference</span>
              </a>
            
          
        </td>
        <td>
          

          <p>GlobalContextEntryReference is a reference to a cached global context entry.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Deny">Deny
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ForEachValidation">ForEachValidation</a>, 
        <a href="#kyverno-io-v1-ValidateImageVerification">ValidateImageVerification</a>, 
        <a href="#kyverno-io-v1-Validation">Validation</a>)
    </p>
  

  <p><p>Deny specifies a list of conditions used to pass or fail a validation rule.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>conditions</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ConditionsWrapper">
                <span style="font-family: monospace">ConditionsWrapper</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Multiple conditions can be declared under an <code>any</code> or <code>all</code> statement. A direct list
of conditions (without <code>any</code> or <code>all</code> statements) is also supported for backwards compatibility
but will be deprecated in the next major release.
See: https://kyverno.io/docs/writing-policies/validate/#deny-rules</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-DryRunOption">DryRunOption
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Manifests">Manifests</a>)
    </p>
  

  <p><p>DryRunOption is a configuration for dryrun.
If enable is set to &quot;true&quot;, manifest verification performs &quot;dryrun &amp; compare&quot;
which provides robust matching against changes by defaults and other admission controllers.
Dryrun requires additional permissions. See config/dryrun/dryrun_rbac.yaml</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>enable</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>namespace</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-FailurePolicyType">FailurePolicyType
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Spec">Spec</a>, 
        <a href="#kyverno-io-v1-WebhookConfiguration">WebhookConfiguration</a>)
    </p>
  

  <p><p>FailurePolicyType specifies a failure policy that defines how unrecognized errors from the admission endpoint are handled.</p>
</p>

  

  <H3 id="kyverno-io-v1-ForEachGeneration">ForEachGeneration
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Generation">Generation</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>list</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>List specifies a JMESPath expression that results in one or more elements
to which the validation logic is applied.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>context</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ContextEntry">
                <span style="font-family: monospace">[]ContextEntry</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Context defines variables and data sources that can be used during rule execution.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>preconditions</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-AnyAllConditions">
                <span style="font-family: monospace">AnyAllConditions</span>
              </a>
            
          
        </td>
        <td>
          

          <p>AnyAllConditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.
See: https://kyverno.io/docs/writing-policies/preconditions/</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>GeneratePattern</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-GeneratePattern">
                <span style="font-family: monospace">GeneratePattern</span>
              </a>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ForEachMutation">ForEachMutation
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ForEachMutationWrapper">ForEachMutationWrapper</a>, 
        <a href="#kyverno-io-v1-Mutation">Mutation</a>)
    </p>
  

  <p><p>ForEachMutation applies mutation rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>list</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>List specifies a JMESPath expression that results in one or more elements
to which the validation logic is applied.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>order</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ForeachOrder">
                <span style="font-family: monospace">ForeachOrder</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Order defines the iteration order on the list.
Can be Ascending to iterate from first to last element or Descending to iterate in from last to first element.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>context</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ContextEntry">
                <span style="font-family: monospace">[]ContextEntry</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Context defines variables and data sources that can be used during rule execution.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>preconditions</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-AnyAllConditions">
                <span style="font-family: monospace">AnyAllConditions</span>
              </a>
            
          
        </td>
        <td>
          

          <p>AnyAllConditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.
See: https://kyverno.io/docs/writing-policies/preconditions/</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>patchStrategicMerge</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">github.com/kyverno/kyverno/api/kyverno.Any</span>
            
          
        </td>
        <td>
          

          <p>PatchStrategicMerge is a strategic merge patch used to modify resources.
See https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch/
and https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesstrategicmerge/.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>patchesJson6902</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>PatchesJSON6902 is a list of RFC 6902 JSON Patch declarations used to modify resources.
See https://tools.ietf.org/html/rfc6902 and https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesjson6902/.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>foreach</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ForEachMutationWrapper">
                <span style="font-family: monospace">ForEachMutationWrapper</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Foreach declares a nested foreach iterator</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ForEachMutationWrapper">ForEachMutationWrapper
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ForEachMutation">ForEachMutation</a>)
    </p>
  

  <p><p>ForEachMutationWrapper contains a list of ForEach descriptors.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>-</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ForEachMutation">
                <span style="font-family: monospace">[]ForEachMutation</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Item is a descriptor on how to iterate over the list of items.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ForEachValidation">ForEachValidation
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ForEachValidationWrapper">ForEachValidationWrapper</a>, 
        <a href="#kyverno-io-v1-Validation">Validation</a>)
    </p>
  

  <p><p>ForEachValidation applies validate rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>list</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>List specifies a JMESPath expression that results in one or more elements
to which the validation logic is applied.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>elementScope</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>ElementScope specifies whether to use the current list element as the scope for validation. Defaults to &quot;true&quot; if not specified.
When set to &quot;false&quot;, &quot;request.object&quot; is used as the validation scope within the foreach
block to allow referencing other elements in the subtree.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>context</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ContextEntry">
                <span style="font-family: monospace">[]ContextEntry</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Context defines variables and data sources that can be used during rule execution.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>preconditions</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-AnyAllConditions">
                <span style="font-family: monospace">AnyAllConditions</span>
              </a>
            
          
        </td>
        <td>
          

          <p>AnyAllConditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.
See: https://kyverno.io/docs/writing-policies/preconditions/</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>pattern</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>Pattern specifies an overlay-style pattern used to check resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>anyPattern</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>AnyPattern specifies list of validation patterns. At least one of the patterns
must be satisfied for the validation rule to succeed.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>deny</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Deny">
                <span style="font-family: monospace">Deny</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deny defines conditions used to pass or fail a validation rule.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>foreach</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ForEachValidationWrapper">
                <span style="font-family: monospace">ForEachValidationWrapper</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Foreach declares a nested foreach iterator</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ForEachValidationWrapper">ForEachValidationWrapper
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ForEachValidation">ForEachValidation</a>)
    </p>
  

  <p><p>ForEachValidationWrapper contains a list of ForEach descriptors.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>-</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ForEachValidation">
                <span style="font-family: monospace">[]ForEachValidation</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Item is a descriptor on how to iterate over the list of items.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ForeachOrder">ForeachOrder
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ForEachMutation">ForEachMutation</a>)
    </p>
  

  <p><p>ForeachOrder specifies the iteration order in foreach statements.</p>
</p>

  

  <H3 id="kyverno-io-v1-GeneratePattern">GeneratePattern
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ForEachGeneration">ForEachGeneration</a>, 
        <a href="#kyverno-io-v1-Generation">Generation</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>ResourceSpec</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ResourceSpec">
                <span style="font-family: monospace">ResourceSpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ResourceSpec contains information to select the resource.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>data</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>Data provides the resource declaration used to populate each generated resource.
At most one of Data or Clone must be specified. If neither are provided, the generated
resource will be created with default data only.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>clone</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-CloneFrom">
                <span style="font-family: monospace">CloneFrom</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Clone specifies the source resource used to populate each generated resource.
At most one of Data or Clone can be specified. If neither are provided, the generated
resource will be created with default data only.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>cloneList</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-CloneList">
                <span style="font-family: monospace">CloneList</span>
              </a>
            
          
        </td>
        <td>
          

          <p>CloneList specifies the list of source resource used to populate each generated resource.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Generation">Generation
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Rule">Rule</a>)
    </p>
  

  <p><p>Generation defines how new resources should be created and managed.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>generateExisting</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>GenerateExisting controls whether to trigger the rule in existing resources
If is set to &quot;true&quot; the rule will be triggered and applied to existing matched resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>synchronize</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Synchronize controls if generated resources should be kept in-sync with their source resource.
If Synchronize is set to &quot;true&quot; changes to generated resources will be overwritten with resource
data from Data or the resource specified in the Clone declaration.
Optional. Defaults to &quot;false&quot; if not specified.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>orphanDownstreamOnPolicyDelete</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>OrphanDownstreamOnPolicyDelete controls whether generated resources should be deleted when the rule that generated
them is deleted with synchronization enabled. This option is only applicable to generate rules of the data type.
See https://kyverno.io/docs/writing-policies/generate/#data-examples.
Defaults to &quot;false&quot; if not specified.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>GeneratePattern</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-GeneratePattern">
                <span style="font-family: monospace">GeneratePattern</span>
              </a>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>foreach</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ForEachGeneration">
                <span style="font-family: monospace">[]ForEachGeneration</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ForEach applies generate rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-GlobalContextEntryReference">GlobalContextEntryReference
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ContextEntry">ContextEntry</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>name</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Name of the global context entry</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>jmesPath</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>JMESPath is an optional JSON Match Expression that can be used to
transform the JSON response returned from the server. For example
a JMESPath of &quot;items | length(@)&quot; applied to the API server response
for the URLPath &quot;/apis/apps/v1/deployments&quot; will return the total count
of deployments across all namespaces.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-HTTPHeader">HTTPHeader
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ServiceCall">ServiceCall</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>key</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Key is the header key</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>value</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Value is the header value</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ImageExtractorConfigs">ImageExtractorConfigs
    (<code>map[string][]github.com/kyverno/kyverno/api/kyverno/v1.ImageExtractorConfig</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Rule">Rule</a>)
    </p>
  

  <p></p>

  

  <H3 id="kyverno-io-v1-ImageRegistry">ImageRegistry
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ContextEntry">ContextEntry</a>)
    </p>
  

  <p><p>ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image
details.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>reference</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Reference is image reference to a container image in the registry.
Example: ghcr.io/kyverno/kyverno:latest</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>jmesPath</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>JMESPath is an optional JSON Match Expression that can be used to
transform the ImageData struct returned as a result of processing
the image reference.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>imageRegistryCredentials</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ImageRegistryCredentials">
                <span style="font-family: monospace">ImageRegistryCredentials</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ImageRegistryCredentials provides credentials that will be used for authentication with registry</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ImageRegistryCredentials">ImageRegistryCredentials
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ImageRegistry">ImageRegistry</a>, 
        <a href="#kyverno-io-v1-ImageVerification">ImageVerification</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>allowInsecureRegistry</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>AllowInsecureRegistry allows insecure access to a registry.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>providers</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ImageRegistryCredentialsProvidersType">
                <span style="font-family: monospace">[]ImageRegistryCredentialsProvidersType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Providers specifies a list of OCI Registry names, whose authentication providers are provided.
It can be of one of these values: default,google,azure,amazon,github.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>secrets</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>Secrets specifies a list of secrets that are provided for credentials.
Secrets must live in the Kyverno namespace.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ImageRegistryCredentialsProvidersType">ImageRegistryCredentialsProvidersType
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ImageRegistryCredentials">ImageRegistryCredentials</a>)
    </p>
  

  <p><p>ImageRegistryCredentialsProvidersType provides the list of credential providers required.</p>
</p>

  

  <H3 id="kyverno-io-v1-ImageVerification">ImageVerification
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Rule">Rule</a>)
    </p>
  

  <p><p>ImageVerification validates that images that match the specified pattern
are signed with the supplied public key. Once the image is verified it is
mutated to include the SHA digest retrieved during the registration.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>failureAction</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidationFailureAction">
                <span style="font-family: monospace">ValidationFailureAction</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Allowed values are Audit or Enforce.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>type</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ImageVerificationType">
                <span style="font-family: monospace">ImageVerificationType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Type specifies the method of signature validation. The allowed options
are Cosign, Sigstore Bundle and Notary. By default Cosign is used if a type is not specified.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>image</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Deprecated. Use ImageReferences instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>imageReferences</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>ImageReferences is a list of matching image reference patterns. At least one pattern in the
list must match the image for the rule to apply. Each image reference consists of a registry
address (defaults to docker.io), repository, image, and tag (defaults to latest).
Wildcards ('*' and '?') are allowed. See: https://kubernetes.io/docs/concepts/containers/images.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>skipImageReferences</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>SkipImageReferences is a list of matching image reference patterns that should be skipped.
At least one pattern in the list must match the image for the rule to be skipped. Each image reference
consists of a registry address (defaults to docker.io), repository, image, and tag (defaults to latest).
Wildcards ('*' and '?') are allowed. See: https://kubernetes.io/docs/concepts/containers/images.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>key</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Deprecated. Use StaticKeyAttestor instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>roots</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Deprecated. Use KeylessAttestor instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>subject</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Deprecated. Use KeylessAttestor instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>issuer</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Deprecated. Use KeylessAttestor instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>additionalExtensions</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">map[string]string</span>
            
          
        </td>
        <td>
          

          <p>Deprecated.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>attestors</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-AttestorSet">
                <span style="font-family: monospace">[]AttestorSet</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Attestors specified the required attestors (i.e. authorities)</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>attestations</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Attestation">
                <span style="font-family: monospace">[]Attestation</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Attestations are optional checks for signed in-toto Statements used to verify the image.
See https://github.com/in-toto/attestation. Kyverno fetches signed attestations from the
OCI registry and decodes them into a list of Statement declarations.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>annotations</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">map[string]string</span>
            
          
        </td>
        <td>
          

          <p>Deprecated. Use annotations per Attestor instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>repository</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Repository is an optional alternate OCI repository to use for image signatures and attestations that match this rule.
If specified Repository will override the default OCI image repository configured for the installation.
The repository can also be overridden per Attestor or Attestation.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>cosignOCI11</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>CosignOCI11 enables the experimental OCI 1.1 behaviour in cosign image verification.
Defaults to false.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>mutateDigest</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>MutateDigest enables replacement of image tags with digests.
Defaults to true.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>verifyDigest</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>VerifyDigest validates that images have a digest.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>validate</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidateImageVerification">
                <span style="font-family: monospace">ValidateImageVerification</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Validation checks conditions across multiple image
verification attestations or context entries</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>required</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Required validates that images are verified i.e. have matched passed a signature or attestation check.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>imageRegistryCredentials</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ImageRegistryCredentials">
                <span style="font-family: monospace">ImageRegistryCredentials</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ImageRegistryCredentials provides credentials that will be used for authentication with registry.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>useCache</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>UseCache enables caching of image verify responses for this rule.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ImageVerificationType">ImageVerificationType
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ImageVerification">ImageVerification</a>)
    </p>
  

  <p><p>ImageVerificationType selects the type of verification algorithm</p>
</p>

  

  <H3 id="kyverno-io-v1-KeylessAttestor">KeylessAttestor
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Attestor">Attestor</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>rekor</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Rekor">
                <span style="font-family: monospace">Rekor</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Rekor provides configuration for the Rekor transparency log service. If an empty object
is provided the public instance of Rekor (https://rekor.sigstore.dev) is used.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>ctlog</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-CTLog">
                <span style="font-family: monospace">CTLog</span>
              </a>
            
          
        </td>
        <td>
          

          <p>CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate
Timestamps (SCTs). If the value is unset, the default behavior by Cosign is used.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>issuer</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Issuer is the certificate issuer used for keyless signing.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>issuerRegExp</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>IssuerRegExp is the regular expression to match certificate issuer used for keyless signing.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>subject</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Subject is the verified identity used for keyless signing, for example the email address.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>subjectRegExp</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>SubjectRegExp is the regular expression to match identity used for keyless signing, for example the email address.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>roots</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Roots is an optional set of PEM encoded trusted root certificates.
If not provided, the system roots are used.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>additionalExtensions</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">map[string]string</span>
            
          
        </td>
        <td>
          

          <p>AdditionalExtensions are certificate-extensions used for keyless signing.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Manifests">Manifests
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Validation">Validation</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>attestors</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-AttestorSet">
                <span style="font-family: monospace">[]AttestorSet</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Attestors specified the required attestors (i.e. authorities)</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>annotationDomain</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>AnnotationDomain is custom domain of annotation for message and signature. Default is &quot;cosign.sigstore.dev&quot;.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>ignoreFields</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-IgnoreFieldList">
                <span style="font-family: monospace">IgnoreFieldList</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Fields which will be ignored while comparing manifests.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>dryRun</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-DryRunOption">
                <span style="font-family: monospace">DryRunOption</span>
              </a>
            
          
        </td>
        <td>
          

          <p>DryRun configuration</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>repository</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Repository is an optional alternate OCI repository to use for resource bundle reference.
The repository can be overridden per Attestor or Attestation.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-MatchResources">MatchResources
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Rule">Rule</a>)
    </p>
  

  <p><p>MatchResources is used to specify resource and admission review request data for
which a policy rule is applicable.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>any</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ResourceFilters">
                <span style="font-family: monospace">ResourceFilters</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Any allows specifying resources which will be ORed</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>all</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ResourceFilters">
                <span style="font-family: monospace">ResourceFilters</span>
              </a>
            
          
        </td>
        <td>
          

          <p>All allows specifying resources which will be ANDed</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>UserInfo</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-UserInfo">
                <span style="font-family: monospace">UserInfo</span>
              </a>
            
          
        </td>
        <td>
          

          <p>UserInfo contains information about the user performing the operation.
Specifying UserInfo directly under match is being deprecated.
Please specify under &quot;any&quot; or &quot;all&quot; instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>resources</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ResourceDescription">
                <span style="font-family: monospace">ResourceDescription</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ResourceDescription contains information about the resource being created or modified.
Requires at least one tag to be specified when under MatchResources.
Specifying ResourceDescription directly under match is being deprecated.
Please specify under &quot;any&quot; or &quot;all&quot; instead.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Method">Method
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-APICall">APICall</a>)
    </p>
  

  <p><p>Method is a HTTP request type.</p>
</p>

  

  <H3 id="kyverno-io-v1-Mutation">Mutation
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Rule">Rule</a>)
    </p>
  

  <p><p>Mutation defines how resource are modified.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>mutateExistingOnPolicyUpdate</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>MutateExistingOnPolicyUpdate controls if the mutateExisting rule will be applied on policy events.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>targets</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-TargetResourceSpec">
                <span style="font-family: monospace">[]TargetResourceSpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Targets defines the target resources to be mutated.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>patchStrategicMerge</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>PatchStrategicMerge is a strategic merge patch used to modify resources.
See https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch/
and https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesstrategicmerge/.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>patchesJson6902</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>PatchesJSON6902 is a list of RFC 6902 JSON Patch declarations used to modify resources.
See https://tools.ietf.org/html/rfc6902 and https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesjson6902/.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>foreach</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ForEachMutation">
                <span style="font-family: monospace">[]ForEachMutation</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ForEach applies mutation rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-PodSecurity">PodSecurity
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Validation">Validation</a>)
    </p>
  

  <p><p>PodSecurity applies exemptions for Kubernetes Pod Security admission
by specifying exclusions for Pod Security Standards controls.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>level</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/pod-security-admission/api.Level</span>
            
          
        </td>
        <td>
          

          <p>Level defines the Pod Security Standard level to be applied to workloads.
Allowed values are privileged, baseline, and restricted.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>version</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Version defines the Pod Security Standard versions that Kubernetes supports.
Allowed values are v1.19, v1.20, v1.21, v1.22, v1.23, v1.24, v1.25, v1.26, v1.27, v1.28, v1.29, latest. Defaults to latest.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>exclude</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-PodSecurityStandard">
                <span style="font-family: monospace">[]PodSecurityStandard</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Exclude specifies the Pod Security Standard controls to be excluded.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-PodSecurityStandard">PodSecurityStandard
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-PodSecurity">PodSecurity</a>)
    </p>
  

  <p><p>PodSecurityStandard specifies the Pod Security Standard controls to be excluded.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>controlName</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>ControlName specifies the name of the Pod Security Standard control.
See: https://kubernetes.io/docs/concepts/security/pod-security-standards/</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>images</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>Images selects matching containers and applies the container level PSS.
Each image is the image name consisting of the registry address, repository, image, and tag.
Empty list matches no containers, PSS checks are applied at the pod level only.
Wildcards ('*' and '?') are allowed. See: https://kubernetes.io/docs/concepts/containers/images.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>restrictedField</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>RestrictedField selects the field for the given Pod Security Standard control.
When not set, all restricted fields for the control are selected.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>values</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>Values defines the allowed values that can be excluded.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-PolicyStatus">PolicyStatus
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ClusterPolicy">ClusterPolicy</a>, 
        <a href="#kyverno-io-v1-Policy">Policy</a>)
    </p>
  

  <p><p>Deprecated. Policy metrics are now available via the &quot;/metrics&quot; endpoint.
See: https://kyverno.io/docs/monitoring-kyverno-with-prometheus-metrics/</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>ready</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated in favor of Conditions</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>conditions</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]meta/v1.Condition</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>autogen</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-AutogenStatus">
                <span style="font-family: monospace">AutogenStatus</span>
              </a>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>rulecount</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-RuleCountStatus">
                <span style="font-family: monospace">RuleCountStatus</span>
              </a>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>validatingadmissionpolicy</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidatingAdmissionPolicyStatus">
                <span style="font-family: monospace">ValidatingAdmissionPolicyStatus</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ValidatingAdmissionPolicy contains status information</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Rekor">Rekor
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-CertificateAttestor">CertificateAttestor</a>, 
        <a href="#kyverno-io-v1-KeylessAttestor">KeylessAttestor</a>, 
        <a href="#kyverno-io-v1-StaticKeyAttestor">StaticKeyAttestor</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>url</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>URL is the address of the transparency log. Defaults to the public Rekor log instance https://rekor.sigstore.dev.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>pubkey</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.
If set, this will be used to validate transparency log signatures from a custom Rekor.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>ignoreTlog</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>IgnoreTlog skips transparency log verification.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-RequestData">RequestData
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-APICall">APICall</a>)
    </p>
  

  <p><p>RequestData contains the HTTP POST data</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>key</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Key is a unique identifier for the data value</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>value</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>Value is the data value</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ResourceDescription">ResourceDescription
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-MatchResources">MatchResources</a>)
    </p>
  

  <p><p>ResourceDescription contains criteria used to match resources.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>kinds</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>Kinds is a list of resource kinds.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>name</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Name is the name of the resource. The name supports wildcard characters
&quot;*&quot; (matches zero or many characters) and &quot;?&quot; (at least one character).
NOTE: &quot;Name&quot; is being deprecated in favor of &quot;Names&quot;.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>names</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>Names are the names of the resources. Each name supports wildcard characters
&quot;*&quot; (matches zero or many characters) and &quot;?&quot; (at least one character).</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>namespaces</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>Namespaces is a list of namespaces names. Each name supports wildcard characters
&quot;*&quot; (matches zero or many characters) and &quot;?&quot; (at least one character).</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>annotations</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">map[string]string</span>
            
          
        </td>
        <td>
          

          <p>Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
and values support the wildcard characters &quot;*&quot; (matches zero or many characters) and
&quot;?&quot; (matches at least one character).</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>selector</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.LabelSelector</span>
            
          
        </td>
        <td>
          

          <p>Selector is a label selector. Label keys and values in <code>matchLabels</code> support the wildcard
characters <code>*</code> (matches zero or many characters) and <code>?</code> (matches one character).
Wildcards allows writing label selectors like [&quot;storage.k8s.io/<em>&quot;: &quot;</em>&quot;]. Note that
using [&quot;<em>&quot; : &quot;</em>&quot;] matches any key and value but does not match an empty label set.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>namespaceSelector</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.LabelSelector</span>
            
          
        </td>
        <td>
          

          <p>NamespaceSelector is a label selector for the resource namespace. Label keys and values
in <code>matchLabels</code> support the wildcard characters <code>*</code> (matches zero or many characters)
and <code>?</code> (matches one character).Wildcards allows writing label selectors like
[&quot;storage.k8s.io/<em>&quot;: &quot;</em>&quot;]. Note that using [&quot;<em>&quot; : &quot;</em>&quot;] matches any key and value but
does not match an empty label set.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>operations</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-AdmissionOperation">
                <span style="font-family: monospace">[]AdmissionOperation</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Operations can contain values [&quot;CREATE, &quot;UPDATE&quot;, &quot;CONNECT&quot;, &quot;DELETE&quot;], which are used to match a specific action.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ResourceFilters">ResourceFilters
    (<code>[]github.com/kyverno/kyverno/api/kyverno/v1.ResourceFilter</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-MatchResources">MatchResources</a>)
    </p>
  

  <p><p>ResourceFilters is a slice of ResourceFilter</p>
</p>

  

  <H3 id="kyverno-io-v1-ResourceSpec">ResourceSpec
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-GeneratePattern">GeneratePattern</a>, 
        <a href="#kyverno-io-v1-TargetSelector">TargetSelector</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>apiVersion</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>APIVersion specifies resource apiVersion.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>kind</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Kind specifies resource kind.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>namespace</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Namespace specifies resource namespace.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>name</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Name specifies the resource name.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>uid</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apimachinery/pkg/types.UID</span>
            
          
        </td>
        <td>
          

          <p>UID specifies the resource uid.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Rule">Rule
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-AutogenStatus">AutogenStatus</a>, 
        <a href="#kyverno-io-v1-Spec">Spec</a>)
    </p>
  

  <p><p>Rule defines a validation, mutation, or generation control for matching resources.
Each rules contains a match declaration to select resources, and an optional exclude
declaration to specify which resources to exclude.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>name</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Name is a label to identify the rule, It must be unique within the policy.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>context</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ContextEntry">
                <span style="font-family: monospace">[]ContextEntry</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Context defines variables and data sources that can be used during rule execution.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>reportProperties</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">map[string]string</span>
            
          
        </td>
        <td>
          

          <p>ReportProperties are the additional properties from the rule that will be added to the policy report result</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>match</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-MatchResources">
                <span style="font-family: monospace">MatchResources</span>
              </a>
            
          
        </td>
        <td>
          

          <p>MatchResources defines when this policy rule should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>exclude</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-MatchResources">
                <span style="font-family: monospace">MatchResources</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ExcludeResources defines when this policy rule should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>imageExtractors</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ImageExtractorConfigs">
                <span style="font-family: monospace">ImageExtractorConfigs</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ImageExtractors defines a mapping from kinds to ImageExtractorConfigs.
This config is only valid for verifyImages rules.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>preconditions</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ConditionsWrapper">
                <span style="font-family: monospace">ConditionsWrapper</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Preconditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements. A direct list
of conditions (without <code>any</code> or <code>all</code> statements is supported for backwards compatibility but
will be deprecated in the next major release.
See: https://kyverno.io/docs/writing-policies/preconditions/</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>celPreconditions</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]admissionregistration/v1.MatchCondition</span>
            
          
        </td>
        <td>
          

          <p>CELPreconditions are used to determine if a policy rule should be applied by evaluating a
set of CEL conditions. It can only be used with the validate.cel subrule</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>mutate</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Mutation">
                <span style="font-family: monospace">Mutation</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Mutation is used to modify matching resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>validate</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Validation">
                <span style="font-family: monospace">Validation</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Validation is used to validate matching resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>generate</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Generation">
                <span style="font-family: monospace">Generation</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Generation is used to create new resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>verifyImages</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ImageVerification">
                <span style="font-family: monospace">[]ImageVerification</span>
              </a>
            
          
        </td>
        <td>
          

          <p>VerifyImages is used to verify image signatures and mutate them to add a digest</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>skipBackgroundRequests</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>SkipBackgroundRequests bypasses admission requests that are sent by the background controller.
The default value is set to &quot;true&quot;, it must be set to &quot;false&quot; to apply
generate and mutateExisting rules to those requests.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-RuleCountStatus">RuleCountStatus
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-PolicyStatus">PolicyStatus</a>)
    </p>
  

  <p><p>RuleCountStatus contains four variables which describes counts for
validate, generate, mutate and verify images rules</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>validate</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">int</span>
            
          
        </td>
        <td>
          

          <p>Count for validate rules in policy</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>generate</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">int</span>
            
          
        </td>
        <td>
          

          <p>Count for generate rules in policy</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>mutate</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">int</span>
            
          
        </td>
        <td>
          

          <p>Count for mutate rules in policy</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>verifyimages</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">int</span>
            
          
        </td>
        <td>
          

          <p>Count for verify image rules in policy</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-SecretReference">SecretReference
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-StaticKeyAttestor">StaticKeyAttestor</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>name</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Name of the secret. The provided secret must contain a key named cosign.pub.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>namespace</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Namespace name where the Secret exists.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ServiceCall">ServiceCall
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-APICall">APICall</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>url</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>URL is the JSON web service URL. A typical form is
<code>https://{service}.{namespace}:{port}/{path}</code>.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>headers</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-HTTPHeader">
                <span style="font-family: monospace">[]HTTPHeader</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Headers is a list of optional HTTP headers to be included in the request.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>caBundle</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>CABundle is a PEM encoded CA bundle which will be used to validate
the server certificate.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Spec">Spec
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ClusterPolicy">ClusterPolicy</a>, 
        <a href="#kyverno-io-v1-Policy">Policy</a>)
    </p>
  

  <p><p>Spec contains a list of Rule instances and other policy controls.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>rules</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Rule">
                <span style="font-family: monospace">[]Rule</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Rules is a list of Rule instances. A Policy contains multiple rules and
each rule can validate, mutate, or generate resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>applyRules</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ApplyRulesType">
                <span style="font-family: monospace">ApplyRulesType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ApplyRules controls how rules in a policy are applied. Rule are processed in
the order of declaration. When set to <code>One</code> processing stops after a rule has
been applied i.e. the rule matches and results in a pass, fail, or error. When
set to <code>All</code> all rules in the policy are processed. The default is <code>All</code>.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>failurePolicy</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-FailurePolicyType">
                <span style="font-family: monospace">FailurePolicyType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deprecated, use failurePolicy under the webhookConfiguration instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>validationFailureAction</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidationFailureAction">
                <span style="font-family: monospace">ValidationFailureAction</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deprecated, use validationFailureAction under the validate rule instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>validationFailureActionOverrides</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidationFailureActionOverride">
                <span style="font-family: monospace">[]ValidationFailureActionOverride</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deprecated, use validationFailureActionOverrides under the validate rule instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>emitWarning</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>EmitWarning enables API response warnings for mutate policy rules or validate policy rules with validationFailureAction set to Audit.
Enabling this option will extend admission request processing times. The default value is &quot;false&quot;.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>admission</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Admission controls if rules are applied during admission.
Optional. Default value is &quot;true&quot;.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>background</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Background controls if rules are applied to existing resources during a background scan.
Optional. Default value is &quot;true&quot;. The value must be set to &quot;false&quot; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>schemaValidation</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>webhookTimeoutSeconds</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">int32</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use webhookTimeoutSeconds under webhookConfiguration instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>mutateExistingOnPolicyUpdate</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use mutateExistingOnPolicyUpdate under the mutate rule instead</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>generateExistingOnPolicyUpdate</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use generateExisting instead</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>generateExisting</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Deprecated, use generateExisting under the generate rule instead</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>useServerSideApply</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>UseServerSideApply controls whether to use server-side apply for generate rules
If is set to &quot;true&quot; create &amp; update for generate rules will use apply instead of create/update.
Defaults to &quot;false&quot; if not specified.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>webhookConfiguration</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-WebhookConfiguration">
                <span style="font-family: monospace">WebhookConfiguration</span>
              </a>
            
          
        </td>
        <td>
          

          <p>WebhookConfiguration specifies the custom configuration for Kubernetes admission webhookconfiguration.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-StaticKeyAttestor">StaticKeyAttestor
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Attestor">Attestor</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>publicKeys</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Keys is a set of X.509 public keys used to verify image signatures. The keys can be directly
specified or can be a variable reference to a key specified in a ConfigMap (see
https://kyverno.io/docs/writing-policies/variables/), or reference a standard Kubernetes Secret
elsewhere in the cluster by specifying it in the format &quot;k8s://<!-- raw HTML omitted -->/&lt;secret_name&gt;&quot;.
The named Secret must specify a key <code>cosign.pub</code> containing the public key used for
verification, (see https://github.com/sigstore/cosign/blob/main/KMS.md#kubernetes-secret).
When multiple keys are specified each key is processed as a separate staticKey entry
(.attestors[*].entries.keys) within the set of attestors and the count is applied across the keys.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>signatureAlgorithm</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Deprecated. Use attestor.signatureAlgorithm instead.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>kms</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>KMS provides the URI to the public key stored in a Key Management System. See:
https://github.com/sigstore/cosign/blob/main/KMS.md</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>secret</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-SecretReference">
                <span style="font-family: monospace">SecretReference</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Reference to a Secret resource that contains a public key</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>rekor</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Rekor">
                <span style="font-family: monospace">Rekor</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Rekor provides configuration for the Rekor transparency log service. If an empty object
is provided the public instance of Rekor (https://rekor.sigstore.dev) is used.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>ctlog</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-CTLog">
                <span style="font-family: monospace">CTLog</span>
              </a>
            
          
        </td>
        <td>
          

          <p>CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate
Timestamps (SCTs). If the value is unset, the default behavior by Cosign is used.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-TargetResourceSpec">TargetResourceSpec
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Mutation">Mutation</a>)
    </p>
  

  <p><p>TargetResourceSpec defines targets for mutating existing resources.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>TargetSelector</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-TargetSelector">
                <span style="font-family: monospace">TargetSelector</span>
              </a>
            
          
        </td>
        <td>
          

          <p>TargetSelector contains the ResourceSpec and a label selector to support selecting with labels.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>context</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ContextEntry">
                <span style="font-family: monospace">[]ContextEntry</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Context defines variables and data sources that can be used during rule execution.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>preconditions</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ConditionsWrapper">
                <span style="font-family: monospace">ConditionsWrapper</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Preconditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements. A direct list
of conditions (without <code>any</code> or <code>all</code> statements is supported for backwards compatibility but
will be deprecated in the next major release.
See: https://kyverno.io/docs/writing-policies/preconditions/</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-TargetSelector">TargetSelector
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-TargetResourceSpec">TargetResourceSpec</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>ResourceSpec</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ResourceSpec">
                <span style="font-family: monospace">ResourceSpec</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ResourceSpec contains the target resources to load when mutating existing resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>selector</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.LabelSelector</span>
            
          
        </td>
        <td>
          

          <p>Selector allows you to select target resources with their labels.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-UserInfo">UserInfo
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-MatchResources">MatchResources</a>)
    </p>
  

  <p><p>UserInfo contains information about the user performing the operation.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>roles</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>Roles is the list of namespaced role names for the user.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>clusterRoles</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          <p>ClusterRoles is the list of cluster-wide role names for the user.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>subjects</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]rbac/v1.Subject</span>
            
          
        </td>
        <td>
          

          <p>Subjects is the list of subject names like users, user groups, and service accounts.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ValidateImageVerification">ValidateImageVerification
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ImageVerification">ImageVerification</a>)
    </p>
  

  <p><p>ValidateImageVerification checks conditions across multiple image
verification attestations or context entries</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>message</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Message specifies a custom message to be displayed on failure.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>deny</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Deny">
                <span style="font-family: monospace">Deny</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deny defines conditions used to pass or fail a validation rule.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ValidatingAdmissionPolicyStatus">ValidatingAdmissionPolicyStatus
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-PolicyStatus">PolicyStatus</a>)
    </p>
  

  <p><p>ValidatingAdmissionPolicy contains status information</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>generated</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>Generated indicates whether a validating admission policy is generated from the policy or not</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>message</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Message is a human readable message indicating details about the generation of validating admission policy
It is an empty string when validating admission policy is successfully generated.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Validation">Validation
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Rule">Rule</a>)
    </p>
  

  <p><p>Validation defines checks to be performed on matching resources.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>failureAction</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidationFailureAction">
                <span style="font-family: monospace">ValidationFailureAction</span>
              </a>
            
          
        </td>
        <td>
          

          <p>FailureAction defines if a validation policy rule violation should block
the admission review request (Enforce), or allow (Audit) the admission review request
and report an error in a policy report. Optional.
Allowed values are Audit or Enforce.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>failureActionOverrides</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidationFailureActionOverride">
                <span style="font-family: monospace">[]ValidationFailureActionOverride</span>
              </a>
            
          
        </td>
        <td>
          

          <p>FailureActionOverrides is a Cluster Policy attribute that specifies FailureAction
namespace-wise. It overrides FailureAction for the specified namespaces.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>allowExistingViolations</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">bool</span>
            
          
        </td>
        <td>
          

          <p>AllowExistingViolations allows prexisting violating resources to continue violating a policy.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>message</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>Message specifies a custom message to be displayed on failure.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>manifests</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Manifests">
                <span style="font-family: monospace">Manifests</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Manifest specifies conditions for manifest verification</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>foreach</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ForEachValidation">
                <span style="font-family: monospace">[]ForEachValidation</span>
              </a>
            
          
        </td>
        <td>
          

          <p>ForEach applies validate rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>pattern</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>Pattern specifies an overlay-style pattern used to check resources.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>anyPattern</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1.JSON</span>
            
          
        </td>
        <td>
          

          <p>AnyPattern specifies list of validation patterns. At least one of the patterns
must be satisfied for the validation rule to succeed.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>deny</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-Deny">
                <span style="font-family: monospace">Deny</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Deny defines conditions used to pass or fail a validation rule.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>podSecurity</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-PodSecurity">
                <span style="font-family: monospace">PodSecurity</span>
              </a>
            
          
        </td>
        <td>
          

          <p>PodSecurity applies exemptions for Kubernetes Pod Security admission
by specifying exclusions for Pod Security Standards controls.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>cel</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-CEL">
                <span style="font-family: monospace">CEL</span>
              </a>
            
          
        </td>
        <td>
          

          <p>CEL allows validation checks using the Common Expression Language (https://kubernetes.io/docs/reference/using-api/cel/).</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>assert</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-AssertionTree">
                <span style="font-family: monospace">AssertionTree</span>
              </a>
            
          
        </td>
        <td>
          

          <p>Assert defines a kyverno-json assertion tree.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-ValidationFailureAction">ValidationFailureAction
    (<code>string</code> alias)</p></H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ImageVerification">ImageVerification</a>, 
        <a href="#kyverno-io-v1-Spec">Spec</a>, 
        <a href="#kyverno-io-v1-Validation">Validation</a>, 
        <a href="#kyverno-io-v1-ValidationFailureActionOverride">ValidationFailureActionOverride</a>)
    </p>
  

  <p><p>ValidationFailureAction defines the policy validation failure action</p>
</p>

  

  <H3 id="kyverno-io-v1-ValidationFailureActionOverride">ValidationFailureActionOverride
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Spec">Spec</a>, 
        <a href="#kyverno-io-v1-Validation">Validation</a>)
    </p>
  

  <p></p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>action</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-ValidationFailureAction">
                <span style="font-family: monospace">ValidationFailureAction</span>
              </a>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>namespaces</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]string</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>namespaceSelector</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">meta/v1.LabelSelector</span>
            
          
        </td>
        <td>
          

          

          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-Variable">Variable
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-ContextEntry">ContextEntry</a>)
    </p>
  

  <p><p>Variable defines an arbitrary JMESPath context variable that can be defined inline.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>value</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">github.com/kyverno/kyverno/api/kyverno.Any</span>
            
          
        </td>
        <td>
          

          <p>Value is any arbitrary JSON object representable in YAML or JSON form.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>jmesPath</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">string</span>
            
          
        </td>
        <td>
          

          <p>JMESPath is an optional JMESPath Expression that can be used to
transform the variable.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>default</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">github.com/kyverno/kyverno/api/kyverno.Any</span>
            
          
        </td>
        <td>
          

          <p>Default is an optional arbitrary JSON object that the variable may take if the JMESPath
expression evaluates to nil</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

  <H3 id="kyverno-io-v1-WebhookConfiguration">WebhookConfiguration
    </H3>

  
    <p>
      (<em>Appears in:</em>
        <a href="#kyverno-io-v1-Spec">Spec</a>)
    </p>
  

  <p><p>WebhookConfiguration specifies the configuration for Kubernetes admission webhookconfiguration.</p>
</p>

  
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          <th>Field</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        
        

        
        

  
  
    
    
      <tr>
        <td><code>failurePolicy</code>
          
          </br>

          
          
            
              <a href="#kyverno-io-v1-FailurePolicyType">
                <span style="font-family: monospace">FailurePolicyType</span>
              </a>
            
          
        </td>
        <td>
          

          <p>FailurePolicy defines how unexpected policy errors and webhook response timeout errors are handled.
Rules within the same policy share the same failure behavior.
This field should not be accessed directly, instead <code>GetFailurePolicy()</code> should be used.
Allowed values are Ignore or Fail. Defaults to Fail.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>timeoutSeconds</code>
          
          <span style="color:blue;"> *</span>
          
          </br>

          
          
            
              <span style="font-family: monospace">int32</span>
            
          
        </td>
        <td>
          

          <p>TimeoutSeconds specifies the maximum time in seconds allowed to apply this policy.
After the configured time expires, the admission request may fail, or may simply ignore the policy results,
based on the failure policy. The default timeout is 10s, the value must be between 1 and 30 seconds.</p>


          

          
        </td>
      </tr>
    
  
    
    
      <tr>
        <td><code>matchConditions</code>
          
          </br>

          
          
            
              <span style="font-family: monospace">[]admissionregistration/v1.MatchCondition</span>
            
          
        </td>
        <td>
          

          <p>MatchCondition configures admission webhook matchConditions.
Requires Kubernetes 1.27 or later.</p>


          

          
        </td>
      </tr>
    
  


      </tbody>
    </table>
  

          
          <hr />
        
      </div>
    </body>
  </html>

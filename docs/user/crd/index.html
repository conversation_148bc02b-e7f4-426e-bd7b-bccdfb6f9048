<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
<title>Kyverno API</title>
<style>
.bg-blue {
color: #ffffff;
background-color: #1589dd;
}
</style>
</head>
<body>
<div class="container">
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
<a class="navbar-brand" href="#"><p><b>Packages : </b></p></a>
<ul style="list-style:none">
<li>
<a href="#kyverno.io%2fv1"><b style="color: white">kyverno.io/v1</b></a>
</li>
<li>
<a href="#kyverno.io%2fv1beta1"><b style="color: white">kyverno.io/v1beta1</b></a>
</li>
<li>
<a href="#kyverno.io%2fv2"><b style="color: white">kyverno.io/v2</b></a>
</li>
<li>
<a href="#kyverno.io%2fv2alpha1"><b style="color: white">kyverno.io/v2alpha1</b></a>
</li>
<li>
<a href="#kyverno.io%2fv2beta1"><b style="color: white">kyverno.io/v2beta1</b></a>
</li>
<li>
<a href="#policies.kyverno.io%2fv1alpha1"><b style="color: white">policies.kyverno.io/v1alpha1</b></a>
</li>
<li>
<a href="#reports.kyverno.io%2fv1"><b style="color: white">reports.kyverno.io/v1</b></a>
</li>
<li>
<a href="#wgpolicyk8s.io%2fv1alpha2"><b style="color: white">wgpolicyk8s.io/v1alpha2</b></a>
</li>
</ul>
</nav>
<h2 id="kyverno.io/v1">kyverno.io/v1</h2>
Resource Types:
<ul><li>
<a href="#kyverno.io/v1.ClusterPolicy">ClusterPolicy</a>
</li><li>
<a href="#kyverno.io/v1.Policy">Policy</a>
</li></ul>
<hr />
<h3 id="kyverno.io/v1.ClusterPolicy">ClusterPolicy
</h3>
<p>
<p>ClusterPolicy declares validation, mutation, and generation behaviors for matching resources.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>ClusterPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v1.Spec">
Spec
</a>
</em>
</td>
<td>
<p>Spec declares policy behaviors.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>rules</code><br/>
<em>
<a href="#kyverno.io/v1.Rule">
[]Rule
</a>
</em>
</td>
<td>
<p>Rules is a list of Rule instances. A Policy contains multiple rules and
each rule can validate, mutate, or generate resources.</p>
</td>
</tr>
<tr>
<td>
<code>applyRules</code><br/>
<em>
<a href="#kyverno.io/v1.ApplyRulesType">
ApplyRulesType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ApplyRules controls how rules in a policy are applied. Rule are processed in
the order of declaration. When set to <code>One</code> processing stops after a rule has
been applied i.e. the rule matches and results in a pass, fail, or error. When
set to <code>All</code> all rules in the policy are processed. The default is <code>All</code>.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="#kyverno.io/v1.FailurePolicyType">
FailurePolicyType
</a>
</em>
</td>
<td>
<p>Deprecated, use failurePolicy under the webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureAction</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureAction under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureActionOverrides</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureActionOverride">
[]ValidationFailureActionOverride
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureActionOverrides under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>emitWarning</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>EmitWarning enables API response warnings for mutate policy rules or validate policy rules with validationFailureAction set to Audit.
Enabling this option will extend admission request processing times. The default value is &ldquo;false&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>admission</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Admission controls if rules are applied during admission.
Optional. Default value is &ldquo;true&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>background</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Background controls if rules are applied to existing resources during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
<tr>
<td>
<code>schemaValidation</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated.</p>
</td>
</tr>
<tr>
<td>
<code>webhookTimeoutSeconds</code><br/>
<em>
int32
</em>
</td>
<td>
<p>Deprecated, use webhookTimeoutSeconds under webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>mutateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use mutateExistingOnPolicyUpdate under the mutate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use generateExisting instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExisting</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use generateExisting under the generate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>useServerSideApply</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>UseServerSideApply controls whether to use server-side apply for generate rules
If is set to &ldquo;true&rdquo; create &amp; update for generate rules will use apply instead of create/update.
Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#kyverno.io/v1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration specifies the custom configuration for Kubernetes admission webhookconfiguration.</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v1.PolicyStatus">
PolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Policy">Policy
</h3>
<p>
<p>Policy declares validation, mutation, and generation behaviors for matching resources.
See: <a href="https://kyverno.io/docs/writing-policies/">https://kyverno.io/docs/writing-policies/</a> for more information.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>Policy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v1.Spec">
Spec
</a>
</em>
</td>
<td>
<p>Spec defines policy behaviors and contains one or more rules.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>rules</code><br/>
<em>
<a href="#kyverno.io/v1.Rule">
[]Rule
</a>
</em>
</td>
<td>
<p>Rules is a list of Rule instances. A Policy contains multiple rules and
each rule can validate, mutate, or generate resources.</p>
</td>
</tr>
<tr>
<td>
<code>applyRules</code><br/>
<em>
<a href="#kyverno.io/v1.ApplyRulesType">
ApplyRulesType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ApplyRules controls how rules in a policy are applied. Rule are processed in
the order of declaration. When set to <code>One</code> processing stops after a rule has
been applied i.e. the rule matches and results in a pass, fail, or error. When
set to <code>All</code> all rules in the policy are processed. The default is <code>All</code>.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="#kyverno.io/v1.FailurePolicyType">
FailurePolicyType
</a>
</em>
</td>
<td>
<p>Deprecated, use failurePolicy under the webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureAction</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureAction under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureActionOverrides</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureActionOverride">
[]ValidationFailureActionOverride
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureActionOverrides under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>emitWarning</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>EmitWarning enables API response warnings for mutate policy rules or validate policy rules with validationFailureAction set to Audit.
Enabling this option will extend admission request processing times. The default value is &ldquo;false&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>admission</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Admission controls if rules are applied during admission.
Optional. Default value is &ldquo;true&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>background</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Background controls if rules are applied to existing resources during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
<tr>
<td>
<code>schemaValidation</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated.</p>
</td>
</tr>
<tr>
<td>
<code>webhookTimeoutSeconds</code><br/>
<em>
int32
</em>
</td>
<td>
<p>Deprecated, use webhookTimeoutSeconds under webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>mutateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use mutateExistingOnPolicyUpdate under the mutate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use generateExisting instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExisting</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use generateExisting under the generate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>useServerSideApply</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>UseServerSideApply controls whether to use server-side apply for generate rules
If is set to &ldquo;true&rdquo; create &amp; update for generate rules will use apply instead of create/update.
Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#kyverno.io/v1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration specifies the custom configuration for Kubernetes admission webhookconfiguration.</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v1.PolicyStatus">
PolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated. Policy metrics are available via the metrics endpoint</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.APICall">APICall
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ContextAPICall">ContextAPICall</a>, 
<a href="#kyverno.io/v2alpha1.ExternalAPICall">ExternalAPICall</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>urlPath</code><br/>
<em>
string
</em>
</td>
<td>
<p>URLPath is the URL path to be used in the HTTP GET or POST request to the
Kubernetes API server (e.g. &ldquo;/api/v1/namespaces&rdquo; or  &ldquo;/apis/apps/v1/deployments&rdquo;).
The format required is the same format used by the <code>kubectl get --raw</code> command.
See <a href="https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls">https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls</a>
for details.
It&rsquo;s mutually exclusive with the Service field.</p>
</td>
</tr>
<tr>
<td>
<code>method</code><br/>
<em>
<a href="#kyverno.io/v1.Method">
Method
</a>
</em>
</td>
<td>
<p>Method is the HTTP request type (GET or POST). Defaults to GET.</p>
</td>
</tr>
<tr>
<td>
<code>data</code><br/>
<em>
<a href="#kyverno.io/v1.RequestData">
[]RequestData
</a>
</em>
</td>
<td>
<p>The data object specifies the POST data sent to the server.
Only applicable when the method field is set to POST.</p>
</td>
</tr>
<tr>
<td>
<code>service</code><br/>
<em>
<a href="#kyverno.io/v1.ServiceCall">
ServiceCall
</a>
</em>
</td>
<td>
<p>Service is an API call to a JSON web service.
This is used for non-Kubernetes API server calls.
It&rsquo;s mutually exclusive with the URLPath field.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.AdmissionOperation">AdmissionOperation
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ResourceDescription">ResourceDescription</a>, 
<a href="#kyverno.io/v2beta1.ResourceDescription">ResourceDescription</a>)
</p>
<p>
<p>AdmissionOperation can have one of the values CREATE, UPDATE, CONNECT, DELETE, which are used to match a specific action.</p>
</p>
<h3 id="kyverno.io/v1.AnyAllConditions">AnyAllConditions
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Attestation">Attestation</a>, 
<a href="#kyverno.io/v1.ForEachGeneration">ForEachGeneration</a>, 
<a href="#kyverno.io/v1.ForEachMutation">ForEachMutation</a>, 
<a href="#kyverno.io/v1.ForEachValidation">ForEachValidation</a>)
</p>
<p>
<p>AnyAllConditions consists of conditions wrapped denoting a logical criteria to be fulfilled.
AnyConditions get fulfilled when at least one of its sub-conditions passes.
AllConditions get fulfilled only when all of its sub-conditions pass.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>any</code><br/>
<em>
<a href="#kyverno.io/v1.Condition">
[]Condition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AnyConditions enable variable-based conditional rule execution. This is useful for
finer control of when an rule is applied. A condition can reference object data
using JMESPath notation.
Here, at least one of the conditions need to pass</p>
</td>
</tr>
<tr>
<td>
<code>all</code><br/>
<em>
<a href="#kyverno.io/v1.Condition">
[]Condition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AllConditions enable variable-based conditional rule execution. This is useful for
finer control of when an rule is applied. A condition can reference object data
using JMESPath notation.
Here, all of the conditions need to pass</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ApplyRulesType">ApplyRulesType
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Spec">Spec</a>, 
<a href="#kyverno.io/v2beta1.Spec">Spec</a>)
</p>
<p>
<p>ApplyRulesType controls whether processing stops after one rule is applied or all rules are applied.</p>
</p>
<h3 id="kyverno.io/v1.Attestation">Attestation
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ImageVerification">ImageVerification</a>, 
<a href="#kyverno.io/v2beta1.ImageVerification">ImageVerification</a>)
</p>
<p>
<p>Attestation are checks for signed in-toto Statements that are used to verify the image.
See <a href="https://github.com/in-toto/attestation">https://github.com/in-toto/attestation</a>. Kyverno fetches signed attestations from the
OCI registry and decodes them into a list of Statements.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the variable name.</p>
</td>
</tr>
<tr>
<td>
<code>predicateType</code><br/>
<em>
string
</em>
</td>
<td>
<p>Deprecated in favour of &lsquo;Type&rsquo;, to be removed soon</p>
</td>
</tr>
<tr>
<td>
<code>type</code><br/>
<em>
string
</em>
</td>
<td>
<p>Type defines the type of attestation contained within the Statement.</p>
</td>
</tr>
<tr>
<td>
<code>attestors</code><br/>
<em>
<a href="#kyverno.io/v1.AttestorSet">
[]AttestorSet
</a>
</em>
</td>
<td>
<p>Attestors specify the required attestors (i.e. authorities).</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v1.AnyAllConditions">
[]AnyAllConditions
</a>
</em>
</td>
<td>
<p>Conditions are used to verify attributes within a Predicate. If no Conditions are specified
the attestation check is satisfied as long there are predicates that match the predicate type.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Attestor">Attestor
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.AttestorSet">AttestorSet</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>keys</code><br/>
<em>
<a href="#kyverno.io/v1.StaticKeyAttestor">
StaticKeyAttestor
</a>
</em>
</td>
<td>
<p>Keys specifies one or more public keys.</p>
</td>
</tr>
<tr>
<td>
<code>certificates</code><br/>
<em>
<a href="#kyverno.io/v1.CertificateAttestor">
CertificateAttestor
</a>
</em>
</td>
<td>
<p>Certificates specifies one or more certificates.</p>
</td>
</tr>
<tr>
<td>
<code>keyless</code><br/>
<em>
<a href="#kyverno.io/v1.KeylessAttestor">
KeylessAttestor
</a>
</em>
</td>
<td>
<p>Keyless is a set of attribute used to verify a Sigstore keyless attestor.
See <a href="https://github.com/sigstore/cosign/blob/main/KEYLESS.md">https://github.com/sigstore/cosign/blob/main/KEYLESS.md</a>.</p>
</td>
</tr>
<tr>
<td>
<code>attestor</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<p>Attestor is a nested set of Attestor used to specify a more complex set of match authorities.</p>
</td>
</tr>
<tr>
<td>
<code>annotations</code><br/>
<em>
map[string]string
</em>
</td>
<td>
<p>Annotations are used for image verification.
Every specified key-value pair must exist and match in the verified payload.
The payload may contain other key-value pairs.</p>
</td>
</tr>
<tr>
<td>
<code>repository</code><br/>
<em>
string
</em>
</td>
<td>
<p>Repository is an optional alternate OCI repository to use for signatures and attestations that match this rule.
If specified Repository will override other OCI image repository locations for this Attestor.</p>
</td>
</tr>
<tr>
<td>
<code>signatureAlgorithm</code><br/>
<em>
string
</em>
</td>
<td>
<p>Specify signature algorithm for public keys. Supported values are sha224, sha256, sha384 and sha512.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.AttestorSet">AttestorSet
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Attestation">Attestation</a>, 
<a href="#kyverno.io/v1.ImageVerification">ImageVerification</a>, 
<a href="#kyverno.io/v1.Manifests">Manifests</a>, 
<a href="#kyverno.io/v2beta1.ImageVerification">ImageVerification</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>count</code><br/>
<em>
int
</em>
</td>
<td>
<p>Count specifies the required number of entries that must match. If the count is null, all entries must match
(a logical AND). If the count is 1, at least one entry must match (a logical OR). If the count contains a
value N, then N must be less than or equal to the size of entries, and at least N entries must match.</p>
</td>
</tr>
<tr>
<td>
<code>entries</code><br/>
<em>
<a href="#kyverno.io/v1.Attestor">
[]Attestor
</a>
</em>
</td>
<td>
<p>Entries contains the available attestors. An attestor can be a static key,
attributes for keyless verification, or a nested attestor declaration.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.AutogenStatus">AutogenStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.PolicyStatus">PolicyStatus</a>)
</p>
<p>
<p>AutogenStatus contains autogen status information.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>rules</code><br/>
<em>
<a href="#kyverno.io/v1.Rule">
[]Rule
</a>
</em>
</td>
<td>
<p>Rules is a list of Rule instances. It contains auto generated rules added for pod controllers</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.CEL">CEL
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Validation">Validation</a>, 
<a href="#kyverno.io/v2beta1.Validation">Validation</a>)
</p>
<p>
<p>CEL allows validation checks using the Common Expression Language (<a href="https://kubernetes.io/docs/reference/using-api/cel/">https://kubernetes.io/docs/reference/using-api/cel/</a>).</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>generate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Generate specifies whether to generate a Kubernetes ValidatingAdmissionPolicy from the rule.
Optional. Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>expressions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#validation-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Validation
</a>
</em>
</td>
<td>
<p>Expressions is a list of CELExpression types.</p>
</td>
</tr>
<tr>
<td>
<code>paramKind</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#paramkind-v1-admissionregistration">
Kubernetes admissionregistration/v1.ParamKind
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ParamKind is a tuple of Group Kind and Version.</p>
</td>
</tr>
<tr>
<td>
<code>paramRef</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#paramref-v1-admissionregistration">
Kubernetes admissionregistration/v1.ParamRef
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ParamRef references a parameter resource.</p>
</td>
</tr>
<tr>
<td>
<code>auditAnnotations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#auditannotation-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.AuditAnnotation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AuditAnnotations contains CEL expressions which are used to produce audit annotations for the audit event of the API request.</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.
The variables defined here will be available under <code>variables</code> in other expressions of the policy.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.CTLog">CTLog
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.CertificateAttestor">CertificateAttestor</a>, 
<a href="#kyverno.io/v1.KeylessAttestor">KeylessAttestor</a>, 
<a href="#kyverno.io/v1.StaticKeyAttestor">StaticKeyAttestor</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>ignoreSCT</code><br/>
<em>
bool
</em>
</td>
<td>
<p>IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate
timestamp. Default is false. Set to true if this was opted out during signing.</p>
</td>
</tr>
<tr>
<td>
<code>pubkey</code><br/>
<em>
string
</em>
</td>
<td>
<p>PubKey, if set, is used to validate SCTs against a custom source.</p>
</td>
</tr>
<tr>
<td>
<code>tsaCertChain</code><br/>
<em>
string
</em>
</td>
<td>
<p>TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must
contain the root CA certificate. Optionally may contain intermediate CA certificates, and
may contain the leaf TSA certificate if not present in the timestamurce.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.CertificateAttestor">CertificateAttestor
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Attestor">Attestor</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>cert</code><br/>
<em>
string
</em>
</td>
<td>
<p>Cert is an optional PEM-encoded public certificate.</p>
</td>
</tr>
<tr>
<td>
<code>certChain</code><br/>
<em>
string
</em>
</td>
<td>
<p>CertChain is an optional PEM encoded set of certificates used to verify.</p>
</td>
</tr>
<tr>
<td>
<code>rekor</code><br/>
<em>
<a href="#kyverno.io/v1.Rekor">
Rekor
</a>
</em>
</td>
<td>
<p>Rekor provides configuration for the Rekor transparency log service. If an empty object
is provided the public instance of Rekor (<a href="https://rekor.sigstore.dev">https://rekor.sigstore.dev</a>) is used.</p>
</td>
</tr>
<tr>
<td>
<code>ctlog</code><br/>
<em>
<a href="#kyverno.io/v1.CTLog">
CTLog
</a>
</em>
</td>
<td>
<p>CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate
Timestamps (SCTs). If the value is unset, the default behavior by Cosign is used.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.CloneFrom">CloneFrom
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.GeneratePattern">GeneratePattern</a>)
</p>
<p>
<p>CloneFrom provides the location of the source resource used to generate target resources.
The resource kind is derived from the match criteria.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>namespace</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Namespace specifies source resource namespace.</p>
</td>
</tr>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name specifies name of the resource.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Condition">Condition
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.AnyAllConditions">AnyAllConditions</a>)
</p>
<p>
<p>Condition defines variable-based conditional criteria for rule execution.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>key</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<p>Key is the context entry (using JMESPath) for conditional rule evaluation.</p>
</td>
</tr>
<tr>
<td>
<code>operator</code><br/>
<em>
<a href="#kyverno.io/v1.ConditionOperator">
ConditionOperator
</a>
</em>
</td>
<td>
<p>Operator is the conditional operation to perform. Valid operators are:
Equals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,
GreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,
DurationLessThanOrEquals, DurationLessThan</p>
</td>
</tr>
<tr>
<td>
<code>value</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Value is the conditional value, or set of values. The values can be fixed set
or can be variables declared using JMESPath.</p>
</td>
</tr>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<p>Message is an optional display message</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ConditionOperator">ConditionOperator
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Condition">Condition</a>)
</p>
<p>
<p>ConditionOperator is the operation performed on condition key and value.</p>
</p>
<h3 id="kyverno.io/v1.ConditionsWrapper">ConditionsWrapper
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Deny">Deny</a>, 
<a href="#kyverno.io/v1.Rule">Rule</a>, 
<a href="#kyverno.io/v1.TargetResourceSpec">TargetResourceSpec</a>)
</p>
<p>
<p>ConditionsWrapper contains either the deprecated list of Conditions or the new AnyAll Conditions.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>-</code><br/>
<em>
any
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions is a list of conditions that must be satisfied for the rule to be applied.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ConfigMapReference">ConfigMapReference
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ContextEntry">ContextEntry</a>)
</p>
<p>
<p>ConfigMapReference refers to a ConfigMap</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the ConfigMap name.</p>
</td>
</tr>
<tr>
<td>
<code>namespace</code><br/>
<em>
string
</em>
</td>
<td>
<p>Namespace is the ConfigMap namespace.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ContextAPICall">ContextAPICall
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ContextEntry">ContextEntry</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>APICall</code><br/>
<em>
<a href="#kyverno.io/v1.APICall">
APICall
</a>
</em>
</td>
<td>
<p>
(Members of <code>APICall</code> are embedded into this type.)
</p>
</td>
</tr>
<tr>
<td>
<code>default</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Default is an optional arbitrary JSON object that the context
value is set to, if the apiCall returns error.</p>
</td>
</tr>
<tr>
<td>
<code>jmesPath</code><br/>
<em>
string
</em>
</td>
<td>
<p>JMESPath is an optional JSON Match Expression that can be used to
transform the JSON response returned from the server. For example
a JMESPath of &ldquo;items | length(@)&rdquo; applied to the API server response
for the URLPath &ldquo;/apis/apps/v1/deployments&rdquo; will return the total count
of deployments across all namespaces.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ContextEntry">ContextEntry
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ForEachGeneration">ForEachGeneration</a>, 
<a href="#kyverno.io/v1.ForEachMutation">ForEachMutation</a>, 
<a href="#kyverno.io/v1.ForEachValidation">ForEachValidation</a>, 
<a href="#kyverno.io/v1.Rule">Rule</a>, 
<a href="#kyverno.io/v1.TargetResourceSpec">TargetResourceSpec</a>, 
<a href="#kyverno.io/v2.CleanupPolicySpec">CleanupPolicySpec</a>, 
<a href="#kyverno.io/v2beta1.CleanupPolicySpec">CleanupPolicySpec</a>, 
<a href="#kyverno.io/v2beta1.Rule">Rule</a>)
</p>
<p>
<p>ContextEntry adds variables and data sources to a rule Context. Either a
ConfigMap reference or a APILookup must be provided.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the variable name.</p>
</td>
</tr>
<tr>
<td>
<code>configMap</code><br/>
<em>
<a href="#kyverno.io/v1.ConfigMapReference">
ConfigMapReference
</a>
</em>
</td>
<td>
<p>ConfigMap is the ConfigMap reference.</p>
</td>
</tr>
<tr>
<td>
<code>apiCall</code><br/>
<em>
<a href="#kyverno.io/v1.ContextAPICall">
ContextAPICall
</a>
</em>
</td>
<td>
<p>APICall is an HTTP request to the Kubernetes API server, or other JSON web service.
The data returned is stored in the context with the name for the context entry.</p>
</td>
</tr>
<tr>
<td>
<code>imageRegistry</code><br/>
<em>
<a href="#kyverno.io/v1.ImageRegistry">
ImageRegistry
</a>
</em>
</td>
<td>
<p>ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image
details.</p>
</td>
</tr>
<tr>
<td>
<code>variable</code><br/>
<em>
<a href="#kyverno.io/v1.Variable">
Variable
</a>
</em>
</td>
<td>
<p>Variable defines an arbitrary JMESPath context variable that can be defined inline.</p>
</td>
</tr>
<tr>
<td>
<code>globalReference</code><br/>
<em>
<a href="#kyverno.io/v1.GlobalContextEntryReference">
GlobalContextEntryReference
</a>
</em>
</td>
<td>
<p>GlobalContextEntryReference is a reference to a cached global context entry.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Deny">Deny
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ForEachValidation">ForEachValidation</a>, 
<a href="#kyverno.io/v1.ValidateImageVerification">ValidateImageVerification</a>, 
<a href="#kyverno.io/v1.Validation">Validation</a>)
</p>
<p>
<p>Deny specifies a list of conditions used to pass or fail a validation rule.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v1.ConditionsWrapper">
ConditionsWrapper
</a>
</em>
</td>
<td>
<p>Multiple conditions can be declared under an <code>any</code> or <code>all</code> statement. A direct list
of conditions (without <code>any</code> or <code>all</code> statements) is also supported for backwards compatibility
but will be deprecated in the next major release.
See: <a href="https://kyverno.io/docs/writing-policies/validate/#deny-rules">https://kyverno.io/docs/writing-policies/validate/#deny-rules</a></p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.DryRunOption">DryRunOption
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Manifests">Manifests</a>)
</p>
<p>
<p>DryRunOption is a configuration for dryrun.
If enable is set to &ldquo;true&rdquo;, manifest verification performs &ldquo;dryrun &amp; compare&rdquo;
which provides robust matching against changes by defaults and other admission controllers.
Dryrun requires additional permissions. See config/dryrun/dryrun_rbac.yaml</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>enable</code><br/>
<em>
bool
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>namespace</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.FailurePolicyType">FailurePolicyType
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Spec">Spec</a>, 
<a href="#kyverno.io/v1.WebhookConfiguration">WebhookConfiguration</a>, 
<a href="#kyverno.io/v2beta1.Spec">Spec</a>)
</p>
<p>
<p>FailurePolicyType specifies a failure policy that defines how unrecognized errors from the admission endpoint are handled.</p>
</p>
<h3 id="kyverno.io/v1.ForEachGeneration">ForEachGeneration
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Generation">Generation</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>list</code><br/>
<em>
string
</em>
</td>
<td>
<p>List specifies a JMESPath expression that results in one or more elements
to which the validation logic is applied.</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>preconditions</code><br/>
<em>
<a href="#kyverno.io/v1.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AnyAllConditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.
See: <a href="https://kyverno.io/docs/writing-policies/preconditions/">https://kyverno.io/docs/writing-policies/preconditions/</a></p>
</td>
</tr>
<tr>
<td>
<code>GeneratePattern</code><br/>
<em>
<a href="#kyverno.io/v1.GeneratePattern">
GeneratePattern
</a>
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ForEachMutation">ForEachMutation
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ForEachMutationWrapper">ForEachMutationWrapper</a>, 
<a href="#kyverno.io/v1.Mutation">Mutation</a>)
</p>
<p>
<p>ForEachMutation applies mutation rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>list</code><br/>
<em>
string
</em>
</td>
<td>
<p>List specifies a JMESPath expression that results in one or more elements
to which the validation logic is applied.</p>
</td>
</tr>
<tr>
<td>
<code>order</code><br/>
<em>
<a href="#kyverno.io/v1.ForeachOrder">
ForeachOrder
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Order defines the iteration order on the list.
Can be Ascending to iterate from first to last element or Descending to iterate in from last to first element.</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>preconditions</code><br/>
<em>
<a href="#kyverno.io/v1.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AnyAllConditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.
See: <a href="https://kyverno.io/docs/writing-policies/preconditions/">https://kyverno.io/docs/writing-policies/preconditions/</a></p>
</td>
</tr>
<tr>
<td>
<code>patchStrategicMerge</code><br/>
<em>
github.com/kyverno/kyverno/api/kyverno.Any
</em>
</td>
<td>
<em>(Optional)</em>
<p>PatchStrategicMerge is a strategic merge patch used to modify resources.
See <a href="https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch/">https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch/</a>
and <a href="https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesstrategicmerge/">https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesstrategicmerge/</a>.</p>
</td>
</tr>
<tr>
<td>
<code>patchesJson6902</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>PatchesJSON6902 is a list of RFC 6902 JSON Patch declarations used to modify resources.
See <a href="https://tools.ietf.org/html/rfc6902">https://tools.ietf.org/html/rfc6902</a> and <a href="https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesjson6902/">https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesjson6902/</a>.</p>
</td>
</tr>
<tr>
<td>
<code>foreach</code><br/>
<em>
<a href="#kyverno.io/v1.ForEachMutationWrapper">
ForEachMutationWrapper
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Foreach declares a nested foreach iterator</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ForEachMutationWrapper">ForEachMutationWrapper
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ForEachMutation">ForEachMutation</a>)
</p>
<p>
<p>ForEachMutationWrapper contains a list of ForEach descriptors.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>-</code><br/>
<em>
<a href="#kyverno.io/v1.ForEachMutation">
[]ForEachMutation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Item is a descriptor on how to iterate over the list of items.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ForEachValidation">ForEachValidation
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ForEachValidationWrapper">ForEachValidationWrapper</a>, 
<a href="#kyverno.io/v1.Validation">Validation</a>, 
<a href="#kyverno.io/v2beta1.Validation">Validation</a>)
</p>
<p>
<p>ForEachValidation applies validate rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>list</code><br/>
<em>
string
</em>
</td>
<td>
<p>List specifies a JMESPath expression that results in one or more elements
to which the validation logic is applied.</p>
</td>
</tr>
<tr>
<td>
<code>elementScope</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>ElementScope specifies whether to use the current list element as the scope for validation. Defaults to &ldquo;true&rdquo; if not specified.
When set to &ldquo;false&rdquo;, &ldquo;request.object&rdquo; is used as the validation scope within the foreach
block to allow referencing other elements in the subtree.</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>preconditions</code><br/>
<em>
<a href="#kyverno.io/v1.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AnyAllConditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.
See: <a href="https://kyverno.io/docs/writing-policies/preconditions/">https://kyverno.io/docs/writing-policies/preconditions/</a></p>
</td>
</tr>
<tr>
<td>
<code>pattern</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Pattern specifies an overlay-style pattern used to check resources.</p>
</td>
</tr>
<tr>
<td>
<code>anyPattern</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AnyPattern specifies list of validation patterns. At least one of the patterns
must be satisfied for the validation rule to succeed.</p>
</td>
</tr>
<tr>
<td>
<code>deny</code><br/>
<em>
<a href="#kyverno.io/v1.Deny">
Deny
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deny defines conditions used to pass or fail a validation rule.</p>
</td>
</tr>
<tr>
<td>
<code>foreach</code><br/>
<em>
<a href="#kyverno.io/v1.ForEachValidationWrapper">
ForEachValidationWrapper
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Foreach declares a nested foreach iterator</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ForEachValidationWrapper">ForEachValidationWrapper
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ForEachValidation">ForEachValidation</a>)
</p>
<p>
<p>ForEachValidationWrapper contains a list of ForEach descriptors.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>-</code><br/>
<em>
<a href="#kyverno.io/v1.ForEachValidation">
[]ForEachValidation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Item is a descriptor on how to iterate over the list of items.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ForeachOrder">ForeachOrder
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ForEachMutation">ForEachMutation</a>)
</p>
<p>
<p>ForeachOrder specifies the iteration order in foreach statements.</p>
</p>
<h3 id="kyverno.io/v1.GeneratePattern">GeneratePattern
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ForEachGeneration">ForEachGeneration</a>, 
<a href="#kyverno.io/v1.Generation">Generation</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>ResourceSpec</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceSpec">
ResourceSpec
</a>
</em>
</td>
<td>
<p>ResourceSpec contains information to select the resource.</p>
</td>
</tr>
<tr>
<td>
<code>data</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Data provides the resource declaration used to populate each generated resource.
At most one of Data or Clone must be specified. If neither are provided, the generated
resource will be created with default data only.</p>
</td>
</tr>
<tr>
<td>
<code>clone</code><br/>
<em>
<a href="#kyverno.io/v1.CloneFrom">
CloneFrom
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Clone specifies the source resource used to populate each generated resource.
At most one of Data or Clone can be specified. If neither are provided, the generated
resource will be created with default data only.</p>
</td>
</tr>
<tr>
<td>
<code>cloneList</code><br/>
<em>
<a href="#kyverno.io/v1.CloneList">
CloneList
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>CloneList specifies the list of source resource used to populate each generated resource.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.GenerateType">GenerateType
(<code>string</code> alias)</p></h3>
<p>
</p>
<h3 id="kyverno.io/v1.Generation">Generation
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Rule">Rule</a>, 
<a href="#kyverno.io/v2beta1.Rule">Rule</a>)
</p>
<p>
<p>Generation defines how new resources should be created and managed.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>generateExisting</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>GenerateExisting controls whether to trigger the rule in existing resources
If is set to &ldquo;true&rdquo; the rule will be triggered and applied to existing matched resources.</p>
</td>
</tr>
<tr>
<td>
<code>synchronize</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Synchronize controls if generated resources should be kept in-sync with their source resource.
If Synchronize is set to &ldquo;true&rdquo; changes to generated resources will be overwritten with resource
data from Data or the resource specified in the Clone declaration.
Optional. Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>orphanDownstreamOnPolicyDelete</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>OrphanDownstreamOnPolicyDelete controls whether generated resources should be deleted when the rule that generated
them is deleted with synchronization enabled. This option is only applicable to generate rules of the data type.
See <a href="https://kyverno.io/docs/writing-policies/generate/#data-examples">https://kyverno.io/docs/writing-policies/generate/#data-examples</a>.
Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>GeneratePattern</code><br/>
<em>
<a href="#kyverno.io/v1.GeneratePattern">
GeneratePattern
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>foreach</code><br/>
<em>
<a href="#kyverno.io/v1.ForEachGeneration">
[]ForEachGeneration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ForEach applies generate rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.GlobalContextEntryReference">GlobalContextEntryReference
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ContextEntry">ContextEntry</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name of the global context entry</p>
</td>
</tr>
<tr>
<td>
<code>jmesPath</code><br/>
<em>
string
</em>
</td>
<td>
<p>JMESPath is an optional JSON Match Expression that can be used to
transform the JSON response returned from the server. For example
a JMESPath of &ldquo;items | length(@)&rdquo; applied to the API server response
for the URLPath &ldquo;/apis/apps/v1/deployments&rdquo; will return the total count
of deployments across all namespaces.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.HTTPHeader">HTTPHeader
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ServiceCall">ServiceCall</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>key</code><br/>
<em>
string
</em>
</td>
<td>
<p>Key is the header key</p>
</td>
</tr>
<tr>
<td>
<code>value</code><br/>
<em>
string
</em>
</td>
<td>
<p>Value is the header value</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ImageExtractorConfig">ImageExtractorConfig
</h3>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>path</code><br/>
<em>
string
</em>
</td>
<td>
<p>Path is the path to the object containing the image field in a custom resource.
It should be slash-separated. Each slash-separated key must be a valid YAML key or a wildcard &lsquo;*&rsquo;.
Wildcard keys are expanded in case of arrays or objects.</p>
</td>
</tr>
<tr>
<td>
<code>value</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Value is an optional name of the field within &lsquo;path&rsquo; that points to the image URI.
This is useful when a custom &lsquo;key&rsquo; is also defined.</p>
</td>
</tr>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Name is the entry the image will be available under &lsquo;images.<name>&rsquo; in the context.
If this field is not defined, image entries will appear under &lsquo;images.custom&rsquo;.</p>
</td>
</tr>
<tr>
<td>
<code>key</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Key is an optional name of the field within &lsquo;path&rsquo; that will be used to uniquely identify an image.
Note - this field MUST be unique.</p>
</td>
</tr>
<tr>
<td>
<code>jmesPath</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>JMESPath is an optional JMESPath expression to apply to the image value.
This is useful when the extracted image begins with a prefix like &lsquo;docker://&rsquo;.
The &lsquo;trim_prefix&rsquo; function may be used to trim the prefix: trim_prefix(@, &lsquo;docker://&rsquo;).
Note - Image digest mutation may not be used when applying a JMESPAth to an image.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ImageExtractorConfigs">ImageExtractorConfigs
(<code>map[string][]github.com/kyverno/kyverno/api/kyverno/v1.ImageExtractorConfig</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Rule">Rule</a>, 
<a href="#kyverno.io/v2beta1.Rule">Rule</a>)
</p>
<p>
</p>
<h3 id="kyverno.io/v1.ImageRegistry">ImageRegistry
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ContextEntry">ContextEntry</a>)
</p>
<p>
<p>ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image
details.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>reference</code><br/>
<em>
string
</em>
</td>
<td>
<p>Reference is image reference to a container image in the registry.
Example: ghcr.io/kyverno/kyverno:latest</p>
</td>
</tr>
<tr>
<td>
<code>jmesPath</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>JMESPath is an optional JSON Match Expression that can be used to
transform the ImageData struct returned as a result of processing
the image reference.</p>
</td>
</tr>
<tr>
<td>
<code>imageRegistryCredentials</code><br/>
<em>
<a href="#kyverno.io/v1.ImageRegistryCredentials">
ImageRegistryCredentials
</a>
</em>
</td>
<td>
<p>ImageRegistryCredentials provides credentials that will be used for authentication with registry</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ImageRegistryCredentials">ImageRegistryCredentials
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ImageRegistry">ImageRegistry</a>, 
<a href="#kyverno.io/v1.ImageVerification">ImageVerification</a>, 
<a href="#kyverno.io/v2beta1.ImageVerification">ImageVerification</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>allowInsecureRegistry</code><br/>
<em>
bool
</em>
</td>
<td>
<p>AllowInsecureRegistry allows insecure access to a registry.</p>
</td>
</tr>
<tr>
<td>
<code>providers</code><br/>
<em>
<a href="#kyverno.io/v1.ImageRegistryCredentialsProvidersType">
[]ImageRegistryCredentialsProvidersType
</a>
</em>
</td>
<td>
<p>Providers specifies a list of OCI Registry names, whose authentication providers are provided.
It can be of one of these values: default,google,azure,amazon,github.</p>
</td>
</tr>
<tr>
<td>
<code>secrets</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>Secrets specifies a list of secrets that are provided for credentials.
Secrets must live in the Kyverno namespace.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ImageRegistryCredentialsProvidersType">ImageRegistryCredentialsProvidersType
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ImageRegistryCredentials">ImageRegistryCredentials</a>)
</p>
<p>
<p>ImageRegistryCredentialsProvidersType provides the list of credential providers required.</p>
</p>
<h3 id="kyverno.io/v1.ImageVerification">ImageVerification
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Rule">Rule</a>)
</p>
<p>
<p>ImageVerification validates that images that match the specified pattern
are signed with the supplied public key. Once the image is verified it is
mutated to include the SHA digest retrieved during the registration.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>failureAction</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Allowed values are Audit or Enforce.</p>
</td>
</tr>
<tr>
<td>
<code>type</code><br/>
<em>
<a href="#kyverno.io/v1.ImageVerificationType">
ImageVerificationType
</a>
</em>
</td>
<td>
<p>Type specifies the method of signature validation. The allowed options
are Cosign, Sigstore Bundle and Notary. By default Cosign is used if a type is not specified.</p>
</td>
</tr>
<tr>
<td>
<code>image</code><br/>
<em>
string
</em>
</td>
<td>
<p>Deprecated. Use ImageReferences instead.</p>
</td>
</tr>
<tr>
<td>
<code>imageReferences</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>ImageReferences is a list of matching image reference patterns. At least one pattern in the
list must match the image for the rule to apply. Each image reference consists of a registry
address (defaults to docker.io), repository, image, and tag (defaults to latest).
Wildcards (&lsquo;*&rsquo; and &lsquo;?&rsquo;) are allowed. See: <a href="https://kubernetes.io/docs/concepts/containers/images">https://kubernetes.io/docs/concepts/containers/images</a>.</p>
</td>
</tr>
<tr>
<td>
<code>skipImageReferences</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>SkipImageReferences is a list of matching image reference patterns that should be skipped.
At least one pattern in the list must match the image for the rule to be skipped. Each image reference
consists of a registry address (defaults to docker.io), repository, image, and tag (defaults to latest).
Wildcards (&lsquo;*&rsquo; and &lsquo;?&rsquo;) are allowed. See: <a href="https://kubernetes.io/docs/concepts/containers/images">https://kubernetes.io/docs/concepts/containers/images</a>.</p>
</td>
</tr>
<tr>
<td>
<code>key</code><br/>
<em>
string
</em>
</td>
<td>
<p>Deprecated. Use StaticKeyAttestor instead.</p>
</td>
</tr>
<tr>
<td>
<code>roots</code><br/>
<em>
string
</em>
</td>
<td>
<p>Deprecated. Use KeylessAttestor instead.</p>
</td>
</tr>
<tr>
<td>
<code>subject</code><br/>
<em>
string
</em>
</td>
<td>
<p>Deprecated. Use KeylessAttestor instead.</p>
</td>
</tr>
<tr>
<td>
<code>issuer</code><br/>
<em>
string
</em>
</td>
<td>
<p>Deprecated. Use KeylessAttestor instead.</p>
</td>
</tr>
<tr>
<td>
<code>additionalExtensions</code><br/>
<em>
map[string]string
</em>
</td>
<td>
<p>Deprecated.</p>
</td>
</tr>
<tr>
<td>
<code>attestors</code><br/>
<em>
<a href="#kyverno.io/v1.AttestorSet">
[]AttestorSet
</a>
</em>
</td>
<td>
<p>Attestors specified the required attestors (i.e. authorities)</p>
</td>
</tr>
<tr>
<td>
<code>attestations</code><br/>
<em>
<a href="#kyverno.io/v1.Attestation">
[]Attestation
</a>
</em>
</td>
<td>
<p>Attestations are optional checks for signed in-toto Statements used to verify the image.
See <a href="https://github.com/in-toto/attestation">https://github.com/in-toto/attestation</a>. Kyverno fetches signed attestations from the
OCI registry and decodes them into a list of Statement declarations.</p>
</td>
</tr>
<tr>
<td>
<code>annotations</code><br/>
<em>
map[string]string
</em>
</td>
<td>
<p>Deprecated. Use annotations per Attestor instead.</p>
</td>
</tr>
<tr>
<td>
<code>repository</code><br/>
<em>
string
</em>
</td>
<td>
<p>Repository is an optional alternate OCI repository to use for image signatures and attestations that match this rule.
If specified Repository will override the default OCI image repository configured for the installation.
The repository can also be overridden per Attestor or Attestation.</p>
</td>
</tr>
<tr>
<td>
<code>cosignOCI11</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>CosignOCI11 enables the experimental OCI 1.1 behaviour in cosign image verification.
Defaults to false.</p>
</td>
</tr>
<tr>
<td>
<code>mutateDigest</code><br/>
<em>
bool
</em>
</td>
<td>
<p>MutateDigest enables replacement of image tags with digests.
Defaults to true.</p>
</td>
</tr>
<tr>
<td>
<code>verifyDigest</code><br/>
<em>
bool
</em>
</td>
<td>
<p>VerifyDigest validates that images have a digest.</p>
</td>
</tr>
<tr>
<td>
<code>validate</code><br/>
<em>
<a href="#kyverno.io/v1.ValidateImageVerification">
ValidateImageVerification
</a>
</em>
</td>
<td>
<p>Validation checks conditions across multiple image
verification attestations or context entries</p>
</td>
</tr>
<tr>
<td>
<code>required</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Required validates that images are verified i.e. have matched passed a signature or attestation check.</p>
</td>
</tr>
<tr>
<td>
<code>imageRegistryCredentials</code><br/>
<em>
<a href="#kyverno.io/v1.ImageRegistryCredentials">
ImageRegistryCredentials
</a>
</em>
</td>
<td>
<p>ImageRegistryCredentials provides credentials that will be used for authentication with registry.</p>
</td>
</tr>
<tr>
<td>
<code>useCache</code><br/>
<em>
bool
</em>
</td>
<td>
<p>UseCache enables caching of image verify responses for this rule.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ImageVerificationType">ImageVerificationType
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ImageVerification">ImageVerification</a>, 
<a href="#kyverno.io/v2beta1.ImageVerification">ImageVerification</a>)
</p>
<p>
<p>ImageVerificationType selects the type of verification algorithm</p>
</p>
<h3 id="kyverno.io/v1.KeylessAttestor">KeylessAttestor
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Attestor">Attestor</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>rekor</code><br/>
<em>
<a href="#kyverno.io/v1.Rekor">
Rekor
</a>
</em>
</td>
<td>
<p>Rekor provides configuration for the Rekor transparency log service. If an empty object
is provided the public instance of Rekor (<a href="https://rekor.sigstore.dev">https://rekor.sigstore.dev</a>) is used.</p>
</td>
</tr>
<tr>
<td>
<code>ctlog</code><br/>
<em>
<a href="#kyverno.io/v1.CTLog">
CTLog
</a>
</em>
</td>
<td>
<p>CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate
Timestamps (SCTs). If the value is unset, the default behavior by Cosign is used.</p>
</td>
</tr>
<tr>
<td>
<code>issuer</code><br/>
<em>
string
</em>
</td>
<td>
<p>Issuer is the certificate issuer used for keyless signing.</p>
</td>
</tr>
<tr>
<td>
<code>issuerRegExp</code><br/>
<em>
string
</em>
</td>
<td>
<p>IssuerRegExp is the regular expression to match certificate issuer used for keyless signing.</p>
</td>
</tr>
<tr>
<td>
<code>subject</code><br/>
<em>
string
</em>
</td>
<td>
<p>Subject is the verified identity used for keyless signing, for example the email address.</p>
</td>
</tr>
<tr>
<td>
<code>subjectRegExp</code><br/>
<em>
string
</em>
</td>
<td>
<p>SubjectRegExp is the regular expression to match identity used for keyless signing, for example the email address.</p>
</td>
</tr>
<tr>
<td>
<code>roots</code><br/>
<em>
string
</em>
</td>
<td>
<p>Roots is an optional set of PEM encoded trusted root certificates.
If not provided, the system roots are used.</p>
</td>
</tr>
<tr>
<td>
<code>additionalExtensions</code><br/>
<em>
map[string]string
</em>
</td>
<td>
<p>AdditionalExtensions are certificate-extensions used for keyless signing.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Manifests">Manifests
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Validation">Validation</a>, 
<a href="#kyverno.io/v2beta1.Validation">Validation</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>attestors</code><br/>
<em>
<a href="#kyverno.io/v1.AttestorSet">
[]AttestorSet
</a>
</em>
</td>
<td>
<p>Attestors specified the required attestors (i.e. authorities)</p>
</td>
</tr>
<tr>
<td>
<code>annotationDomain</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>AnnotationDomain is custom domain of annotation for message and signature. Default is &ldquo;cosign.sigstore.dev&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>ignoreFields</code><br/>
<em>
<a href="#kyverno.io/v1.IgnoreFieldList">
IgnoreFieldList
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Fields which will be ignored while comparing manifests.</p>
</td>
</tr>
<tr>
<td>
<code>dryRun</code><br/>
<em>
<a href="#kyverno.io/v1.DryRunOption">
DryRunOption
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>DryRun configuration</p>
</td>
</tr>
<tr>
<td>
<code>repository</code><br/>
<em>
string
</em>
</td>
<td>
<p>Repository is an optional alternate OCI repository to use for resource bundle reference.
The repository can be overridden per Attestor or Attestation.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.MatchResources">MatchResources
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Rule">Rule</a>)
</p>
<p>
<p>MatchResources is used to specify resource and admission review request data for
which a policy rule is applicable.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>any</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceFilters">
ResourceFilters
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Any allows specifying resources which will be ORed</p>
</td>
</tr>
<tr>
<td>
<code>all</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceFilters">
ResourceFilters
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>All allows specifying resources which will be ANDed</p>
</td>
</tr>
<tr>
<td>
<code>UserInfo</code><br/>
<em>
<a href="#kyverno.io/v1.UserInfo">
UserInfo
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>UserInfo contains information about the user performing the operation.
Specifying UserInfo directly under match is being deprecated.
Please specify under &ldquo;any&rdquo; or &ldquo;all&rdquo; instead.</p>
</td>
</tr>
<tr>
<td>
<code>resources</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceDescription">
ResourceDescription
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ResourceDescription contains information about the resource being created or modified.
Requires at least one tag to be specified when under MatchResources.
Specifying ResourceDescription directly under match is being deprecated.
Please specify under &ldquo;any&rdquo; or &ldquo;all&rdquo; instead.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Method">Method
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.APICall">APICall</a>)
</p>
<p>
<p>Method is a HTTP request type.</p>
</p>
<h3 id="kyverno.io/v1.Mutation">Mutation
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Rule">Rule</a>, 
<a href="#kyverno.io/v2beta1.Rule">Rule</a>)
</p>
<p>
<p>Mutation defines how resource are modified.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>mutateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>MutateExistingOnPolicyUpdate controls if the mutateExisting rule will be applied on policy events.</p>
</td>
</tr>
<tr>
<td>
<code>targets</code><br/>
<em>
<a href="#kyverno.io/v1.TargetResourceSpec">
[]TargetResourceSpec
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Targets defines the target resources to be mutated.</p>
</td>
</tr>
<tr>
<td>
<code>patchStrategicMerge</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PatchStrategicMerge is a strategic merge patch used to modify resources.
See <a href="https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch/">https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch/</a>
and <a href="https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesstrategicmerge/">https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesstrategicmerge/</a>.</p>
</td>
</tr>
<tr>
<td>
<code>patchesJson6902</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>PatchesJSON6902 is a list of RFC 6902 JSON Patch declarations used to modify resources.
See <a href="https://tools.ietf.org/html/rfc6902">https://tools.ietf.org/html/rfc6902</a> and <a href="https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesjson6902/">https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesjson6902/</a>.</p>
</td>
</tr>
<tr>
<td>
<code>foreach</code><br/>
<em>
<a href="#kyverno.io/v1.ForEachMutation">
[]ForEachMutation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ForEach applies mutation rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ObjectFieldBinding">ObjectFieldBinding
</h3>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>fields</code><br/>
<em>
[]string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>objects</code><br/>
<em>
github.com/sigstore/k8s-manifest-sigstore/pkg/k8smanifest.ObjectReferenceList
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.PodSecurity">PodSecurity
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Validation">Validation</a>, 
<a href="#kyverno.io/v2beta1.Validation">Validation</a>)
</p>
<p>
<p>PodSecurity applies exemptions for Kubernetes Pod Security admission
by specifying exclusions for Pod Security Standards controls.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>level</code><br/>
<em>
k8s.io/pod-security-admission/api.Level
</em>
</td>
<td>
<p>Level defines the Pod Security Standard level to be applied to workloads.
Allowed values are privileged, baseline, and restricted.</p>
</td>
</tr>
<tr>
<td>
<code>version</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Version defines the Pod Security Standard versions that Kubernetes supports.
Allowed values are v1.19, v1.20, v1.21, v1.22, v1.23, v1.24, v1.25, v1.26, v1.27, v1.28, v1.29, latest. Defaults to latest.</p>
</td>
</tr>
<tr>
<td>
<code>exclude</code><br/>
<em>
<a href="#kyverno.io/v1.PodSecurityStandard">
[]PodSecurityStandard
</a>
</em>
</td>
<td>
<p>Exclude specifies the Pod Security Standard controls to be excluded.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.PodSecurityStandard">PodSecurityStandard
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.PodSecurity">PodSecurity</a>, 
<a href="#kyverno.io/v2.PolicyExceptionSpec">PolicyExceptionSpec</a>, 
<a href="#kyverno.io/v2beta1.PolicyExceptionSpec">PolicyExceptionSpec</a>)
</p>
<p>
<p>PodSecurityStandard specifies the Pod Security Standard controls to be excluded.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>controlName</code><br/>
<em>
string
</em>
</td>
<td>
<p>ControlName specifies the name of the Pod Security Standard control.
See: <a href="https://kubernetes.io/docs/concepts/security/pod-security-standards/">https://kubernetes.io/docs/concepts/security/pod-security-standards/</a></p>
</td>
</tr>
<tr>
<td>
<code>images</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Images selects matching containers and applies the container level PSS.
Each image is the image name consisting of the registry address, repository, image, and tag.
Empty list matches no containers, PSS checks are applied at the pod level only.
Wildcards (&lsquo;*&rsquo; and &lsquo;?&rsquo;) are allowed. See: <a href="https://kubernetes.io/docs/concepts/containers/images">https://kubernetes.io/docs/concepts/containers/images</a>.</p>
</td>
</tr>
<tr>
<td>
<code>restrictedField</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>RestrictedField selects the field for the given Pod Security Standard control.
When not set, all restricted fields for the control are selected.</p>
</td>
</tr>
<tr>
<td>
<code>values</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Values defines the allowed values that can be excluded.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.PolicyInterface">PolicyInterface
</h3>
<p>
<p>PolicyInterface abstracts the concrete policy type (Policy vs ClusterPolicy)</p>
</p>
<h3 id="kyverno.io/v1.PolicyStatus">PolicyStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ClusterPolicy">ClusterPolicy</a>, 
<a href="#kyverno.io/v1.Policy">Policy</a>, 
<a href="#kyverno.io/v2beta1.ClusterPolicy">ClusterPolicy</a>, 
<a href="#kyverno.io/v2beta1.Policy">Policy</a>)
</p>
<p>
<p>Deprecated. Policy metrics are now available via the &ldquo;/metrics&rdquo; endpoint.
See: <a href="https://kyverno.io/docs/monitoring-kyverno-with-prometheus-metrics/">https://kyverno.io/docs/monitoring-kyverno-with-prometheus-metrics/</a></p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>ready</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated in favor of Conditions</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#condition-v1-meta">
[]Kubernetes meta/v1.Condition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>autogen</code><br/>
<em>
<a href="#kyverno.io/v1.AutogenStatus">
AutogenStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>rulecount</code><br/>
<em>
<a href="#kyverno.io/v1.RuleCountStatus">
RuleCountStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>validatingadmissionpolicy</code><br/>
<em>
<a href="#kyverno.io/v1.ValidatingAdmissionPolicyStatus">
ValidatingAdmissionPolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ValidatingAdmissionPolicy contains status information</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Rekor">Rekor
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.CertificateAttestor">CertificateAttestor</a>, 
<a href="#kyverno.io/v1.KeylessAttestor">KeylessAttestor</a>, 
<a href="#kyverno.io/v1.StaticKeyAttestor">StaticKeyAttestor</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>url</code><br/>
<em>
string
</em>
</td>
<td>
<p>URL is the address of the transparency log. Defaults to the public Rekor log instance <a href="https://rekor.sigstore.dev">https://rekor.sigstore.dev</a>.</p>
</td>
</tr>
<tr>
<td>
<code>pubkey</code><br/>
<em>
string
</em>
</td>
<td>
<p>RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.
If set, this will be used to validate transparency log signatures from a custom Rekor.</p>
</td>
</tr>
<tr>
<td>
<code>ignoreTlog</code><br/>
<em>
bool
</em>
</td>
<td>
<p>IgnoreTlog skips transparency log verification.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.RequestData">RequestData
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.APICall">APICall</a>)
</p>
<p>
<p>RequestData contains the HTTP POST data</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>key</code><br/>
<em>
string
</em>
</td>
<td>
<p>Key is a unique identifier for the data value</p>
</td>
</tr>
<tr>
<td>
<code>value</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<p>Value is the data value</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ResourceDescription">ResourceDescription
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.MatchResources">MatchResources</a>, 
<a href="#kyverno.io/v1.ResourceFilter">ResourceFilter</a>)
</p>
<p>
<p>ResourceDescription contains criteria used to match resources.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>kinds</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Kinds is a list of resource kinds.</p>
</td>
</tr>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Name is the name of the resource. The name supports wildcard characters
&ldquo;*&rdquo; (matches zero or many characters) and &ldquo;?&rdquo; (at least one character).
NOTE: &ldquo;Name&rdquo; is being deprecated in favor of &ldquo;Names&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>names</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Names are the names of the resources. Each name supports wildcard characters
&ldquo;*&rdquo; (matches zero or many characters) and &ldquo;?&rdquo; (at least one character).</p>
</td>
</tr>
<tr>
<td>
<code>namespaces</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Namespaces is a list of namespaces names. Each name supports wildcard characters
&ldquo;*&rdquo; (matches zero or many characters) and &ldquo;?&rdquo; (at least one character).</p>
</td>
</tr>
<tr>
<td>
<code>annotations</code><br/>
<em>
map[string]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
and values support the wildcard characters &ldquo;*&rdquo; (matches zero or many characters) and
&ldquo;?&rdquo; (matches at least one character).</p>
</td>
</tr>
<tr>
<td>
<code>selector</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#labelselector-v1-meta">
Kubernetes meta/v1.LabelSelector
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Selector is a label selector. Label keys and values in <code>matchLabels</code> support the wildcard
characters <code>*</code> (matches zero or many characters) and <code>?</code> (matches one character).
Wildcards allows writing label selectors like [&ldquo;storage.k8s.io/<em>&rdquo;: &ldquo;</em>&rdquo;]. Note that
using [&rdquo;<em>&rdquo; : &ldquo;</em>&rdquo;] matches any key and value but does not match an empty label set.</p>
</td>
</tr>
<tr>
<td>
<code>namespaceSelector</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#labelselector-v1-meta">
Kubernetes meta/v1.LabelSelector
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>NamespaceSelector is a label selector for the resource namespace. Label keys and values
in <code>matchLabels</code> support the wildcard characters <code>*</code> (matches zero or many characters)
and <code>?</code> (matches one character).Wildcards allows writing label selectors like
[&ldquo;storage.k8s.io/<em>&rdquo;: &ldquo;</em>&rdquo;]. Note that using [&rdquo;<em>&rdquo; : &ldquo;</em>&rdquo;] matches any key and value but
does not match an empty label set.</p>
</td>
</tr>
<tr>
<td>
<code>operations</code><br/>
<em>
<a href="#kyverno.io/v1.AdmissionOperation">
[]AdmissionOperation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Operations can contain values [&ldquo;CREATE, &ldquo;UPDATE&rdquo;, &ldquo;CONNECT&rdquo;, &ldquo;DELETE&rdquo;], which are used to match a specific action.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ResourceFilter">ResourceFilter
</h3>
<p>
<p>ResourceFilter allow users to &ldquo;AND&rdquo; or &ldquo;OR&rdquo; between resources</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>UserInfo</code><br/>
<em>
<a href="#kyverno.io/v1.UserInfo">
UserInfo
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>UserInfo contains information about the user performing the operation.</p>
</td>
</tr>
<tr>
<td>
<code>resources</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceDescription">
ResourceDescription
</a>
</em>
</td>
<td>
<p>ResourceDescription contains information about the resource being created or modified.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ResourceFilters">ResourceFilters
(<code>[]github.com/kyverno/kyverno/api/kyverno/v1.ResourceFilter</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.MatchResources">MatchResources</a>, 
<a href="#kyverno.io/v2beta1.MatchResources">MatchResources</a>)
</p>
<p>
<p>ResourceFilters is a slice of ResourceFilter</p>
</p>
<h3 id="kyverno.io/v1.ResourceSpec">ResourceSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.GeneratePattern">GeneratePattern</a>, 
<a href="#kyverno.io/v1.TargetSelector">TargetSelector</a>, 
<a href="#kyverno.io/v1beta1.UpdateRequestSpec">UpdateRequestSpec</a>, 
<a href="#kyverno.io/v1beta1.UpdateRequestStatus">UpdateRequestStatus</a>, 
<a href="#kyverno.io/v2.RuleContext">RuleContext</a>, 
<a href="#kyverno.io/v2.UpdateRequestSpec">UpdateRequestSpec</a>, 
<a href="#kyverno.io/v2.UpdateRequestStatus">UpdateRequestStatus</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>APIVersion specifies resource apiVersion.</p>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
<em>
string
</em>
</td>
<td>
<p>Kind specifies resource kind.</p>
</td>
</tr>
<tr>
<td>
<code>namespace</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Namespace specifies resource namespace.</p>
</td>
</tr>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Name specifies the resource name.</p>
</td>
</tr>
<tr>
<td>
<code>uid</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#uid-types-pkg">
k8s.io/apimachinery/pkg/types.UID
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>UID specifies the resource uid.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Rule">Rule
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.AutogenStatus">AutogenStatus</a>, 
<a href="#kyverno.io/v1.Spec">Spec</a>)
</p>
<p>
<p>Rule defines a validation, mutation, or generation control for matching resources.
Each rules contains a match declaration to select resources, and an optional exclude
declaration to specify which resources to exclude.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is a label to identify the rule, It must be unique within the policy.</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>reportProperties</code><br/>
<em>
map[string]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>ReportProperties are the additional properties from the rule that will be added to the policy report result</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>MatchResources defines when this policy rule should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>
</td>
</tr>
<tr>
<td>
<code>exclude</code><br/>
<em>
<a href="#kyverno.io/v1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ExcludeResources defines when this policy rule should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>
</td>
</tr>
<tr>
<td>
<code>imageExtractors</code><br/>
<em>
<a href="#kyverno.io/v1.ImageExtractorConfigs">
ImageExtractorConfigs
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ImageExtractors defines a mapping from kinds to ImageExtractorConfigs.
This config is only valid for verifyImages rules.</p>
</td>
</tr>
<tr>
<td>
<code>preconditions</code><br/>
<em>
<a href="#kyverno.io/v1.ConditionsWrapper">
ConditionsWrapper
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Preconditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements. A direct list
of conditions (without <code>any</code> or <code>all</code> statements is supported for backwards compatibility but
will be deprecated in the next major release.
See: <a href="https://kyverno.io/docs/writing-policies/preconditions/">https://kyverno.io/docs/writing-policies/preconditions/</a></p>
</td>
</tr>
<tr>
<td>
<code>celPreconditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>CELPreconditions are used to determine if a policy rule should be applied by evaluating a
set of CEL conditions. It can only be used with the validate.cel subrule</p>
</td>
</tr>
<tr>
<td>
<code>mutate</code><br/>
<em>
<a href="#kyverno.io/v1.Mutation">
Mutation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Mutation is used to modify matching resources.</p>
</td>
</tr>
<tr>
<td>
<code>validate</code><br/>
<em>
<a href="#kyverno.io/v1.Validation">
Validation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Validation is used to validate matching resources.</p>
</td>
</tr>
<tr>
<td>
<code>generate</code><br/>
<em>
<a href="#kyverno.io/v1.Generation">
Generation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Generation is used to create new resources.</p>
</td>
</tr>
<tr>
<td>
<code>verifyImages</code><br/>
<em>
<a href="#kyverno.io/v1.ImageVerification">
[]ImageVerification
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>VerifyImages is used to verify image signatures and mutate them to add a digest</p>
</td>
</tr>
<tr>
<td>
<code>skipBackgroundRequests</code><br/>
<em>
bool
</em>
</td>
<td>
<p>SkipBackgroundRequests bypasses admission requests that are sent by the background controller.
The default value is set to &ldquo;true&rdquo;, it must be set to &ldquo;false&rdquo; to apply
generate and mutateExisting rules to those requests.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.RuleCountStatus">RuleCountStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.PolicyStatus">PolicyStatus</a>)
</p>
<p>
<p>RuleCountStatus contains four variables which describes counts for
validate, generate, mutate and verify images rules</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>validate</code><br/>
<em>
int
</em>
</td>
<td>
<p>Count for validate rules in policy</p>
</td>
</tr>
<tr>
<td>
<code>generate</code><br/>
<em>
int
</em>
</td>
<td>
<p>Count for generate rules in policy</p>
</td>
</tr>
<tr>
<td>
<code>mutate</code><br/>
<em>
int
</em>
</td>
<td>
<p>Count for mutate rules in policy</p>
</td>
</tr>
<tr>
<td>
<code>verifyimages</code><br/>
<em>
int
</em>
</td>
<td>
<p>Count for verify image rules in policy</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.SecretReference">SecretReference
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.StaticKeyAttestor">StaticKeyAttestor</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name of the secret. The provided secret must contain a key named cosign.pub.</p>
</td>
</tr>
<tr>
<td>
<code>namespace</code><br/>
<em>
string
</em>
</td>
<td>
<p>Namespace name where the Secret exists.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ServiceCall">ServiceCall
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.APICall">APICall</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>url</code><br/>
<em>
string
</em>
</td>
<td>
<p>URL is the JSON web service URL. A typical form is
<code>https://{service}.{namespace}:{port}/{path}</code>.</p>
</td>
</tr>
<tr>
<td>
<code>headers</code><br/>
<em>
<a href="#kyverno.io/v1.HTTPHeader">
[]HTTPHeader
</a>
</em>
</td>
<td>
<p>Headers is a list of optional HTTP headers to be included in the request.</p>
</td>
</tr>
<tr>
<td>
<code>caBundle</code><br/>
<em>
string
</em>
</td>
<td>
<p>CABundle is a PEM encoded CA bundle which will be used to validate
the server certificate.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Spec">Spec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ClusterPolicy">ClusterPolicy</a>, 
<a href="#kyverno.io/v1.Policy">Policy</a>)
</p>
<p>
<p>Spec contains a list of Rule instances and other policy controls.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>rules</code><br/>
<em>
<a href="#kyverno.io/v1.Rule">
[]Rule
</a>
</em>
</td>
<td>
<p>Rules is a list of Rule instances. A Policy contains multiple rules and
each rule can validate, mutate, or generate resources.</p>
</td>
</tr>
<tr>
<td>
<code>applyRules</code><br/>
<em>
<a href="#kyverno.io/v1.ApplyRulesType">
ApplyRulesType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ApplyRules controls how rules in a policy are applied. Rule are processed in
the order of declaration. When set to <code>One</code> processing stops after a rule has
been applied i.e. the rule matches and results in a pass, fail, or error. When
set to <code>All</code> all rules in the policy are processed. The default is <code>All</code>.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="#kyverno.io/v1.FailurePolicyType">
FailurePolicyType
</a>
</em>
</td>
<td>
<p>Deprecated, use failurePolicy under the webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureAction</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureAction under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureActionOverrides</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureActionOverride">
[]ValidationFailureActionOverride
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureActionOverrides under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>emitWarning</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>EmitWarning enables API response warnings for mutate policy rules or validate policy rules with validationFailureAction set to Audit.
Enabling this option will extend admission request processing times. The default value is &ldquo;false&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>admission</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Admission controls if rules are applied during admission.
Optional. Default value is &ldquo;true&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>background</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Background controls if rules are applied to existing resources during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
<tr>
<td>
<code>schemaValidation</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated.</p>
</td>
</tr>
<tr>
<td>
<code>webhookTimeoutSeconds</code><br/>
<em>
int32
</em>
</td>
<td>
<p>Deprecated, use webhookTimeoutSeconds under webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>mutateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use mutateExistingOnPolicyUpdate under the mutate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use generateExisting instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExisting</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use generateExisting under the generate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>useServerSideApply</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>UseServerSideApply controls whether to use server-side apply for generate rules
If is set to &ldquo;true&rdquo; create &amp; update for generate rules will use apply instead of create/update.
Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#kyverno.io/v1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration specifies the custom configuration for Kubernetes admission webhookconfiguration.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.StaticKeyAttestor">StaticKeyAttestor
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Attestor">Attestor</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>publicKeys</code><br/>
<em>
string
</em>
</td>
<td>
<p>Keys is a set of X.509 public keys used to verify image signatures. The keys can be directly
specified or can be a variable reference to a key specified in a ConfigMap (see
<a href="https://kyverno.io/docs/writing-policies/variables/)">https://kyverno.io/docs/writing-policies/variables/)</a>, or reference a standard Kubernetes Secret
elsewhere in the cluster by specifying it in the format &ldquo;k8s://<namespace>/<secret_name>&rdquo;.
The named Secret must specify a key <code>cosign.pub</code> containing the public key used for
verification, (see <a href="https://github.com/sigstore/cosign/blob/main/KMS.md#kubernetes-secret">https://github.com/sigstore/cosign/blob/main/KMS.md#kubernetes-secret</a>).
When multiple keys are specified each key is processed as a separate staticKey entry
(.attestors[*].entries.keys) within the set of attestors and the count is applied across the keys.</p>
</td>
</tr>
<tr>
<td>
<code>signatureAlgorithm</code><br/>
<em>
string
</em>
</td>
<td>
<p>Deprecated. Use attestor.signatureAlgorithm instead.</p>
</td>
</tr>
<tr>
<td>
<code>kms</code><br/>
<em>
string
</em>
</td>
<td>
<p>KMS provides the URI to the public key stored in a Key Management System. See:
<a href="https://github.com/sigstore/cosign/blob/main/KMS.md">https://github.com/sigstore/cosign/blob/main/KMS.md</a></p>
</td>
</tr>
<tr>
<td>
<code>secret</code><br/>
<em>
<a href="#kyverno.io/v1.SecretReference">
SecretReference
</a>
</em>
</td>
<td>
<p>Reference to a Secret resource that contains a public key</p>
</td>
</tr>
<tr>
<td>
<code>rekor</code><br/>
<em>
<a href="#kyverno.io/v1.Rekor">
Rekor
</a>
</em>
</td>
<td>
<p>Rekor provides configuration for the Rekor transparency log service. If an empty object
is provided the public instance of Rekor (<a href="https://rekor.sigstore.dev">https://rekor.sigstore.dev</a>) is used.</p>
</td>
</tr>
<tr>
<td>
<code>ctlog</code><br/>
<em>
<a href="#kyverno.io/v1.CTLog">
CTLog
</a>
</em>
</td>
<td>
<p>CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate
Timestamps (SCTs). If the value is unset, the default behavior by Cosign is used.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.TargetResourceSpec">TargetResourceSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Mutation">Mutation</a>)
</p>
<p>
<p>TargetResourceSpec defines targets for mutating existing resources.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>TargetSelector</code><br/>
<em>
<a href="#kyverno.io/v1.TargetSelector">
TargetSelector
</a>
</em>
</td>
<td>
<p>TargetSelector contains the ResourceSpec and a label selector to support selecting with labels.</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>preconditions</code><br/>
<em>
<a href="#kyverno.io/v1.ConditionsWrapper">
ConditionsWrapper
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Preconditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements. A direct list
of conditions (without <code>any</code> or <code>all</code> statements is supported for backwards compatibility but
will be deprecated in the next major release.
See: <a href="https://kyverno.io/docs/writing-policies/preconditions/">https://kyverno.io/docs/writing-policies/preconditions/</a></p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.TargetSelector">TargetSelector
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.TargetResourceSpec">TargetResourceSpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>ResourceSpec</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceSpec">
ResourceSpec
</a>
</em>
</td>
<td>
<p>ResourceSpec contains the target resources to load when mutating existing resources.</p>
</td>
</tr>
<tr>
<td>
<code>selector</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#labelselector-v1-meta">
Kubernetes meta/v1.LabelSelector
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Selector allows you to select target resources with their labels.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.UserInfo">UserInfo
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.MatchResources">MatchResources</a>, 
<a href="#kyverno.io/v1.ResourceFilter">ResourceFilter</a>, 
<a href="#kyverno.io/v2beta1.ResourceFilter">ResourceFilter</a>)
</p>
<p>
<p>UserInfo contains information about the user performing the operation.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>roles</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Roles is the list of namespaced role names for the user.</p>
</td>
</tr>
<tr>
<td>
<code>clusterRoles</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>ClusterRoles is the list of cluster-wide role names for the user.</p>
</td>
</tr>
<tr>
<td>
<code>subjects</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#subject-v1-rbac">
[]Kubernetes rbac/v1.Subject
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Subjects is the list of subject names like users, user groups, and service accounts.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ValidateImageVerification">ValidateImageVerification
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ImageVerification">ImageVerification</a>, 
<a href="#kyverno.io/v2beta1.ImageVerification">ImageVerification</a>)
</p>
<p>
<p>ValidateImageVerification checks conditions across multiple image
verification attestations or context entries</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Message specifies a custom message to be displayed on failure.</p>
</td>
</tr>
<tr>
<td>
<code>deny</code><br/>
<em>
<a href="#kyverno.io/v1.Deny">
Deny
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deny defines conditions used to pass or fail a validation rule.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ValidatingAdmissionPolicyStatus">ValidatingAdmissionPolicyStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.PolicyStatus">PolicyStatus</a>)
</p>
<p>
<p>ValidatingAdmissionPolicy contains status information</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>generated</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Generated indicates whether a validating admission policy is generated from the policy or not</p>
</td>
</tr>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<p>Message is a human readable message indicating details about the generation of validating admission policy
It is an empty string when validating admission policy is successfully generated.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Validation">Validation
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Rule">Rule</a>)
</p>
<p>
<p>Validation defines checks to be performed on matching resources.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>failureAction</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>FailureAction defines if a validation policy rule violation should block
the admission review request (Enforce), or allow (Audit) the admission review request
and report an error in a policy report. Optional.
Allowed values are Audit or Enforce.</p>
</td>
</tr>
<tr>
<td>
<code>failureActionOverrides</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureActionOverride">
[]ValidationFailureActionOverride
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>FailureActionOverrides is a Cluster Policy attribute that specifies FailureAction
namespace-wise. It overrides FailureAction for the specified namespaces.</p>
</td>
</tr>
<tr>
<td>
<code>allowExistingViolations</code><br/>
<em>
bool
</em>
</td>
<td>
<p>AllowExistingViolations allows prexisting violating resources to continue violating a policy.</p>
</td>
</tr>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Message specifies a custom message to be displayed on failure.</p>
</td>
</tr>
<tr>
<td>
<code>manifests</code><br/>
<em>
<a href="#kyverno.io/v1.Manifests">
Manifests
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Manifest specifies conditions for manifest verification</p>
</td>
</tr>
<tr>
<td>
<code>foreach</code><br/>
<em>
<a href="#kyverno.io/v1.ForEachValidation">
[]ForEachValidation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ForEach applies validate rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>
</td>
</tr>
<tr>
<td>
<code>pattern</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Pattern specifies an overlay-style pattern used to check resources.</p>
</td>
</tr>
<tr>
<td>
<code>anyPattern</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#json-v1-apiextensions">
Kubernetes apiextensions/v1.JSON
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AnyPattern specifies list of validation patterns. At least one of the patterns
must be satisfied for the validation rule to succeed.</p>
</td>
</tr>
<tr>
<td>
<code>deny</code><br/>
<em>
<a href="#kyverno.io/v1.Deny">
Deny
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deny defines conditions used to pass or fail a validation rule.</p>
</td>
</tr>
<tr>
<td>
<code>podSecurity</code><br/>
<em>
<a href="#kyverno.io/v1.PodSecurity">
PodSecurity
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PodSecurity applies exemptions for Kubernetes Pod Security admission
by specifying exclusions for Pod Security Standards controls.</p>
</td>
</tr>
<tr>
<td>
<code>cel</code><br/>
<em>
<a href="#kyverno.io/v1.CEL">
CEL
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>CEL allows validation checks using the Common Expression Language (<a href="https://kubernetes.io/docs/reference/using-api/cel/">https://kubernetes.io/docs/reference/using-api/cel/</a>).</p>
</td>
</tr>
<tr>
<td>
<code>assert</code><br/>
<em>
github.com/kyverno/kyverno-json/pkg/apis/policy/v1alpha1.Any
</em>
</td>
<td>
<em>(Optional)</em>
<p>Assert defines a kyverno-json assertion tree.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.ValidationFailureAction">ValidationFailureAction
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ImageVerification">ImageVerification</a>, 
<a href="#kyverno.io/v1.Spec">Spec</a>, 
<a href="#kyverno.io/v1.Validation">Validation</a>, 
<a href="#kyverno.io/v1.ValidationFailureActionOverride">ValidationFailureActionOverride</a>, 
<a href="#kyverno.io/v2beta1.ImageVerification">ImageVerification</a>, 
<a href="#kyverno.io/v2beta1.Spec">Spec</a>, 
<a href="#kyverno.io/v2beta1.Validation">Validation</a>)
</p>
<p>
<p>ValidationFailureAction defines the policy validation failure action</p>
</p>
<h3 id="kyverno.io/v1.ValidationFailureActionOverride">ValidationFailureActionOverride
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Spec">Spec</a>, 
<a href="#kyverno.io/v1.Validation">Validation</a>, 
<a href="#kyverno.io/v2beta1.Spec">Spec</a>, 
<a href="#kyverno.io/v2beta1.Validation">Validation</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>action</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>namespaces</code><br/>
<em>
[]string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>namespaceSelector</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#labelselector-v1-meta">
Kubernetes meta/v1.LabelSelector
</a>
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.Variable">Variable
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.ContextEntry">ContextEntry</a>)
</p>
<p>
<p>Variable defines an arbitrary JMESPath context variable that can be defined inline.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>value</code><br/>
<em>
github.com/kyverno/kyverno/api/kyverno.Any
</em>
</td>
<td>
<em>(Optional)</em>
<p>Value is any arbitrary JSON object representable in YAML or JSON form.</p>
</td>
</tr>
<tr>
<td>
<code>jmesPath</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>JMESPath is an optional JMESPath Expression that can be used to
transform the variable.</p>
</td>
</tr>
<tr>
<td>
<code>default</code><br/>
<em>
github.com/kyverno/kyverno/api/kyverno.Any
</em>
</td>
<td>
<em>(Optional)</em>
<p>Default is an optional arbitrary JSON object that the variable may take if the JMESPath
expression evaluates to nil</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1.WebhookConfiguration">WebhookConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1.Spec">Spec</a>, 
<a href="#kyverno.io/v2beta1.Spec">Spec</a>)
</p>
<p>
<p>WebhookConfiguration specifies the configuration for Kubernetes admission webhookconfiguration.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="#kyverno.io/v1.FailurePolicyType">
FailurePolicyType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>FailurePolicy defines how unexpected policy errors and webhook response timeout errors are handled.
Rules within the same policy share the same failure behavior.
This field should not be accessed directly, instead <code>GetFailurePolicy()</code> should be used.
Allowed values are Ignore or Fail. Defaults to Fail.</p>
</td>
</tr>
<tr>
<td>
<code>timeoutSeconds</code><br/>
<em>
int32
</em>
</td>
<td>
<p>TimeoutSeconds specifies the maximum time in seconds allowed to apply this policy.
After the configured time expires, the admission request may fail, or may simply ignore the policy results,
based on the failure policy. The default timeout is 10s, the value must be between 1 and 30 seconds.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchCondition configures admission webhook matchConditions.
Requires Kubernetes 1.27 or later.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h2 id="kyverno.io/v1beta1">kyverno.io/v1beta1</h2>
<p>
<p>Package v1beta1 contains API Schema definitions for the policy v1beta1 API group</p>
</p>
Resource Types:
<ul><li>
<a href="#kyverno.io/v1beta1.UpdateRequest">UpdateRequest</a>
</li></ul>
<hr />
<h3 id="kyverno.io/v1beta1.UpdateRequest">UpdateRequest
</h3>
<p>
<p>UpdateRequest is a request to process mutate and generate rules in background.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v1beta1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>UpdateRequest</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v1beta1.UpdateRequestSpec">
UpdateRequestSpec
</a>
</em>
</td>
<td>
<p>ResourceSpec is the information to identify the trigger resource.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>requestType</code><br/>
<em>
<a href="#kyverno.io/v1beta1.RequestType">
RequestType
</a>
</em>
</td>
<td>
<p>Type represents request type for background processing</p>
</td>
</tr>
<tr>
<td>
<code>policy</code><br/>
<em>
string
</em>
</td>
<td>
<p>Specifies the name of the policy.</p>
</td>
</tr>
<tr>
<td>
<code>rule</code><br/>
<em>
string
</em>
</td>
<td>
<p>Rule is the associate rule name of the current UR.</p>
</td>
</tr>
<tr>
<td>
<code>deleteDownstream</code><br/>
<em>
bool
</em>
</td>
<td>
<p>DeleteDownstream represents whether the downstream needs to be deleted.</p>
</td>
</tr>
<tr>
<td>
<code>synchronize</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Synchronize represents the sync behavior of the corresponding rule
Optional. Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>resource</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceSpec">
ResourceSpec
</a>
</em>
</td>
<td>
<p>ResourceSpec is the information to identify the trigger resource.</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1beta1.UpdateRequestSpecContext">
UpdateRequestSpecContext
</a>
</em>
</td>
<td>
<p>Context &hellip;</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v1beta1.UpdateRequestStatus">
UpdateRequestStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains statistics related to update request.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1beta1.AdmissionRequestInfoObject">AdmissionRequestInfoObject
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1beta1.UpdateRequestSpecContext">UpdateRequestSpecContext</a>)
</p>
<p>
<p>AdmissionRequestInfoObject stores the admission request and operation details</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>admissionRequest</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#admissionrequest-v1-admission">
Kubernetes admission/v1.AdmissionRequest
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>operation</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#operation-v1-admission">
Kubernetes admission/v1.Operation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1beta1.RequestInfo">RequestInfo
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1beta1.UpdateRequestSpecContext">UpdateRequestSpecContext</a>)
</p>
<p>
<p>RequestInfo contains permission info carried in an admission request.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>roles</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Roles is a list of possible role send the request.</p>
</td>
</tr>
<tr>
<td>
<code>clusterRoles</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>ClusterRoles is a list of possible clusterRoles send the request.</p>
</td>
</tr>
<tr>
<td>
<code>userInfo</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#userinfo-v1-authentication">
Kubernetes authentication/v1.UserInfo
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>UserInfo is the userInfo carried in the admission request.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1beta1.RequestType">RequestType
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1beta1.UpdateRequestSpec">UpdateRequestSpec</a>)
</p>
<p>
</p>
<h3 id="kyverno.io/v1beta1.UpdateRequestSpec">UpdateRequestSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1beta1.UpdateRequest">UpdateRequest</a>)
</p>
<p>
<p>UpdateRequestSpec stores the request specification.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>requestType</code><br/>
<em>
<a href="#kyverno.io/v1beta1.RequestType">
RequestType
</a>
</em>
</td>
<td>
<p>Type represents request type for background processing</p>
</td>
</tr>
<tr>
<td>
<code>policy</code><br/>
<em>
string
</em>
</td>
<td>
<p>Specifies the name of the policy.</p>
</td>
</tr>
<tr>
<td>
<code>rule</code><br/>
<em>
string
</em>
</td>
<td>
<p>Rule is the associate rule name of the current UR.</p>
</td>
</tr>
<tr>
<td>
<code>deleteDownstream</code><br/>
<em>
bool
</em>
</td>
<td>
<p>DeleteDownstream represents whether the downstream needs to be deleted.</p>
</td>
</tr>
<tr>
<td>
<code>synchronize</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Synchronize represents the sync behavior of the corresponding rule
Optional. Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>resource</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceSpec">
ResourceSpec
</a>
</em>
</td>
<td>
<p>ResourceSpec is the information to identify the trigger resource.</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1beta1.UpdateRequestSpecContext">
UpdateRequestSpecContext
</a>
</em>
</td>
<td>
<p>Context &hellip;</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1beta1.UpdateRequestSpecContext">UpdateRequestSpecContext
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1beta1.UpdateRequestSpec">UpdateRequestSpec</a>)
</p>
<p>
<p>UpdateRequestSpecContext stores the context to be shared.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>userInfo</code><br/>
<em>
<a href="#kyverno.io/v1beta1.RequestInfo">
RequestInfo
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>admissionRequestInfo</code><br/>
<em>
<a href="#kyverno.io/v1beta1.AdmissionRequestInfoObject">
AdmissionRequestInfoObject
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v1beta1.UpdateRequestState">UpdateRequestState
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1beta1.UpdateRequestStatus">UpdateRequestStatus</a>)
</p>
<p>
<p>UpdateRequestState defines the state of request.</p>
</p>
<h3 id="kyverno.io/v1beta1.UpdateRequestStatus">UpdateRequestStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v1beta1.UpdateRequest">UpdateRequest</a>)
</p>
<p>
<p>UpdateRequestStatus defines the observed state of UpdateRequest</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>handler</code><br/>
<em>
string
</em>
</td>
<td>
<p>Deprecated</p>
</td>
</tr>
<tr>
<td>
<code>state</code><br/>
<em>
<a href="#kyverno.io/v1beta1.UpdateRequestState">
UpdateRequestState
</a>
</em>
</td>
<td>
<p>State represents state of the update request.</p>
</td>
</tr>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Specifies request status message.</p>
</td>
</tr>
<tr>
<td>
<code>generatedResources</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceSpec">
[]ResourceSpec
</a>
</em>
</td>
<td>
<p>This will track the resources that are updated by the generate Policy.
Will be used during clean up resources.</p>
</td>
</tr>
<tr>
<td>
<code>retryCount</code><br/>
<em>
int
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h2 id="kyverno.io/v2">kyverno.io/v2</h2>
<p>
</p>
Resource Types:
<ul><li>
<a href="#kyverno.io/v2.CleanupPolicy">CleanupPolicy</a>
</li><li>
<a href="#kyverno.io/v2.ClusterCleanupPolicy">ClusterCleanupPolicy</a>
</li><li>
<a href="#kyverno.io/v2.PolicyException">PolicyException</a>
</li><li>
<a href="#kyverno.io/v2.UpdateRequest">UpdateRequest</a>
</li></ul>
<hr />
<h3 id="kyverno.io/v2.CleanupPolicy">CleanupPolicy
</h3>
<p>
<p>CleanupPolicy defines a rule for resource cleanup.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v2
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>CleanupPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v2.CleanupPolicySpec">
CleanupPolicySpec
</a>
</em>
</td>
<td>
<p>Spec declares policy behaviors.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>MatchResources defines when cleanuppolicy should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>
</td>
</tr>
<tr>
<td>
<code>exclude</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ExcludeResources defines when cleanuppolicy should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>
</td>
</tr>
<tr>
<td>
<code>schedule</code><br/>
<em>
string
</em>
</td>
<td>
<p>The schedule in Cron format</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions defines the conditions used to select the resources which will be cleaned up.</p>
</td>
</tr>
<tr>
<td>
<code>deletionPropagationPolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#deletionpropagation-v1-meta">
Kubernetes meta/v1.DeletionPropagation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v2.CleanupPolicyStatus">
CleanupPolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.ClusterCleanupPolicy">ClusterCleanupPolicy
</h3>
<p>
<p>ClusterCleanupPolicy defines rule for resource cleanup.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v2
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>ClusterCleanupPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v2.CleanupPolicySpec">
CleanupPolicySpec
</a>
</em>
</td>
<td>
<p>Spec declares policy behaviors.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>MatchResources defines when cleanuppolicy should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>
</td>
</tr>
<tr>
<td>
<code>exclude</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ExcludeResources defines when cleanuppolicy should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>
</td>
</tr>
<tr>
<td>
<code>schedule</code><br/>
<em>
string
</em>
</td>
<td>
<p>The schedule in Cron format</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions defines the conditions used to select the resources which will be cleaned up.</p>
</td>
</tr>
<tr>
<td>
<code>deletionPropagationPolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#deletionpropagation-v1-meta">
Kubernetes meta/v1.DeletionPropagation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v2.CleanupPolicyStatus">
CleanupPolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.PolicyException">PolicyException
</h3>
<p>
<p>PolicyException declares resources to be excluded from specified policies.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v2
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>PolicyException</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v2.PolicyExceptionSpec">
PolicyExceptionSpec
</a>
</em>
</td>
<td>
<p>Spec declares policy exception behaviors.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>background</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Background controls if exceptions are applied to existing policies during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>Match defines match clause used to check if a resource applies to the exception</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions are used to determine if a resource applies to the exception by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.</p>
</td>
</tr>
<tr>
<td>
<code>exceptions</code><br/>
<em>
<a href="#kyverno.io/v2.Exception">
[]Exception
</a>
</em>
</td>
<td>
<p>Exceptions is a list policy/rules to be excluded</p>
</td>
</tr>
<tr>
<td>
<code>podSecurity</code><br/>
<em>
<a href="#kyverno.io/v1.PodSecurityStandard">
[]PodSecurityStandard
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PodSecurity specifies the Pod Security Standard controls to be excluded.
Applicable only to policies that have validate.podSecurity subrule.</p>
</td>
</tr>
</table>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.UpdateRequest">UpdateRequest
</h3>
<p>
<p>UpdateRequest is a request to process mutate and generate rules in background.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v2
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>UpdateRequest</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v2.UpdateRequestSpec">
UpdateRequestSpec
</a>
</em>
</td>
<td>
<p>ResourceSpec is the information to identify the trigger resource.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>requestType</code><br/>
<em>
<a href="#kyverno.io/v2.RequestType">
RequestType
</a>
</em>
</td>
<td>
<p>Type represents request type for background processing</p>
</td>
</tr>
<tr>
<td>
<code>policy</code><br/>
<em>
string
</em>
</td>
<td>
<p>Specifies the name of the policy.</p>
</td>
</tr>
<tr>
<td>
<code>ruleContext</code><br/>
<em>
<a href="#kyverno.io/v2.RuleContext">
[]RuleContext
</a>
</em>
</td>
<td>
<p>RuleContext is the associate context to apply rules.
optional</p>
</td>
</tr>
<tr>
<td>
<code>rule</code><br/>
<em>
string
</em>
</td>
<td>
<p>Rule is the associate rule name of the current UR.</p>
</td>
</tr>
<tr>
<td>
<code>deleteDownstream</code><br/>
<em>
bool
</em>
</td>
<td>
<p>DeleteDownstream represents whether the downstream needs to be deleted.
Deprecated</p>
</td>
</tr>
<tr>
<td>
<code>synchronize</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Synchronize represents the sync behavior of the corresponding rule
Optional. Defaults to &ldquo;false&rdquo; if not specified.
Deprecated, will be removed in 1.14.</p>
</td>
</tr>
<tr>
<td>
<code>resource</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceSpec">
ResourceSpec
</a>
</em>
</td>
<td>
<p>ResourceSpec is the information to identify the trigger resource.</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v2.UpdateRequestSpecContext">
UpdateRequestSpecContext
</a>
</em>
</td>
<td>
<p>Context represents admission request context.
It is used upon admission review only and is shared across rules within the same UR.</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v2.UpdateRequestStatus">
UpdateRequestStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains statistics related to update request.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.AdmissionRequestInfoObject">AdmissionRequestInfoObject
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.UpdateRequestSpecContext">UpdateRequestSpecContext</a>)
</p>
<p>
<p>AdmissionRequestInfoObject stores the admission request and operation details</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>admissionRequest</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#admissionrequest-v1-admission">
Kubernetes admission/v1.AdmissionRequest
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>operation</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#operation-v1-admission">
Kubernetes admission/v1.Operation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.AnyAllConditions">AnyAllConditions
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.CleanupPolicySpec">CleanupPolicySpec</a>, 
<a href="#kyverno.io/v2.PolicyExceptionSpec">PolicyExceptionSpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>any</code><br/>
<em>
<a href="#kyverno.io/v2.Condition">
[]Condition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AnyConditions enable variable-based conditional rule execution. This is useful for
finer control of when an rule is applied. A condition can reference object data
using JMESPath notation.
Here, at least one of the conditions need to pass.</p>
</td>
</tr>
<tr>
<td>
<code>all</code><br/>
<em>
<a href="#kyverno.io/v2.Condition">
[]Condition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AllConditions enable variable-based conditional rule execution. This is useful for
finer control of when an rule is applied. A condition can reference object data
using JMESPath notation.
Here, all of the conditions need to pass.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.CleanupPolicyInterface">CleanupPolicyInterface
</h3>
<p>
<p>CleanupPolicyInterface abstracts the concrete policy type (CleanupPolicy vs ClusterCleanupPolicy)</p>
</p>
<h3 id="kyverno.io/v2.CleanupPolicySpec">CleanupPolicySpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.CleanupPolicy">CleanupPolicy</a>, 
<a href="#kyverno.io/v2.ClusterCleanupPolicy">ClusterCleanupPolicy</a>)
</p>
<p>
<p>CleanupPolicySpec stores specifications for selecting resources that the user needs to delete
and schedule when the matching resources needs deleted.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>MatchResources defines when cleanuppolicy should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>
</td>
</tr>
<tr>
<td>
<code>exclude</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ExcludeResources defines when cleanuppolicy should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>
</td>
</tr>
<tr>
<td>
<code>schedule</code><br/>
<em>
string
</em>
</td>
<td>
<p>The schedule in Cron format</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions defines the conditions used to select the resources which will be cleaned up.</p>
</td>
</tr>
<tr>
<td>
<code>deletionPropagationPolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#deletionpropagation-v1-meta">
Kubernetes meta/v1.DeletionPropagation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.CleanupPolicyStatus">CleanupPolicyStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.CleanupPolicy">CleanupPolicy</a>, 
<a href="#kyverno.io/v2.ClusterCleanupPolicy">ClusterCleanupPolicy</a>)
</p>
<p>
<p>CleanupPolicyStatus stores the status of the policy.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#condition-v1-meta">
[]Kubernetes meta/v1.Condition
</a>
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>lastExecutionTime</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#time-v1-meta">
Kubernetes meta/v1.Time
</a>
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.Condition">Condition
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.AnyAllConditions">AnyAllConditions</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>key</code><br/>
<em>
github.com/kyverno/kyverno/api/kyverno.Any
</em>
</td>
<td>
<p>Key is the context entry (using JMESPath) for conditional rule evaluation.</p>
</td>
</tr>
<tr>
<td>
<code>operator</code><br/>
<em>
<a href="#kyverno.io/v2.ConditionOperator">
ConditionOperator
</a>
</em>
</td>
<td>
<p>Operator is the conditional operation to perform. Valid operators are:
Equals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,
GreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,
DurationLessThanOrEquals, DurationLessThan</p>
</td>
</tr>
<tr>
<td>
<code>value</code><br/>
<em>
github.com/kyverno/kyverno/api/kyverno.Any
</em>
</td>
<td>
<p>Value is the conditional value, or set of values. The values can be fixed set
or can be variables declared using JMESPath.</p>
</td>
</tr>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<p>Message is an optional display message</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.ConditionOperator">ConditionOperator
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.Condition">Condition</a>)
</p>
<p>
<p>ConditionOperator is the operation performed on condition key and value.</p>
</p>
<h3 id="kyverno.io/v2.Exception">Exception
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.PolicyExceptionSpec">PolicyExceptionSpec</a>)
</p>
<p>
<p>Exception stores infos about a policy and rules</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>policyName</code><br/>
<em>
string
</em>
</td>
<td>
<p>PolicyName identifies the policy to which the exception is applied.
The policy name uses the format <namespace>/<name> unless it
references a ClusterPolicy.</p>
</td>
</tr>
<tr>
<td>
<code>ruleNames</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>RuleNames identifies the rules to which the exception is applied.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.PolicyExceptionSpec">PolicyExceptionSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.PolicyException">PolicyException</a>)
</p>
<p>
<p>PolicyExceptionSpec stores policy exception spec</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>background</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Background controls if exceptions are applied to existing policies during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>Match defines match clause used to check if a resource applies to the exception</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions are used to determine if a resource applies to the exception by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.</p>
</td>
</tr>
<tr>
<td>
<code>exceptions</code><br/>
<em>
<a href="#kyverno.io/v2.Exception">
[]Exception
</a>
</em>
</td>
<td>
<p>Exceptions is a list policy/rules to be excluded</p>
</td>
</tr>
<tr>
<td>
<code>podSecurity</code><br/>
<em>
<a href="#kyverno.io/v1.PodSecurityStandard">
[]PodSecurityStandard
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PodSecurity specifies the Pod Security Standard controls to be excluded.
Applicable only to policies that have validate.podSecurity subrule.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.RequestInfo">RequestInfo
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.UpdateRequestSpecContext">UpdateRequestSpecContext</a>)
</p>
<p>
<p>RequestInfo contains permission info carried in an admission request.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>roles</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Roles is a list of possible role send the request.</p>
</td>
</tr>
<tr>
<td>
<code>clusterRoles</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>ClusterRoles is a list of possible clusterRoles send the request.</p>
</td>
</tr>
<tr>
<td>
<code>userInfo</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#userinfo-v1-authentication">
Kubernetes authentication/v1.UserInfo
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>UserInfo is the userInfo carried in the admission request.</p>
</td>
</tr>
<tr>
<td>
<code>synchronize</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>DryRun indicates that modifications will definitely not be persisted for this request.
Defaults to false.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.RequestType">RequestType
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.UpdateRequestSpec">UpdateRequestSpec</a>)
</p>
<p>
</p>
<h3 id="kyverno.io/v2.RuleContext">RuleContext
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.UpdateRequestSpec">UpdateRequestSpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>rule</code><br/>
<em>
string
</em>
</td>
<td>
<p>Rule is the associate rule name of the current UR.</p>
</td>
</tr>
<tr>
<td>
<code>deleteDownstream</code><br/>
<em>
bool
</em>
</td>
<td>
<p>DeleteDownstream represents whether the downstream needs to be deleted.</p>
</td>
</tr>
<tr>
<td>
<code>synchronize</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Synchronize represents the sync behavior of the corresponding rule
Optional. Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>trigger</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceSpec">
ResourceSpec
</a>
</em>
</td>
<td>
<p>ResourceSpec is the information to identify the trigger resource.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.UpdateRequestSpec">UpdateRequestSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.UpdateRequest">UpdateRequest</a>)
</p>
<p>
<p>UpdateRequestSpec stores the request specification.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>requestType</code><br/>
<em>
<a href="#kyverno.io/v2.RequestType">
RequestType
</a>
</em>
</td>
<td>
<p>Type represents request type for background processing</p>
</td>
</tr>
<tr>
<td>
<code>policy</code><br/>
<em>
string
</em>
</td>
<td>
<p>Specifies the name of the policy.</p>
</td>
</tr>
<tr>
<td>
<code>ruleContext</code><br/>
<em>
<a href="#kyverno.io/v2.RuleContext">
[]RuleContext
</a>
</em>
</td>
<td>
<p>RuleContext is the associate context to apply rules.
optional</p>
</td>
</tr>
<tr>
<td>
<code>rule</code><br/>
<em>
string
</em>
</td>
<td>
<p>Rule is the associate rule name of the current UR.</p>
</td>
</tr>
<tr>
<td>
<code>deleteDownstream</code><br/>
<em>
bool
</em>
</td>
<td>
<p>DeleteDownstream represents whether the downstream needs to be deleted.
Deprecated</p>
</td>
</tr>
<tr>
<td>
<code>synchronize</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Synchronize represents the sync behavior of the corresponding rule
Optional. Defaults to &ldquo;false&rdquo; if not specified.
Deprecated, will be removed in 1.14.</p>
</td>
</tr>
<tr>
<td>
<code>resource</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceSpec">
ResourceSpec
</a>
</em>
</td>
<td>
<p>ResourceSpec is the information to identify the trigger resource.</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v2.UpdateRequestSpecContext">
UpdateRequestSpecContext
</a>
</em>
</td>
<td>
<p>Context represents admission request context.
It is used upon admission review only and is shared across rules within the same UR.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.UpdateRequestSpecContext">UpdateRequestSpecContext
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.UpdateRequestSpec">UpdateRequestSpec</a>)
</p>
<p>
<p>UpdateRequestSpecContext stores the context to be shared.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>userInfo</code><br/>
<em>
<a href="#kyverno.io/v2.RequestInfo">
RequestInfo
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>admissionRequestInfo</code><br/>
<em>
<a href="#kyverno.io/v2.AdmissionRequestInfoObject">
AdmissionRequestInfoObject
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2.UpdateRequestState">UpdateRequestState
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.UpdateRequestStatus">UpdateRequestStatus</a>)
</p>
<p>
<p>UpdateRequestState defines the state of request.</p>
</p>
<h3 id="kyverno.io/v2.UpdateRequestStatus">UpdateRequestStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.UpdateRequest">UpdateRequest</a>)
</p>
<p>
<p>UpdateRequestStatus defines the observed state of UpdateRequest</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>state</code><br/>
<em>
<a href="#kyverno.io/v2.UpdateRequestState">
UpdateRequestState
</a>
</em>
</td>
<td>
<p>State represents state of the update request.</p>
</td>
</tr>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Specifies request status message.</p>
</td>
</tr>
<tr>
<td>
<code>generatedResources</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceSpec">
[]ResourceSpec
</a>
</em>
</td>
<td>
<p>This will track the resources that are updated by the generate Policy.
Will be used during clean up resources.</p>
</td>
</tr>
<tr>
<td>
<code>retryCount</code><br/>
<em>
int
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h2 id="kyverno.io/v2alpha1">kyverno.io/v2alpha1</h2>
<p>
</p>
Resource Types:
<ul><li>
<a href="#kyverno.io/v2alpha1.GlobalContextEntry">GlobalContextEntry</a>
</li></ul>
<hr />
<h3 id="kyverno.io/v2alpha1.GlobalContextEntry">GlobalContextEntry
</h3>
<p>
<p>GlobalContextEntry declares resources to be cached.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v2alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>GlobalContextEntry</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v2alpha1.GlobalContextEntrySpec">
GlobalContextEntrySpec
</a>
</em>
</td>
<td>
<p>Spec declares policy exception behaviors.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>kubernetesResource</code><br/>
<em>
<a href="#kyverno.io/v2alpha1.KubernetesResource">
KubernetesResource
</a>
</em>
</td>
<td>
<p>Stores a list of Kubernetes resources which will be cached.
Mutually exclusive with APICall.</p>
</td>
</tr>
<tr>
<td>
<code>apiCall</code><br/>
<em>
<a href="#kyverno.io/v2alpha1.ExternalAPICall">
ExternalAPICall
</a>
</em>
</td>
<td>
<p>Stores results from an API call which will be cached.
Mutually exclusive with KubernetesResource.
This can be used to make calls to external (non-Kubernetes API server) services.
It can also be used to make calls to the Kubernetes API server in such cases:
1. A POST is needed to create a resource.
2. Finer-grained control is needed. Example: To restrict the number of resources cached.</p>
</td>
</tr>
<tr>
<td>
<code>projections</code><br/>
<em>
<a href="#kyverno.io/v2alpha1.GlobalContextEntryProjection">
[]GlobalContextEntryProjection
</a>
</em>
</td>
<td>
<p>Projections defines the list of JMESPath expressions to extract values from the cached resource.</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v2alpha1.GlobalContextEntryStatus">
GlobalContextEntryStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains globalcontextentry runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2alpha1.ExternalAPICall">ExternalAPICall
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2alpha1.GlobalContextEntrySpec">GlobalContextEntrySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>APICall</code><br/>
<em>
<a href="#kyverno.io/v1.APICall">
APICall
</a>
</em>
</td>
<td>
<p>
(Members of <code>APICall</code> are embedded into this type.)
</p>
</td>
</tr>
<tr>
<td>
<code>refreshInterval</code><br/>
<em>
<a href="https://godoc.org/k8s.io/apimachinery/pkg/apis/meta/v1#Duration">
Kubernetes meta/v1.Duration
</a>
</em>
</td>
<td>
<p>RefreshInterval defines the interval in duration at which to poll the APICall.
The duration is a sequence of decimal numbers, each with optional fraction and a unit suffix,
such as &ldquo;300ms&rdquo;, &ldquo;1.5h&rdquo; or &ldquo;2h45m&rdquo;. Valid time units are &ldquo;ns&rdquo;, &ldquo;us&rdquo; (or &ldquo;µs&rdquo;), &ldquo;ms&rdquo;, &ldquo;s&rdquo;, &ldquo;m&rdquo;, &ldquo;h&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>retryLimit</code><br/>
<em>
int
</em>
</td>
<td>
<em>(Optional)</em>
<p>RetryLimit defines the number of times the APICall should be retried in case of failure.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2alpha1.GlobalContextEntryProjection">GlobalContextEntryProjection
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2alpha1.GlobalContextEntrySpec">GlobalContextEntrySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the name to use for the extracted value in the context.</p>
</td>
</tr>
<tr>
<td>
<code>jmesPath</code><br/>
<em>
string
</em>
</td>
<td>
<p>JMESPath is the JMESPath expression to extract the value from the cached resource.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2alpha1.GlobalContextEntrySpec">GlobalContextEntrySpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2alpha1.GlobalContextEntry">GlobalContextEntry</a>)
</p>
<p>
<p>GlobalContextEntrySpec stores policy exception spec</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>kubernetesResource</code><br/>
<em>
<a href="#kyverno.io/v2alpha1.KubernetesResource">
KubernetesResource
</a>
</em>
</td>
<td>
<p>Stores a list of Kubernetes resources which will be cached.
Mutually exclusive with APICall.</p>
</td>
</tr>
<tr>
<td>
<code>apiCall</code><br/>
<em>
<a href="#kyverno.io/v2alpha1.ExternalAPICall">
ExternalAPICall
</a>
</em>
</td>
<td>
<p>Stores results from an API call which will be cached.
Mutually exclusive with KubernetesResource.
This can be used to make calls to external (non-Kubernetes API server) services.
It can also be used to make calls to the Kubernetes API server in such cases:
1. A POST is needed to create a resource.
2. Finer-grained control is needed. Example: To restrict the number of resources cached.</p>
</td>
</tr>
<tr>
<td>
<code>projections</code><br/>
<em>
<a href="#kyverno.io/v2alpha1.GlobalContextEntryProjection">
[]GlobalContextEntryProjection
</a>
</em>
</td>
<td>
<p>Projections defines the list of JMESPath expressions to extract values from the cached resource.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2alpha1.GlobalContextEntryStatus">GlobalContextEntryStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2alpha1.GlobalContextEntry">GlobalContextEntry</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>ready</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated in favor of Conditions</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#condition-v1-meta">
[]Kubernetes meta/v1.Condition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>lastRefreshTime</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#time-v1-meta">
Kubernetes meta/v1.Time
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Indicates the time when the globalcontextentry was last refreshed successfully for the API Call</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2alpha1.KubernetesResource">KubernetesResource
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2alpha1.GlobalContextEntrySpec">GlobalContextEntrySpec</a>)
</p>
<p>
<p>KubernetesResource stores infos about kubernetes resource that should be cached</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>group</code><br/>
<em>
string
</em>
</td>
<td>
<p>Group defines the group of the resource.</p>
</td>
</tr>
<tr>
<td>
<code>version</code><br/>
<em>
string
</em>
</td>
<td>
<p>Version defines the version of the resource.</p>
</td>
</tr>
<tr>
<td>
<code>resource</code><br/>
<em>
string
</em>
</td>
<td>
<p>Resource defines the type of the resource.
Requires the pluralized form of the resource kind in lowercase. (Ex., &ldquo;deployments&rdquo;)</p>
</td>
</tr>
<tr>
<td>
<code>namespace</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Namespace defines the namespace of the resource. Leave empty for cluster scoped resources.
If left empty for namespaced resources, all resources from all namespaces will be cached.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h2 id="kyverno.io/v2beta1">kyverno.io/v2beta1</h2>
Resource Types:
<ul><li>
<a href="#kyverno.io/v2beta1.CleanupPolicy">CleanupPolicy</a>
</li><li>
<a href="#kyverno.io/v2beta1.ClusterCleanupPolicy">ClusterCleanupPolicy</a>
</li><li>
<a href="#kyverno.io/v2beta1.ClusterPolicy">ClusterPolicy</a>
</li><li>
<a href="#kyverno.io/v2beta1.Policy">Policy</a>
</li><li>
<a href="#kyverno.io/v2beta1.PolicyException">PolicyException</a>
</li></ul>
<hr />
<h3 id="kyverno.io/v2beta1.CleanupPolicy">CleanupPolicy
</h3>
<p>
<p>CleanupPolicy defines a rule for resource cleanup.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v2beta1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>CleanupPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v2beta1.CleanupPolicySpec">
CleanupPolicySpec
</a>
</em>
</td>
<td>
<p>Spec declares policy behaviors.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>MatchResources defines when cleanuppolicy should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>
</td>
</tr>
<tr>
<td>
<code>exclude</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ExcludeResources defines when cleanuppolicy should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>
</td>
</tr>
<tr>
<td>
<code>schedule</code><br/>
<em>
string
</em>
</td>
<td>
<p>The schedule in Cron format</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2beta1.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions defines the conditions used to select the resources which will be cleaned up.</p>
</td>
</tr>
<tr>
<td>
<code>deletionPropagationPolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#deletionpropagation-v1-meta">
Kubernetes meta/v1.DeletionPropagation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v2beta1.CleanupPolicyStatus">
CleanupPolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.ClusterCleanupPolicy">ClusterCleanupPolicy
</h3>
<p>
<p>ClusterCleanupPolicy defines rule for resource cleanup.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v2beta1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>ClusterCleanupPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v2beta1.CleanupPolicySpec">
CleanupPolicySpec
</a>
</em>
</td>
<td>
<p>Spec declares policy behaviors.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>MatchResources defines when cleanuppolicy should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>
</td>
</tr>
<tr>
<td>
<code>exclude</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ExcludeResources defines when cleanuppolicy should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>
</td>
</tr>
<tr>
<td>
<code>schedule</code><br/>
<em>
string
</em>
</td>
<td>
<p>The schedule in Cron format</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2beta1.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions defines the conditions used to select the resources which will be cleaned up.</p>
</td>
</tr>
<tr>
<td>
<code>deletionPropagationPolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#deletionpropagation-v1-meta">
Kubernetes meta/v1.DeletionPropagation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v2beta1.CleanupPolicyStatus">
CleanupPolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.ClusterPolicy">ClusterPolicy
</h3>
<p>
<p>ClusterPolicy declares validation, mutation, and generation behaviors for matching resources.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v2beta1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>ClusterPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Spec">
Spec
</a>
</em>
</td>
<td>
<p>Spec declares policy behaviors.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>rules</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Rule">
[]Rule
</a>
</em>
</td>
<td>
<p>Rules is a list of Rule instances. A Policy contains multiple rules and
each rule can validate, mutate, or generate resources.</p>
</td>
</tr>
<tr>
<td>
<code>applyRules</code><br/>
<em>
<a href="#kyverno.io/v1.ApplyRulesType">
ApplyRulesType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ApplyRules controls how rules in a policy are applied. Rule are processed in
the order of declaration. When set to <code>One</code> processing stops after a rule has
been applied i.e. the rule matches and results in a pass, fail, or error. When
set to <code>All</code> all rules in the policy are processed. The default is <code>All</code>.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="#kyverno.io/v1.FailurePolicyType">
FailurePolicyType
</a>
</em>
</td>
<td>
<p>Deprecated, use failurePolicy under the webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureAction</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureAction under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureActionOverrides</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureActionOverride">
[]ValidationFailureActionOverride
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureActionOverrides under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>emitWarning</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>EmitWarning enables API response warnings for mutate policy rules or validate policy rules with validationFailureAction set to Audit.
Enabling this option will extend admission request processing times. The default value is &ldquo;false&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>admission</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Admission controls if rules are applied during admission.
Optional. Default value is &ldquo;true&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>background</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Background controls if rules are applied to existing resources during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
<tr>
<td>
<code>schemaValidation</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated.</p>
</td>
</tr>
<tr>
<td>
<code>webhookTimeoutSeconds</code><br/>
<em>
int32
</em>
</td>
<td>
<p>Deprecated, use webhookTimeoutSeconds under webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>mutateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use mutateExistingOnPolicyUpdate under the mutate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use generateExisting instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExisting</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated, use generateExisting under the generate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>useServerSideApply</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>UseServerSideApply controls whether to use server-side apply for generate rules
If is set to &ldquo;true&rdquo; create &amp; update for generate rules will use apply instead of create/update.
Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#kyverno.io/v1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration specifies the custom configuration for Kubernetes admission webhookconfiguration.</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v1.PolicyStatus">
PolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.Policy">Policy
</h3>
<p>
<p>Policy declares validation, mutation, and generation behaviors for matching resources.
See: <a href="https://kyverno.io/docs/writing-policies/">https://kyverno.io/docs/writing-policies/</a> for more information.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v2beta1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>Policy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Spec">
Spec
</a>
</em>
</td>
<td>
<p>Spec defines policy behaviors and contains one or more rules.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>rules</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Rule">
[]Rule
</a>
</em>
</td>
<td>
<p>Rules is a list of Rule instances. A Policy contains multiple rules and
each rule can validate, mutate, or generate resources.</p>
</td>
</tr>
<tr>
<td>
<code>applyRules</code><br/>
<em>
<a href="#kyverno.io/v1.ApplyRulesType">
ApplyRulesType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ApplyRules controls how rules in a policy are applied. Rule are processed in
the order of declaration. When set to <code>One</code> processing stops after a rule has
been applied i.e. the rule matches and results in a pass, fail, or error. When
set to <code>All</code> all rules in the policy are processed. The default is <code>All</code>.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="#kyverno.io/v1.FailurePolicyType">
FailurePolicyType
</a>
</em>
</td>
<td>
<p>Deprecated, use failurePolicy under the webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureAction</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureAction under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureActionOverrides</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureActionOverride">
[]ValidationFailureActionOverride
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureActionOverrides under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>emitWarning</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>EmitWarning enables API response warnings for mutate policy rules or validate policy rules with validationFailureAction set to Audit.
Enabling this option will extend admission request processing times. The default value is &ldquo;false&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>admission</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Admission controls if rules are applied during admission.
Optional. Default value is &ldquo;true&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>background</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Background controls if rules are applied to existing resources during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
<tr>
<td>
<code>schemaValidation</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated.</p>
</td>
</tr>
<tr>
<td>
<code>webhookTimeoutSeconds</code><br/>
<em>
int32
</em>
</td>
<td>
<p>Deprecated, use webhookTimeoutSeconds under webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>mutateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use mutateExistingOnPolicyUpdate under the mutate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use generateExisting instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExisting</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated, use generateExisting under the generate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>useServerSideApply</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>UseServerSideApply controls whether to use server-side apply for generate rules
If is set to &ldquo;true&rdquo; create &amp; update for generate rules will use apply instead of create/update.
Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#kyverno.io/v1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration specifies the custom configuration for Kubernetes admission webhookconfiguration.</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#kyverno.io/v1.PolicyStatus">
PolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.PolicyException">PolicyException
</h3>
<p>
<p>PolicyException declares resources to be excluded from specified policies.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
kyverno.io/v2beta1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>PolicyException</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#kyverno.io/v2beta1.PolicyExceptionSpec">
PolicyExceptionSpec
</a>
</em>
</td>
<td>
<p>Spec declares policy exception behaviors.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>background</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Background controls if exceptions are applied to existing policies during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>Match defines match clause used to check if a resource applies to the exception</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2beta1.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions are used to determine if a resource applies to the exception by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.</p>
</td>
</tr>
<tr>
<td>
<code>exceptions</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Exception">
[]Exception
</a>
</em>
</td>
<td>
<p>Exceptions is a list policy/rules to be excluded</p>
</td>
</tr>
<tr>
<td>
<code>podSecurity</code><br/>
<em>
<a href="#kyverno.io/v1.PodSecurityStandard">
[]PodSecurityStandard
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PodSecurity specifies the Pod Security Standard controls to be excluded.
Applicable only to policies that have validate.podSecurity subrule.</p>
</td>
</tr>
</table>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.AnyAllConditions">AnyAllConditions
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.CleanupPolicySpec">CleanupPolicySpec</a>, 
<a href="#kyverno.io/v2beta1.Deny">Deny</a>, 
<a href="#kyverno.io/v2beta1.PolicyExceptionSpec">PolicyExceptionSpec</a>, 
<a href="#kyverno.io/v2beta1.Rule">Rule</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>any</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Condition">
[]Condition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AnyConditions enable variable-based conditional rule execution. This is useful for
finer control of when an rule is applied. A condition can reference object data
using JMESPath notation.
Here, at least one of the conditions need to pass.</p>
</td>
</tr>
<tr>
<td>
<code>all</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Condition">
[]Condition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AllConditions enable variable-based conditional rule execution. This is useful for
finer control of when an rule is applied. A condition can reference object data
using JMESPath notation.
Here, all of the conditions need to pass.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.CleanupPolicySpec">CleanupPolicySpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.CleanupPolicy">CleanupPolicy</a>, 
<a href="#kyverno.io/v2beta1.ClusterCleanupPolicy">ClusterCleanupPolicy</a>)
</p>
<p>
<p>CleanupPolicySpec stores specifications for selecting resources that the user needs to delete
and schedule when the matching resources needs deleted.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>MatchResources defines when cleanuppolicy should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>
</td>
</tr>
<tr>
<td>
<code>exclude</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ExcludeResources defines when cleanuppolicy should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>
</td>
</tr>
<tr>
<td>
<code>schedule</code><br/>
<em>
string
</em>
</td>
<td>
<p>The schedule in Cron format</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2beta1.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions defines the conditions used to select the resources which will be cleaned up.</p>
</td>
</tr>
<tr>
<td>
<code>deletionPropagationPolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#deletionpropagation-v1-meta">
Kubernetes meta/v1.DeletionPropagation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.CleanupPolicyStatus">CleanupPolicyStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.CleanupPolicy">CleanupPolicy</a>, 
<a href="#kyverno.io/v2beta1.ClusterCleanupPolicy">ClusterCleanupPolicy</a>)
</p>
<p>
<p>CleanupPolicyStatus stores the status of the policy.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#condition-v1-meta">
[]Kubernetes meta/v1.Condition
</a>
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>lastExecutionTime</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#time-v1-meta">
Kubernetes meta/v1.Time
</a>
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.Condition">Condition
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.AnyAllConditions">AnyAllConditions</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>key</code><br/>
<em>
github.com/kyverno/kyverno/api/kyverno.Any
</em>
</td>
<td>
<p>Key is the context entry (using JMESPath) for conditional rule evaluation.</p>
</td>
</tr>
<tr>
<td>
<code>operator</code><br/>
<em>
<a href="#kyverno.io/v2beta1.ConditionOperator">
ConditionOperator
</a>
</em>
</td>
<td>
<p>Operator is the conditional operation to perform. Valid operators are:
Equals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,
GreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,
DurationLessThanOrEquals, DurationLessThan</p>
</td>
</tr>
<tr>
<td>
<code>value</code><br/>
<em>
github.com/kyverno/kyverno/api/kyverno.Any
</em>
</td>
<td>
<p>Value is the conditional value, or set of values. The values can be fixed set
or can be variables declared using JMESPath.</p>
</td>
</tr>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<p>Message is an optional display message</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.ConditionOperator">ConditionOperator
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.Condition">Condition</a>)
</p>
<p>
<p>ConditionOperator is the operation performed on condition key and value.</p>
</p>
<h3 id="kyverno.io/v2beta1.Deny">Deny
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.Validation">Validation</a>)
</p>
<p>
<p>Deny specifies a list of conditions used to pass or fail a validation rule.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2beta1.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<p>Multiple conditions can be declared under an <code>any</code> or <code>all</code> statement.
See: <a href="https://kyverno.io/docs/writing-policies/validate/#deny-rules">https://kyverno.io/docs/writing-policies/validate/#deny-rules</a></p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.Exception">Exception
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.PolicyExceptionSpec">PolicyExceptionSpec</a>)
</p>
<p>
<p>Exception stores infos about a policy and rules</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>policyName</code><br/>
<em>
string
</em>
</td>
<td>
<p>PolicyName identifies the policy to which the exception is applied.
The policy name uses the format <namespace>/<name> unless it
references a ClusterPolicy.</p>
</td>
</tr>
<tr>
<td>
<code>ruleNames</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>RuleNames identifies the rules to which the exception is applied.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.ImageVerification">ImageVerification
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.Rule">Rule</a>)
</p>
<p>
<p>ImageVerification validates that images that match the specified pattern
are signed with the supplied public key. Once the image is verified it is
mutated to include the SHA digest retrieved during the registration.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>failureAction</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Allowed values are Audit or Enforce.</p>
</td>
</tr>
<tr>
<td>
<code>type</code><br/>
<em>
<a href="#kyverno.io/v1.ImageVerificationType">
ImageVerificationType
</a>
</em>
</td>
<td>
<p>Type specifies the method of signature validation. The allowed options
are Cosign and Notary. By default Cosign is used if a type is not specified.</p>
</td>
</tr>
<tr>
<td>
<code>imageReferences</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>ImageReferences is a list of matching image reference patterns. At least one pattern in the
list must match the image for the rule to apply. Each image reference consists of a registry
address (defaults to docker.io), repository, image, and tag (defaults to latest).
Wildcards (&lsquo;*&rsquo; and &lsquo;?&rsquo;) are allowed. See: <a href="https://kubernetes.io/docs/concepts/containers/images">https://kubernetes.io/docs/concepts/containers/images</a>.</p>
</td>
</tr>
<tr>
<td>
<code>skipImageReferences</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>SkipImageReferences is a list of matching image reference patterns that should be skipped.
At least one pattern in the list must match the image for the rule to be skipped. Each image reference
consists of a registry address (defaults to docker.io), repository, image, and tag (defaults to latest).
Wildcards (&lsquo;*&rsquo; and &lsquo;?&rsquo;) are allowed. See: <a href="https://kubernetes.io/docs/concepts/containers/images">https://kubernetes.io/docs/concepts/containers/images</a>.</p>
</td>
</tr>
<tr>
<td>
<code>attestors</code><br/>
<em>
<a href="#kyverno.io/v1.AttestorSet">
[]AttestorSet
</a>
</em>
</td>
<td>
<p>Attestors specified the required attestors (i.e. authorities)</p>
</td>
</tr>
<tr>
<td>
<code>attestations</code><br/>
<em>
<a href="#kyverno.io/v1.Attestation">
[]Attestation
</a>
</em>
</td>
<td>
<p>Attestations are optional checks for signed in-toto Statements used to verify the image.
See <a href="https://github.com/in-toto/attestation">https://github.com/in-toto/attestation</a>. Kyverno fetches signed attestations from the
OCI registry and decodes them into a list of Statement declarations.</p>
</td>
</tr>
<tr>
<td>
<code>repository</code><br/>
<em>
string
</em>
</td>
<td>
<p>Repository is an optional alternate OCI repository to use for image signatures and attestations that match this rule.
If specified Repository will override the default OCI image repository configured for the installation.
The repository can also be overridden per Attestor or Attestation.</p>
</td>
</tr>
<tr>
<td>
<code>mutateDigest</code><br/>
<em>
bool
</em>
</td>
<td>
<p>MutateDigest enables replacement of image tags with digests.
Defaults to true.</p>
</td>
</tr>
<tr>
<td>
<code>verifyDigest</code><br/>
<em>
bool
</em>
</td>
<td>
<p>VerifyDigest validates that images have a digest.</p>
</td>
</tr>
<tr>
<td>
<code>validate</code><br/>
<em>
<a href="#kyverno.io/v1.ValidateImageVerification">
ValidateImageVerification
</a>
</em>
</td>
<td>
<p>Validation checks conditions across multiple image
verification attestations or context entries</p>
</td>
</tr>
<tr>
<td>
<code>required</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Required validates that images are verified i.e. have matched passed a signature or attestation check.</p>
</td>
</tr>
<tr>
<td>
<code>imageRegistryCredentials</code><br/>
<em>
<a href="#kyverno.io/v1.ImageRegistryCredentials">
ImageRegistryCredentials
</a>
</em>
</td>
<td>
<p>ImageRegistryCredentials provides credentials that will be used for authentication with registry</p>
</td>
</tr>
<tr>
<td>
<code>useCache</code><br/>
<em>
bool
</em>
</td>
<td>
<p>UseCache enables caching of image verify responses for this rule</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.MatchResources">MatchResources
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2.CleanupPolicySpec">CleanupPolicySpec</a>, 
<a href="#kyverno.io/v2.PolicyExceptionSpec">PolicyExceptionSpec</a>, 
<a href="#kyverno.io/v2beta1.CleanupPolicySpec">CleanupPolicySpec</a>, 
<a href="#kyverno.io/v2beta1.PolicyExceptionSpec">PolicyExceptionSpec</a>, 
<a href="#kyverno.io/v2beta1.Rule">Rule</a>)
</p>
<p>
<p>MatchResources is used to specify resource and admission review request data for
which a policy rule is applicable.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>any</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceFilters">
ResourceFilters
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Any allows specifying resources which will be ORed</p>
</td>
</tr>
<tr>
<td>
<code>all</code><br/>
<em>
<a href="#kyverno.io/v1.ResourceFilters">
ResourceFilters
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>All allows specifying resources which will be ANDed</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.PolicyExceptionSpec">PolicyExceptionSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.PolicyException">PolicyException</a>)
</p>
<p>
<p>PolicyExceptionSpec stores policy exception spec</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>background</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Background controls if exceptions are applied to existing policies during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>Match defines match clause used to check if a resource applies to the exception</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="#kyverno.io/v2beta1.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions are used to determine if a resource applies to the exception by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.</p>
</td>
</tr>
<tr>
<td>
<code>exceptions</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Exception">
[]Exception
</a>
</em>
</td>
<td>
<p>Exceptions is a list policy/rules to be excluded</p>
</td>
</tr>
<tr>
<td>
<code>podSecurity</code><br/>
<em>
<a href="#kyverno.io/v1.PodSecurityStandard">
[]PodSecurityStandard
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PodSecurity specifies the Pod Security Standard controls to be excluded.
Applicable only to policies that have validate.podSecurity subrule.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.ResourceDescription">ResourceDescription
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.ResourceFilter">ResourceFilter</a>)
</p>
<p>
<p>ResourceDescription contains criteria used to match resources.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>kinds</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Kinds is a list of resource kinds.</p>
</td>
</tr>
<tr>
<td>
<code>names</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Names are the names of the resources. Each name supports wildcard characters
&ldquo;*&rdquo; (matches zero or many characters) and &ldquo;?&rdquo; (at least one character).</p>
</td>
</tr>
<tr>
<td>
<code>namespaces</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Namespaces is a list of namespaces names. Each name supports wildcard characters
&ldquo;*&rdquo; (matches zero or many characters) and &ldquo;?&rdquo; (at least one character).</p>
</td>
</tr>
<tr>
<td>
<code>annotations</code><br/>
<em>
map[string]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
and values support the wildcard characters &ldquo;*&rdquo; (matches zero or many characters) and
&ldquo;?&rdquo; (matches at least one character).</p>
</td>
</tr>
<tr>
<td>
<code>selector</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#labelselector-v1-meta">
Kubernetes meta/v1.LabelSelector
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Selector is a label selector. Label keys and values in <code>matchLabels</code> support the wildcard
characters <code>*</code> (matches zero or many characters) and <code>?</code> (matches one character).
Wildcards allows writing label selectors like [&ldquo;storage.k8s.io/<em>&rdquo;: &ldquo;</em>&rdquo;]. Note that
using [&rdquo;<em>&rdquo; : &ldquo;</em>&rdquo;] matches any key and value but does not match an empty label set.</p>
</td>
</tr>
<tr>
<td>
<code>namespaceSelector</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#labelselector-v1-meta">
Kubernetes meta/v1.LabelSelector
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>NamespaceSelector is a label selector for the resource namespace. Label keys and values
in <code>matchLabels</code> support the wildcard characters <code>*</code> (matches zero or many characters)
and <code>?</code> (matches one character).Wildcards allows writing label selectors like
[&ldquo;storage.k8s.io/<em>&rdquo;: &ldquo;</em>&rdquo;]. Note that using [&rdquo;<em>&rdquo; : &ldquo;</em>&rdquo;] matches any key and value but
does not match an empty label set.</p>
</td>
</tr>
<tr>
<td>
<code>operations</code><br/>
<em>
<a href="#kyverno.io/v1.AdmissionOperation">
[]AdmissionOperation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Operations can contain values [&ldquo;CREATE, &ldquo;UPDATE&rdquo;, &ldquo;CONNECT&rdquo;, &ldquo;DELETE&rdquo;], which are used to match a specific action.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.ResourceFilter">ResourceFilter
</h3>
<p>
<p>ResourceFilter allow users to &ldquo;AND&rdquo; or &ldquo;OR&rdquo; between resources</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>UserInfo</code><br/>
<em>
<a href="#kyverno.io/v1.UserInfo">
UserInfo
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>UserInfo contains information about the user performing the operation.</p>
</td>
</tr>
<tr>
<td>
<code>resources</code><br/>
<em>
<a href="#kyverno.io/v2beta1.ResourceDescription">
ResourceDescription
</a>
</em>
</td>
<td>
<p>ResourceDescription contains information about the resource being created or modified.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.ResourceFilters">ResourceFilters
(<code>[]github.com/kyverno/kyverno/api/kyverno/v2beta1.ResourceFilter</code> alias)</p></h3>
<p>
<p>ResourceFilters is a slice of ResourceFilter</p>
</p>
<h3 id="kyverno.io/v2beta1.Rule">Rule
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.Spec">Spec</a>)
</p>
<p>
<p>Rule defines a validation, mutation, or generation control for matching resources.
Each rules contains a match declaration to select resources, and an optional exclude
declaration to specify which resources to exclude.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is a label to identify the rule, It must be unique within the policy.</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
<a href="#kyverno.io/v1.ContextEntry">
[]ContextEntry
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Context defines variables and data sources that can be used during rule execution.</p>
</td>
</tr>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<p>MatchResources defines when this policy rule should be applied. The match
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the user name or role.
At least one kind is required.</p>
</td>
</tr>
<tr>
<td>
<code>exclude</code><br/>
<em>
<a href="#kyverno.io/v2beta1.MatchResources">
MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ExcludeResources defines when this policy rule should not be applied. The exclude
criteria can include resource information (e.g. kind, name, namespace, labels)
and admission review request information like the name or role.</p>
</td>
</tr>
<tr>
<td>
<code>imageExtractors</code><br/>
<em>
<a href="#kyverno.io/v1.ImageExtractorConfigs">
ImageExtractorConfigs
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ImageExtractors defines a mapping from kinds to ImageExtractorConfigs.
This config is only valid for verifyImages rules.</p>
</td>
</tr>
<tr>
<td>
<code>preconditions</code><br/>
<em>
<a href="#kyverno.io/v2beta1.AnyAllConditions">
AnyAllConditions
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Preconditions are used to determine if a policy rule should be applied by evaluating a
set of conditions. The declaration can contain nested <code>any</code> or <code>all</code> statements.
See: <a href="https://kyverno.io/docs/writing-policies/preconditions/">https://kyverno.io/docs/writing-policies/preconditions/</a></p>
</td>
</tr>
<tr>
<td>
<code>celPreconditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>CELPreconditions are used to determine if a policy rule should be applied by evaluating a
set of CEL conditions. It can only be used with the validate.cel subrule</p>
</td>
</tr>
<tr>
<td>
<code>mutate</code><br/>
<em>
<a href="#kyverno.io/v1.Mutation">
Mutation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Mutation is used to modify matching resources.</p>
</td>
</tr>
<tr>
<td>
<code>validate</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Validation">
Validation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Validation is used to validate matching resources.</p>
</td>
</tr>
<tr>
<td>
<code>generate</code><br/>
<em>
<a href="#kyverno.io/v1.Generation">
Generation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Generation is used to create new resources.</p>
</td>
</tr>
<tr>
<td>
<code>verifyImages</code><br/>
<em>
<a href="#kyverno.io/v2beta1.ImageVerification">
[]ImageVerification
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>VerifyImages is used to verify image signatures and mutate them to add a digest</p>
</td>
</tr>
<tr>
<td>
<code>skipBackgroundRequests</code><br/>
<em>
bool
</em>
</td>
<td>
<p>SkipBackgroundRequests bypasses admission requests that are sent by the background controller.
The default value is set to &ldquo;true&rdquo;, it must be set to &ldquo;false&rdquo; to apply
generate and mutateExisting rules to those requests.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.Spec">Spec
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.ClusterPolicy">ClusterPolicy</a>, 
<a href="#kyverno.io/v2beta1.Policy">Policy</a>)
</p>
<p>
<p>Spec contains a list of Rule instances and other policy controls.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>rules</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Rule">
[]Rule
</a>
</em>
</td>
<td>
<p>Rules is a list of Rule instances. A Policy contains multiple rules and
each rule can validate, mutate, or generate resources.</p>
</td>
</tr>
<tr>
<td>
<code>applyRules</code><br/>
<em>
<a href="#kyverno.io/v1.ApplyRulesType">
ApplyRulesType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ApplyRules controls how rules in a policy are applied. Rule are processed in
the order of declaration. When set to <code>One</code> processing stops after a rule has
been applied i.e. the rule matches and results in a pass, fail, or error. When
set to <code>All</code> all rules in the policy are processed. The default is <code>All</code>.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="#kyverno.io/v1.FailurePolicyType">
FailurePolicyType
</a>
</em>
</td>
<td>
<p>Deprecated, use failurePolicy under the webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureAction</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureAction under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>validationFailureActionOverrides</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureActionOverride">
[]ValidationFailureActionOverride
</a>
</em>
</td>
<td>
<p>Deprecated, use validationFailureActionOverrides under the validate rule instead.</p>
</td>
</tr>
<tr>
<td>
<code>emitWarning</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>EmitWarning enables API response warnings for mutate policy rules or validate policy rules with validationFailureAction set to Audit.
Enabling this option will extend admission request processing times. The default value is &ldquo;false&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>admission</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Admission controls if rules are applied during admission.
Optional. Default value is &ldquo;true&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>background</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Background controls if rules are applied to existing resources during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
<tr>
<td>
<code>schemaValidation</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated.</p>
</td>
</tr>
<tr>
<td>
<code>webhookTimeoutSeconds</code><br/>
<em>
int32
</em>
</td>
<td>
<p>Deprecated, use webhookTimeoutSeconds under webhookConfiguration instead.</p>
</td>
</tr>
<tr>
<td>
<code>mutateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use mutateExistingOnPolicyUpdate under the mutate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExistingOnPolicyUpdate</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deprecated, use generateExisting instead</p>
</td>
</tr>
<tr>
<td>
<code>generateExisting</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Deprecated, use generateExisting under the generate rule instead</p>
</td>
</tr>
<tr>
<td>
<code>useServerSideApply</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>UseServerSideApply controls whether to use server-side apply for generate rules
If is set to &ldquo;true&rdquo; create &amp; update for generate rules will use apply instead of create/update.
Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#kyverno.io/v1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration specifies the custom configuration for Kubernetes admission webhookconfiguration.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="kyverno.io/v2beta1.Validation">Validation
</h3>
<p>
(<em>Appears on:</em>
<a href="#kyverno.io/v2beta1.Rule">Rule</a>)
</p>
<p>
<p>Validation defines checks to be performed on matching resources.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>failureAction</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureAction">
ValidationFailureAction
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>FailureAction defines if a validation policy rule violation should block
the admission review request (Enforce), or allow (Audit) the admission review request
and report an error in a policy report. Optional.
Allowed values are Audit or Enforce.</p>
</td>
</tr>
<tr>
<td>
<code>failureActionOverrides</code><br/>
<em>
<a href="#kyverno.io/v1.ValidationFailureActionOverride">
[]ValidationFailureActionOverride
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>FailureActionOverrides is a Cluster Policy attribute that specifies FailureAction
namespace-wise. It overrides FailureAction for the specified namespaces.</p>
</td>
</tr>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Message specifies a custom message to be displayed on failure.</p>
</td>
</tr>
<tr>
<td>
<code>manifests</code><br/>
<em>
<a href="#kyverno.io/v1.Manifests">
Manifests
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Manifest specifies conditions for manifest verification</p>
</td>
</tr>
<tr>
<td>
<code>foreach</code><br/>
<em>
<a href="#kyverno.io/v1.ForEachValidation">
[]ForEachValidation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ForEach applies validate rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.</p>
</td>
</tr>
<tr>
<td>
<code>pattern</code><br/>
<em>
github.com/kyverno/kyverno/api/kyverno.Any
</em>
</td>
<td>
<p>Pattern specifies an overlay-style pattern used to check resources.</p>
</td>
</tr>
<tr>
<td>
<code>anyPattern</code><br/>
<em>
github.com/kyverno/kyverno/api/kyverno.Any
</em>
</td>
<td>
<p>AnyPattern specifies list of validation patterns. At least one of the patterns
must be satisfied for the validation rule to succeed.</p>
</td>
</tr>
<tr>
<td>
<code>deny</code><br/>
<em>
<a href="#kyverno.io/v2beta1.Deny">
Deny
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Deny defines conditions used to pass or fail a validation rule.</p>
</td>
</tr>
<tr>
<td>
<code>podSecurity</code><br/>
<em>
<a href="#kyverno.io/v1.PodSecurity">
PodSecurity
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PodSecurity applies exemptions for Kubernetes Pod Security admission
by specifying exclusions for Pod Security Standards controls.</p>
</td>
</tr>
<tr>
<td>
<code>cel</code><br/>
<em>
<a href="#kyverno.io/v1.CEL">
CEL
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>CEL allows validation checks using the Common Expression Language (<a href="https://kubernetes.io/docs/reference/using-api/cel/">https://kubernetes.io/docs/reference/using-api/cel/</a>).</p>
</td>
</tr>
<tr>
<td>
<code>assert</code><br/>
<em>
github.com/kyverno/kyverno-json/pkg/apis/policy/v1alpha1.Any
</em>
</td>
<td>
<em>(Optional)</em>
<p>Assert defines a kyverno-json assertion tree.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h2 id="policies.kyverno.io/v1alpha1">policies.kyverno.io/v1alpha1</h2>
<p>
</p>
Resource Types:
<ul><li>
<a href="#policies.kyverno.io/v1alpha1.DeletingPolicy">DeletingPolicy</a>
</li><li>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicy">GeneratingPolicy</a>
</li><li>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicy">ImageValidatingPolicy</a>
</li><li>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicy">MutatingPolicy</a>
</li><li>
<a href="#policies.kyverno.io/v1alpha1.PolicyException">PolicyException</a>
</li><li>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicy">ValidatingPolicy</a>
</li></ul>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.DeletingPolicy">DeletingPolicy
</h3>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
policies.kyverno.io/v1alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>DeletingPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.DeletingPolicySpec">
DeletingPolicySpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>matchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1-admissionregistration">
Kubernetes admissionregistration/v1.MatchResources
</a>
</em>
</td>
<td>
<p>MatchConstraints specifies what resources this policy is designed to validate.
The AdmissionPolicy cares about a request if it matches <em>all</em> Constraints.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions is a list of conditions that must be met for a resource to be deleted.
Conditions filter resources that have already been matched by the match constraints,
namespaceSelector, and objectSelector. An empty list of conditions matches all resources.
There are a maximum of 64 conditions allowed.</p>
<p>The exact matching logic is (in order):
1. If ANY condition evaluates to FALSE, the policy is skipped.
2. If ALL conditions evaluate to TRUE, the policy is executed.</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.
The variables defined here will be available under <code>variables</code> in other expressions of the policy
except MatchConditions because MatchConditions are evaluated before the rest of the policy.</p>
<p>The expression of a variable can refer to other variables defined earlier in the list but not those after.
Thus, Variables must be sorted by the order of first appearance and acyclic.</p>
</td>
</tr>
<tr>
<td>
<code>schedule</code><br/>
<em>
string
</em>
</td>
<td>
<p>The schedule in Cron format
Required.</p>
</td>
</tr>
<tr>
<td>
<code>deletionPropagationPolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#deletionpropagation-v1-meta">
Kubernetes meta/v1.DeletionPropagation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.DeletingPolicyStatus">
DeletingPolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.GeneratingPolicy">GeneratingPolicy
</h3>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
policies.kyverno.io/v1alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>GeneratingPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicySpec">
GeneratingPolicySpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>matchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1-admissionregistration">
Kubernetes admissionregistration/v1.MatchResources
</a>
</em>
</td>
<td>
<p>MatchConstraints specifies what resources will trigger this policy.
The AdmissionPolicy cares about a request if it matches <em>all</em> Constraints.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConditions is a list of conditions that must be met for a request to be validated.
Match conditions filter requests that have already been matched by the rules,
namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.
There are a maximum of 64 match conditions allowed.</p>
<p>If a parameter object is provided, it can be accessed via the <code>params</code> handle in the same
manner as validation expressions.</p>
<p>The exact matching logic is (in order):
1. If ANY matchCondition evaluates to FALSE, the policy is skipped.
2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.
3. If any matchCondition evaluates to an error (but none are FALSE):
- If failurePolicy=Fail, reject the request
- If failurePolicy=Ignore, the policy is skipped</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.
The variables defined here will be available under <code>variables</code> in other expressions of the policy
except MatchConditions because MatchConditions are evaluated before the rest of the policy.</p>
<p>The expression of a variable can refer to other variables defined earlier in the list but not those after.
Thus, Variables must be sorted by the order of first appearance and acyclic.</p>
</td>
</tr>
<tr>
<td>
<code>evaluation</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicyEvaluationConfiguration">
GeneratingPolicyEvaluationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>EvaluationConfiguration defines the configuration for the policy evaluation.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration defines the configuration for the webhook.</p>
</td>
</tr>
<tr>
<td>
<code>generate</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Generation">
[]Generation
</a>
</em>
</td>
<td>
<p>Generation defines a set of CEL expressions that will be evaluated to generate resources.
Required.</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicyStatus">
GeneratingPolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ImageValidatingPolicy">ImageValidatingPolicy
</h3>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
policies.kyverno.io/v1alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>ImageValidatingPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">
ImageValidatingPolicySpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>matchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1-admissionregistration">
Kubernetes admissionregistration/v1.MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConstraints specifies what resources this policy is designed to validate.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#failurepolicytype-v1-admissionregistration">
Kubernetes admissionregistration/v1.FailurePolicyType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>FailurePolicy defines how to handle failures for the admission policy. Failures can
occur from CEL expression parse errors, type check errors, runtime errors and invalid
or mis-configured policy definitions or bindings.</p>
</td>
</tr>
<tr>
<td>
<code>auditAnnotations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#auditannotation-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.AuditAnnotation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>auditAnnotations contains CEL expressions which are used to produce audit
annotations for the audit event of the API request.
validations and auditAnnotations may not both be empty; a least one of validations or auditAnnotations is
required.</p>
</td>
</tr>
<tr>
<td>
<code>validationActions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#validationaction-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.ValidationAction
</a>
</em>
</td>
<td>
<p>ValidationAction specifies the action to be taken when the matched resource violates the policy.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConditions is a list of conditions that must be met for a request to be validated.
Match conditions filter requests that have already been matched by the rules,
namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.
There are a maximum of 64 match conditions allowed.</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.</p>
</td>
</tr>
<tr>
<td>
<code>validationConfigurations</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ValidationConfiguration">
ValidationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ValidationConfigurations defines settings for mutating and verifying image digests, and enforcing image verification through signatures.</p>
</td>
</tr>
<tr>
<td>
<code>matchImageReferences</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MatchImageReference">
[]MatchImageReference
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchImageReferences is a list of Glob and CELExpressions to match images.
Any image that matches one of the rules is considered for validation
Any image that does not match a rule is skipped, even when they are passed as arguments to
image verification functions</p>
</td>
</tr>
<tr>
<td>
<code>credentials</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Credentials">
Credentials
</a>
</em>
</td>
<td>
<p>Credentials provides credentials that will be used for authentication with registry.</p>
</td>
</tr>
<tr>
<td>
<code>images</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ImageExtractor">
[]ImageExtractor
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ImageExtractors is a list of CEL expression to extract images from the resource</p>
</td>
</tr>
<tr>
<td>
<code>attestors</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Attestor">
[]Attestor
</a>
</em>
</td>
<td>
<p>Attestors provides a list of trusted authorities.</p>
</td>
</tr>
<tr>
<td>
<code>attestations</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Attestation">
[]Attestation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Attestations provides a list of image metadata to verify</p>
</td>
</tr>
<tr>
<td>
<code>validations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#validation-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Validation
</a>
</em>
</td>
<td>
<p>Validations contain CEL expressions which is used to apply the image validation checks.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration defines the configuration for the webhook.</p>
</td>
</tr>
<tr>
<td>
<code>evaluation</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.EvaluationConfiguration">
EvaluationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>EvaluationConfiguration defines the configuration for the policy evaluation.</p>
</td>
</tr>
<tr>
<td>
<code>autogen</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogenConfiguration">
ImageValidatingPolicyAutogenConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AutogenConfiguration defines the configuration for the generation controller.</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyStatus">
ImageValidatingPolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.MutatingPolicy">MutatingPolicy
</h3>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
policies.kyverno.io/v1alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>MutatingPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicySpec">
MutatingPolicySpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>matchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1alpha1-admissionregistration">
Kubernetes admissionregistration/v1alpha1.MatchResources
</a>
</em>
</td>
<td>
<p>MatchConstraints specifies what resources this policy is designed to evaluate.
The AdmissionPolicy cares about a request if it matches <em>all</em> Constraints.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#failurepolicytype-v1alpha1-admissionregistration">
Kubernetes admissionregistration/v1alpha1.FailurePolicyType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>failurePolicy defines how to handle failures for the admission policy. Failures can
occur from CEL expression parse errors, type check errors, runtime errors and invalid
or mis-configured policy definitions or bindings.</p>
<p>failurePolicy does not define how validations that evaluate to false are handled.</p>
<p>When failurePolicy is set to Fail, the validationActions field define how failures are enforced.</p>
<p>Allowed values are Ignore or Fail. Defaults to Fail.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1alpha1-admissionregistration">
[]Kubernetes admissionregistration/v1alpha1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConditions is a list of conditions that must be met for a request to be validated.
Match conditions filter requests that have already been matched by the rules,
namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.
There are a maximum of 64 match conditions allowed.</p>
<p>If a parameter object is provided, it can be accessed via the <code>params</code> handle in the same
manner as validation expressions.</p>
<p>The exact matching logic is (in order):
1. If ANY matchCondition evaluates to FALSE, the policy is skipped.
2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.
3. If any matchCondition evaluates to an error (but none are FALSE):
- If failurePolicy=Fail, reject the request
- If failurePolicy=Ignore, the policy is skipped</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1alpha1-admissionregistration">
[]Kubernetes admissionregistration/v1alpha1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.
The variables defined here will be available under <code>variables</code> in other expressions of the policy
except MatchConditions because MatchConditions are evaluated before the rest of the policy.</p>
<p>The expression of a variable can refer to other variables defined earlier in the list but not those after.
Thus, Variables must be sorted by the order of first appearance and acyclic.</p>
</td>
</tr>
<tr>
<td>
<code>autogen</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyAutogenConfiguration">
MutatingPolicyAutogenConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AutogenConfiguration defines the configuration for the generation controller.</p>
</td>
</tr>
<tr>
<td>
<code>targetMatchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1alpha1-admissionregistration">
Kubernetes admissionregistration/v1alpha1.MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>TargetMatchConstraints specifies what target mutation resources this policy is designed to evaluate.</p>
</td>
</tr>
<tr>
<td>
<code>mutations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#mutation-v1alpha1-admissionregistration">
[]Kubernetes admissionregistration/v1alpha1.Mutation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>mutations contain operations to perform on matching objects.
mutations may not be empty; a minimum of one mutation is required.
mutations are evaluated in order, and are reinvoked according to
the reinvocationPolicy.
The mutations of a policy are invoked for each binding of this policy
and reinvocation of mutations occurs on a per binding basis.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration defines the configuration for the webhook.</p>
</td>
</tr>
<tr>
<td>
<code>evaluation</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyEvaluationConfiguration">
MutatingPolicyEvaluationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>EvaluationConfiguration defines the configuration for mutating policy evaluation.</p>
</td>
</tr>
<tr>
<td>
<code>reinvocationPolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#reinvocationpolicytype-v1-admissionregistration">
Kubernetes admissionregistration/v1.ReinvocationPolicyType
</a>
</em>
</td>
<td>
<p>reinvocationPolicy indicates whether mutations may be called multiple times per MutatingAdmissionPolicyBinding
as part of a single admission evaluation.
Allowed values are &ldquo;Never&rdquo; and &ldquo;IfNeeded&rdquo;.</p>
<p>Never: These mutations will not be called more than once per binding in a single admission evaluation.</p>
<p>IfNeeded: These mutations may be invoked more than once per binding for a single admission request and there is no guarantee of
order with respect to other admission plugins, admission webhooks, bindings of this policy and admission policies.  Mutations are only
reinvoked when mutations change the object after this mutation is invoked.
Required.</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyStatus">
MutatingPolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.PolicyException">PolicyException
</h3>
<p>
<p>PolicyException declares resources to be excluded from specified policies.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
policies.kyverno.io/v1alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>PolicyException</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.PolicyExceptionSpec">
PolicyExceptionSpec
</a>
</em>
</td>
<td>
<p>Spec declares policy exception behaviors.</p>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>policyRefs</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.PolicyRef">
[]PolicyRef
</a>
</em>
</td>
<td>
<p>PolicyRefs identifies the policies to which the exception is applied.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConditions is a list of CEL expressions that must be met for a resource to be excluded.</p>
</td>
</tr>
</table>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ValidatingPolicy">ValidatingPolicy
</h3>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
policies.kyverno.io/v1alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>ValidatingPolicy</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicySpec">
ValidatingPolicySpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>matchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1-admissionregistration">
Kubernetes admissionregistration/v1.MatchResources
</a>
</em>
</td>
<td>
<p>MatchConstraints specifies what resources this policy is designed to validate.
The AdmissionPolicy cares about a request if it matches <em>all</em> Constraints.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>validations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#validation-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Validation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Validations contain CEL expressions which is used to apply the validation.
Validations and AuditAnnotations may not both be empty; a minimum of one Validations or AuditAnnotations is
required.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#failurepolicytype-v1-admissionregistration">
Kubernetes admissionregistration/v1.FailurePolicyType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>failurePolicy defines how to handle failures for the admission policy. Failures can
occur from CEL expression parse errors, type check errors, runtime errors and invalid
or mis-configured policy definitions or bindings.</p>
<p>failurePolicy does not define how validations that evaluate to false are handled.</p>
<p>When failurePolicy is set to Fail, the validationActions field define how failures are enforced.</p>
<p>Allowed values are Ignore or Fail. Defaults to Fail.</p>
</td>
</tr>
<tr>
<td>
<code>auditAnnotations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#auditannotation-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.AuditAnnotation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>auditAnnotations contains CEL expressions which are used to produce audit
annotations for the audit event of the API request.
validations and auditAnnotations may not both be empty; a least one of validations or auditAnnotations is
required.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConditions is a list of conditions that must be met for a request to be validated.
Match conditions filter requests that have already been matched by the rules,
namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.
There are a maximum of 64 match conditions allowed.</p>
<p>If a parameter object is provided, it can be accessed via the <code>params</code> handle in the same
manner as validation expressions.</p>
<p>The exact matching logic is (in order):
1. If ANY matchCondition evaluates to FALSE, the policy is skipped.
2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.
3. If any matchCondition evaluates to an error (but none are FALSE):
- If failurePolicy=Fail, reject the request
- If failurePolicy=Ignore, the policy is skipped</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.
The variables defined here will be available under <code>variables</code> in other expressions of the policy
except MatchConditions because MatchConditions are evaluated before the rest of the policy.</p>
<p>The expression of a variable can refer to other variables defined earlier in the list but not those after.
Thus, Variables must be sorted by the order of first appearance and acyclic.</p>
</td>
</tr>
<tr>
<td>
<code>autogen</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyAutogenConfiguration">
ValidatingPolicyAutogenConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AutogenConfiguration defines the configuration for the generation controller.</p>
</td>
</tr>
<tr>
<td>
<code>validationActions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#validationaction-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.ValidationAction
</a>
</em>
</td>
<td>
<p>ValidationAction specifies the action to be taken when the matched resource violates the policy.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration defines the configuration for the webhook.</p>
</td>
</tr>
<tr>
<td>
<code>evaluation</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.EvaluationConfiguration">
EvaluationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>EvaluationConfiguration defines the configuration for the policy evaluation.</p>
</td>
</tr>
</table>
</td>
</tr>
<tr>
<td>
<code>status</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyStatus">
ValidatingPolicyStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Status contains policy runtime data.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.AdmissionConfiguration">AdmissionConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.EvaluationConfiguration">EvaluationConfiguration</a>, 
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicyEvaluationConfiguration">GeneratingPolicyEvaluationConfiguration</a>, 
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyEvaluationConfiguration">MutatingPolicyEvaluationConfiguration</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>enabled</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Enabled controls if rules are applied during admission.
Optional. Default value is &ldquo;true&rdquo;.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Attestation">Attestation
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">ImageValidatingPolicySpec</a>)
</p>
<p>
<p>Attestation defines the identification details of the  metadata that has to be verified</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the name for this attestation. It is used to refer to the attestation in verification</p>
</td>
</tr>
<tr>
<td>
<code>intoto</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.InToto">
InToto
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>InToto defines the details of attestation attached using intoto format</p>
</td>
</tr>
<tr>
<td>
<code>referrer</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Referrer">
Referrer
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Referrer defines the details of attestation attached using OCI 1.1 format</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Attestor">Attestor
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">ImageValidatingPolicySpec</a>)
</p>
<p>
<p>Attestor is an identity that confirms or verifies the authenticity of an image or an attestation</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the name for this attestor. It is used to refer to the attestor in verification</p>
</td>
</tr>
<tr>
<td>
<code>cosign</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Cosign">
Cosign
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Cosign defines attestor configuration for Cosign based signatures</p>
</td>
</tr>
<tr>
<td>
<code>notary</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Notary">
Notary
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Notary defines attestor configuration for Notary based signatures</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.BackgroundConfiguration">BackgroundConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.EvaluationConfiguration">EvaluationConfiguration</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>enabled</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Enabled controls if rules are applied to existing resources during a background scan.
Optional. Default value is &ldquo;true&rdquo;. The value must be set to &ldquo;false&rdquo; if the policy rule
uses variables that are only available in the admission review request (e.g. user name).</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.CTLog">CTLog
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Cosign">Cosign</a>)
</p>
<p>
<p>CTLog sets the configuration to verify the authority against a Rekor instance.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>url</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>URL sets the url to the rekor instance (by default the public rekor.sigstore.dev)</p>
</td>
</tr>
<tr>
<td>
<code>rekorPubKey</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.
If set, this will be used to validate transparency log signatures from a custom Rekor.</p>
</td>
</tr>
<tr>
<td>
<code>ctLogPubKey</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>CTLogPubKey, if set, is used to validate SCTs against a custom source.</p>
</td>
</tr>
<tr>
<td>
<code>tsaCertChain</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must
contain the root CA certificate. Optionally may contain intermediate CA certificates, and
may contain the leaf TSA certificate if not present in the timestamurce.</p>
</td>
</tr>
<tr>
<td>
<code>insecureIgnoreTlog</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>InsecureIgnoreTlog skips transparency log verification.</p>
</td>
</tr>
<tr>
<td>
<code>insecureIgnoreSCT</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate
timestamp. Default is false. Set to true if this was opted out during signing.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Certificate">Certificate
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Cosign">Cosign</a>)
</p>
<p>
<p>Certificate defines the configuration for local signature verification</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>cert</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.StringOrExpression">
StringOrExpression
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Certificate is the to the public certificate for local signature verification.</p>
</td>
</tr>
<tr>
<td>
<code>certChain</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.StringOrExpression">
StringOrExpression
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>CertificateChain is the list of CA certificates in PEM format which will be needed
when building the certificate chain for the signing certificate. Must start with the
parent intermediate CA certificate of the signing certificate and end with the root certificate</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ConditionStatus">ConditionStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.DeletingPolicyStatus">DeletingPolicyStatus</a>, 
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicyStatus">GeneratingPolicyStatus</a>, 
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyStatus">ImageValidatingPolicyStatus</a>, 
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyStatus">MutatingPolicyStatus</a>, 
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyStatus">ValidatingPolicyStatus</a>)
</p>
<p>
<p>ConditionStatus is the shared status across all policy types</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>ready</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>The ready of a policy is a high-level summary of where the policy is in its lifecycle.
The conditions array, the reason and message fields contain more detail about the policy&rsquo;s status.</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#condition-v1-meta">
[]Kubernetes meta/v1.Condition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Message is a human readable message indicating details about the generation of ValidatingAdmissionPolicy/MutatingAdmissionPolicy
It is an empty string when ValidatingAdmissionPolicy/MutatingAdmissionPolicy is successfully generated.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Cosign">Cosign
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Attestor">Attestor</a>)
</p>
<p>
<p>Cosign defines attestor configuration for Cosign based signatures</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>key</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Key">
Key
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Key defines the type of key to validate the image.</p>
</td>
</tr>
<tr>
<td>
<code>keyless</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Keyless">
Keyless
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Keyless sets the configuration to verify the authority against a Fulcio instance.</p>
</td>
</tr>
<tr>
<td>
<code>certificate</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Certificate">
Certificate
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Certificate defines the configuration for local signature verification</p>
</td>
</tr>
<tr>
<td>
<code>source</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Source">
Source
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Sources sets the configuration to specify the sources from where to consume the signature and attestations.</p>
</td>
</tr>
<tr>
<td>
<code>ctlog</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.CTLog">
CTLog
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>CTLog sets the configuration to verify the authority against a Rekor instance.</p>
</td>
</tr>
<tr>
<td>
<code>tuf</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.TUF">
TUF
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>TUF defines the configuration to fetch sigstore root</p>
</td>
</tr>
<tr>
<td>
<code>annotations</code><br/>
<em>
map[string]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Annotations are used for image verification.
Every specified key-value pair must exist and match in the verified payload.
The payload may contain other key-value pairs.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Credentials">Credentials
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">ImageValidatingPolicySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>allowInsecureRegistry</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>AllowInsecureRegistry allows insecure access to a registry.</p>
</td>
</tr>
<tr>
<td>
<code>providers</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.CredentialsProvidersType">
[]CredentialsProvidersType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Providers specifies a list of OCI Registry names, whose authentication providers are provided.
It can be of one of these values: default,google,azure,amazon,github.</p>
</td>
</tr>
<tr>
<td>
<code>secrets</code><br/>
<em>
[]string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Secrets specifies a list of secrets that are provided for credentials.
Secrets must live in the Kyverno namespace.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.CredentialsProvidersType">CredentialsProvidersType
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Credentials">Credentials</a>)
</p>
<p>
<p>CredentialsProvidersType provides the list of credential providers required.</p>
</p>
<h3 id="policies.kyverno.io/v1alpha1.DeletingPolicySpec">DeletingPolicySpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.DeletingPolicy">DeletingPolicy</a>)
</p>
<p>
<p>DeletingPolicySpec is the specification of the desired behavior of the DeletingPolicy.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>matchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1-admissionregistration">
Kubernetes admissionregistration/v1.MatchResources
</a>
</em>
</td>
<td>
<p>MatchConstraints specifies what resources this policy is designed to validate.
The AdmissionPolicy cares about a request if it matches <em>all</em> Constraints.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>conditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Conditions is a list of conditions that must be met for a resource to be deleted.
Conditions filter resources that have already been matched by the match constraints,
namespaceSelector, and objectSelector. An empty list of conditions matches all resources.
There are a maximum of 64 conditions allowed.</p>
<p>The exact matching logic is (in order):
1. If ANY condition evaluates to FALSE, the policy is skipped.
2. If ALL conditions evaluate to TRUE, the policy is executed.</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.
The variables defined here will be available under <code>variables</code> in other expressions of the policy
except MatchConditions because MatchConditions are evaluated before the rest of the policy.</p>
<p>The expression of a variable can refer to other variables defined earlier in the list but not those after.
Thus, Variables must be sorted by the order of first appearance and acyclic.</p>
</td>
</tr>
<tr>
<td>
<code>schedule</code><br/>
<em>
string
</em>
</td>
<td>
<p>The schedule in Cron format
Required.</p>
</td>
</tr>
<tr>
<td>
<code>deletionPropagationPolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#deletionpropagation-v1-meta">
Kubernetes meta/v1.DeletionPropagation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>DeletionPropagationPolicy defines how resources will be deleted (Foreground, Background, Orphan).</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.DeletingPolicyStatus">DeletingPolicyStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.DeletingPolicy">DeletingPolicy</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>conditionStatus</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ConditionStatus">
ConditionStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>lastExecutionTime</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#time-v1-meta">
Kubernetes meta/v1.Time
</a>
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.EvaluationConfiguration">EvaluationConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">ImageValidatingPolicySpec</a>, 
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicySpec">ValidatingPolicySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>mode</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.EvaluationMode">
EvaluationMode
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Mode is the mode of policy evaluation.
Allowed values are &ldquo;Kubernetes&rdquo; or &ldquo;JSON&rdquo;.
Optional. Default value is &ldquo;Kubernetes&rdquo;.</p>
</td>
</tr>
<tr>
<td>
<code>admission</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.AdmissionConfiguration">
AdmissionConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Admission controls policy evaluation during admission.</p>
</td>
</tr>
<tr>
<td>
<code>background</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.BackgroundConfiguration">
BackgroundConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Background  controls policy evaluation during background scan.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.EvaluationMode">EvaluationMode
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.EvaluationConfiguration">EvaluationConfiguration</a>)
</p>
<p>
</p>
<h3 id="policies.kyverno.io/v1alpha1.GenerateExistingConfiguration">GenerateExistingConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicyEvaluationConfiguration">GeneratingPolicyEvaluationConfiguration</a>)
</p>
<p>
<p>GenerateExistingConfiguration defines the configuration for generating resources for existing triggers.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>enabled</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Enabled controls whether to trigger the policy for existing resources
If is set to &ldquo;true&rdquo; the policy will be triggered and applied to existing matched resources.
Optional. Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.GeneratingPolicyEvaluationConfiguration">GeneratingPolicyEvaluationConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicySpec">GeneratingPolicySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>admission</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.AdmissionConfiguration">
AdmissionConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Admission controls policy evaluation during admission.</p>
</td>
</tr>
<tr>
<td>
<code>generateExisting</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.GenerateExistingConfiguration">
GenerateExistingConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>GenerateExisting defines the configuration for generating resources for existing triggeres.</p>
</td>
</tr>
<tr>
<td>
<code>synchronize</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.SynchronizationConfiguration">
SynchronizationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Synchronization defines the configuration for the synchronization of generated resources.</p>
</td>
</tr>
<tr>
<td>
<code>orphanDownstreamOnPolicyDelete</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.OrphanDownstreamOnPolicyDeleteConfiguration">
OrphanDownstreamOnPolicyDeleteConfiguration
</a>
</em>
</td>
<td>
<p>OrphanDownstreamOnPolicyDelete defines the configuration for orphaning downstream resources on policy delete.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.GeneratingPolicySpec">GeneratingPolicySpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicy">GeneratingPolicy</a>)
</p>
<p>
<p>GeneratingPolicySpec is the specification of the desired behavior of the GeneratingPolicy.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>matchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1-admissionregistration">
Kubernetes admissionregistration/v1.MatchResources
</a>
</em>
</td>
<td>
<p>MatchConstraints specifies what resources will trigger this policy.
The AdmissionPolicy cares about a request if it matches <em>all</em> Constraints.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConditions is a list of conditions that must be met for a request to be validated.
Match conditions filter requests that have already been matched by the rules,
namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.
There are a maximum of 64 match conditions allowed.</p>
<p>If a parameter object is provided, it can be accessed via the <code>params</code> handle in the same
manner as validation expressions.</p>
<p>The exact matching logic is (in order):
1. If ANY matchCondition evaluates to FALSE, the policy is skipped.
2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.
3. If any matchCondition evaluates to an error (but none are FALSE):
- If failurePolicy=Fail, reject the request
- If failurePolicy=Ignore, the policy is skipped</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.
The variables defined here will be available under <code>variables</code> in other expressions of the policy
except MatchConditions because MatchConditions are evaluated before the rest of the policy.</p>
<p>The expression of a variable can refer to other variables defined earlier in the list but not those after.
Thus, Variables must be sorted by the order of first appearance and acyclic.</p>
</td>
</tr>
<tr>
<td>
<code>evaluation</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicyEvaluationConfiguration">
GeneratingPolicyEvaluationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>EvaluationConfiguration defines the configuration for the policy evaluation.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration defines the configuration for the webhook.</p>
</td>
</tr>
<tr>
<td>
<code>generate</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Generation">
[]Generation
</a>
</em>
</td>
<td>
<p>Generation defines a set of CEL expressions that will be evaluated to generate resources.
Required.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.GeneratingPolicyStatus">GeneratingPolicyStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicy">GeneratingPolicy</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>conditionStatus</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ConditionStatus">
ConditionStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Generation">Generation
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicySpec">GeneratingPolicySpec</a>)
</p>
<p>
<p>Generation defines the configuration for the generation of resources.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>expression</code><br/>
<em>
string
</em>
</td>
<td>
<p>Expression is a CEL expression that takes a list of resources to be generated.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.GenericPolicy">GenericPolicy
</h3>
<p>
</p>
<h3 id="policies.kyverno.io/v1alpha1.Identity">Identity
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Keyless">Keyless</a>)
</p>
<p>
<p>Identity may contain the issuer and/or the subject found in the transparency
log.
Issuer/Subject uses a strict match, while IssuerRegExp and SubjectRegExp
apply a regexp for matching.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>issuer</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Issuer defines the issuer for this identity.</p>
</td>
</tr>
<tr>
<td>
<code>subject</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Subject defines the subject for this identity.</p>
</td>
</tr>
<tr>
<td>
<code>issuerRegExp</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>IssuerRegExp specifies a regular expression to match the issuer for this identity.</p>
</td>
</tr>
<tr>
<td>
<code>subjectRegExp</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>SubjectRegExp specifies a regular expression to match the subject for this identity.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ImageExtractor">ImageExtractor
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">ImageValidatingPolicySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the name for this imageList. It is used to refer to the images in verification block as images.<name></p>
</td>
</tr>
<tr>
<td>
<code>expression</code><br/>
<em>
string
</em>
</td>
<td>
<p>Expression defines CEL expression to extract images from the resource.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogen">ImageValidatingPolicyAutogen
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogenStatus">ImageValidatingPolicyAutogenStatus</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>targets</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Target">
[]Target
</a>
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">
ImageValidatingPolicySpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
</table>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogenConfiguration">ImageValidatingPolicyAutogenConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">ImageValidatingPolicySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>podControllers</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.PodControllersGenerationConfiguration">
PodControllersGenerationConfiguration
</a>
</em>
</td>
<td>
<p>PodControllers specifies whether to generate a pod controllers rules.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogenStatus">ImageValidatingPolicyAutogenStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyStatus">ImageValidatingPolicyStatus</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>configs</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogen">
map[string]github.com/kyverno/kyverno/api/policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogen
</a>
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">ImageValidatingPolicySpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicy">ImageValidatingPolicy</a>, 
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogen">ImageValidatingPolicyAutogen</a>)
</p>
<p>
<p>ImageValidatingPolicySpec is the specification of the desired behavior of the ImageValidatingPolicy.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>matchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1-admissionregistration">
Kubernetes admissionregistration/v1.MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConstraints specifies what resources this policy is designed to validate.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#failurepolicytype-v1-admissionregistration">
Kubernetes admissionregistration/v1.FailurePolicyType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>FailurePolicy defines how to handle failures for the admission policy. Failures can
occur from CEL expression parse errors, type check errors, runtime errors and invalid
or mis-configured policy definitions or bindings.</p>
</td>
</tr>
<tr>
<td>
<code>auditAnnotations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#auditannotation-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.AuditAnnotation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>auditAnnotations contains CEL expressions which are used to produce audit
annotations for the audit event of the API request.
validations and auditAnnotations may not both be empty; a least one of validations or auditAnnotations is
required.</p>
</td>
</tr>
<tr>
<td>
<code>validationActions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#validationaction-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.ValidationAction
</a>
</em>
</td>
<td>
<p>ValidationAction specifies the action to be taken when the matched resource violates the policy.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConditions is a list of conditions that must be met for a request to be validated.
Match conditions filter requests that have already been matched by the rules,
namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.
There are a maximum of 64 match conditions allowed.</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.</p>
</td>
</tr>
<tr>
<td>
<code>validationConfigurations</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ValidationConfiguration">
ValidationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ValidationConfigurations defines settings for mutating and verifying image digests, and enforcing image verification through signatures.</p>
</td>
</tr>
<tr>
<td>
<code>matchImageReferences</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MatchImageReference">
[]MatchImageReference
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchImageReferences is a list of Glob and CELExpressions to match images.
Any image that matches one of the rules is considered for validation
Any image that does not match a rule is skipped, even when they are passed as arguments to
image verification functions</p>
</td>
</tr>
<tr>
<td>
<code>credentials</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Credentials">
Credentials
</a>
</em>
</td>
<td>
<p>Credentials provides credentials that will be used for authentication with registry.</p>
</td>
</tr>
<tr>
<td>
<code>images</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ImageExtractor">
[]ImageExtractor
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ImageExtractors is a list of CEL expression to extract images from the resource</p>
</td>
</tr>
<tr>
<td>
<code>attestors</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Attestor">
[]Attestor
</a>
</em>
</td>
<td>
<p>Attestors provides a list of trusted authorities.</p>
</td>
</tr>
<tr>
<td>
<code>attestations</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Attestation">
[]Attestation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Attestations provides a list of image metadata to verify</p>
</td>
</tr>
<tr>
<td>
<code>validations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#validation-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Validation
</a>
</em>
</td>
<td>
<p>Validations contain CEL expressions which is used to apply the image validation checks.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration defines the configuration for the webhook.</p>
</td>
</tr>
<tr>
<td>
<code>evaluation</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.EvaluationConfiguration">
EvaluationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>EvaluationConfiguration defines the configuration for the policy evaluation.</p>
</td>
</tr>
<tr>
<td>
<code>autogen</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogenConfiguration">
ImageValidatingPolicyAutogenConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AutogenConfiguration defines the configuration for the generation controller.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ImageValidatingPolicyStatus">ImageValidatingPolicyStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicy">ImageValidatingPolicy</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>conditionStatus</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ConditionStatus">
ConditionStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>autogen</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogenStatus">
ImageValidatingPolicyAutogenStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.InToto">InToto
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Attestation">Attestation</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>type</code><br/>
<em>
string
</em>
</td>
<td>
<p>Type defines the type of attestation contained within the statement.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Key">Key
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Cosign">Cosign</a>)
</p>
<p>
<p>A Key must specify only one of CEL, Data or KMS</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>data</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Data contains the inline public key</p>
</td>
</tr>
<tr>
<td>
<code>kms</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>KMS contains the KMS url of the public key
Supported formats differ based on the KMS system used.</p>
</td>
</tr>
<tr>
<td>
<code>hashAlgorithm</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>HashAlgorithm specifues signature algorithm for public keys. Supported values are
sha224, sha256, sha384 and sha512. Defaults to sha256.</p>
</td>
</tr>
<tr>
<td>
<code>expression</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Expression is a Expression expression that returns the public key.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Keyless">Keyless
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Cosign">Cosign</a>)
</p>
<p>
<p>Keyless contains location of the validating certificate and the identities
against which to verify.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>identities</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Identity">
[]Identity
</a>
</em>
</td>
<td>
<p>Identities sets a list of identities.</p>
</td>
</tr>
<tr>
<td>
<code>roots</code><br/>
<em>
string
</em>
</td>
<td>
<p>Roots is an optional set of PEM encoded trusted root certificates.
If not provided, the system roots are used.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.MAPGenerationConfiguration">MAPGenerationConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyAutogenConfiguration">MutatingPolicyAutogenConfiguration</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>enabled</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Enabled specifies whether to generate a Kubernetes MutatingAdmissionPolicy.
Optional. Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.MatchImageReference">MatchImageReference
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">ImageValidatingPolicySpec</a>)
</p>
<p>
<p>MatchImageReference defines a Glob or a CEL expression for matching images</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>glob</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Glob defines a globbing pattern for matching images</p>
</td>
</tr>
<tr>
<td>
<code>expression</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Expression defines CEL Expressions for matching images</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.MutateExistingConfiguration">MutateExistingConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyEvaluationConfiguration">MutatingPolicyEvaluationConfiguration</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>enabled</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Enabled enables mutation of existing resources. Default is false.
When spec.targetMatchConstraints is not defined, Kyverno mutates existing resources matched in spec.matchConstraints.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.MutatingPolicyAutogen">MutatingPolicyAutogen
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyAutogenStatus">MutatingPolicyAutogenStatus</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>targets</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Target">
[]Target
</a>
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicySpec">
MutatingPolicySpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
</table>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.MutatingPolicyAutogenConfiguration">MutatingPolicyAutogenConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicySpec">MutatingPolicySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>podControllers</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.PodControllersGenerationConfiguration">
PodControllersGenerationConfiguration
</a>
</em>
</td>
<td>
<p>PodControllers specifies whether to generate a pod controllers rules.</p>
</td>
</tr>
<tr>
<td>
<code>mutatingAdmissionPolicy</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MAPGenerationConfiguration">
MAPGenerationConfiguration
</a>
</em>
</td>
<td>
<p>MutatingAdmissionPolicy specifies whether to generate a Kubernetes MutatingAdmissionPolicy.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.MutatingPolicyAutogenStatus">MutatingPolicyAutogenStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyStatus">MutatingPolicyStatus</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>configs</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyAutogen">
map[string]github.com/kyverno/kyverno/api/policies.kyverno.io/v1alpha1.MutatingPolicyAutogen
</a>
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.MutatingPolicyEvaluationConfiguration">MutatingPolicyEvaluationConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicySpec">MutatingPolicySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>admission</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.AdmissionConfiguration">
AdmissionConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Admission controls policy evaluation during admission.</p>
</td>
</tr>
<tr>
<td>
<code>mutateExisting</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MutateExistingConfiguration">
MutateExistingConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MutateExisting controls whether existing resources are mutated.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.MutatingPolicySpec">MutatingPolicySpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicy">MutatingPolicy</a>, 
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyAutogen">MutatingPolicyAutogen</a>)
</p>
<p>
<p>MutatingPolicySpec is the specification of the desired behavior of the MutatingPolicy.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>matchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1alpha1-admissionregistration">
Kubernetes admissionregistration/v1alpha1.MatchResources
</a>
</em>
</td>
<td>
<p>MatchConstraints specifies what resources this policy is designed to evaluate.
The AdmissionPolicy cares about a request if it matches <em>all</em> Constraints.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#failurepolicytype-v1alpha1-admissionregistration">
Kubernetes admissionregistration/v1alpha1.FailurePolicyType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>failurePolicy defines how to handle failures for the admission policy. Failures can
occur from CEL expression parse errors, type check errors, runtime errors and invalid
or mis-configured policy definitions or bindings.</p>
<p>failurePolicy does not define how validations that evaluate to false are handled.</p>
<p>When failurePolicy is set to Fail, the validationActions field define how failures are enforced.</p>
<p>Allowed values are Ignore or Fail. Defaults to Fail.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1alpha1-admissionregistration">
[]Kubernetes admissionregistration/v1alpha1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConditions is a list of conditions that must be met for a request to be validated.
Match conditions filter requests that have already been matched by the rules,
namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.
There are a maximum of 64 match conditions allowed.</p>
<p>If a parameter object is provided, it can be accessed via the <code>params</code> handle in the same
manner as validation expressions.</p>
<p>The exact matching logic is (in order):
1. If ANY matchCondition evaluates to FALSE, the policy is skipped.
2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.
3. If any matchCondition evaluates to an error (but none are FALSE):
- If failurePolicy=Fail, reject the request
- If failurePolicy=Ignore, the policy is skipped</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1alpha1-admissionregistration">
[]Kubernetes admissionregistration/v1alpha1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.
The variables defined here will be available under <code>variables</code> in other expressions of the policy
except MatchConditions because MatchConditions are evaluated before the rest of the policy.</p>
<p>The expression of a variable can refer to other variables defined earlier in the list but not those after.
Thus, Variables must be sorted by the order of first appearance and acyclic.</p>
</td>
</tr>
<tr>
<td>
<code>autogen</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyAutogenConfiguration">
MutatingPolicyAutogenConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AutogenConfiguration defines the configuration for the generation controller.</p>
</td>
</tr>
<tr>
<td>
<code>targetMatchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1alpha1-admissionregistration">
Kubernetes admissionregistration/v1alpha1.MatchResources
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>TargetMatchConstraints specifies what target mutation resources this policy is designed to evaluate.</p>
</td>
</tr>
<tr>
<td>
<code>mutations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#mutation-v1alpha1-admissionregistration">
[]Kubernetes admissionregistration/v1alpha1.Mutation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>mutations contain operations to perform on matching objects.
mutations may not be empty; a minimum of one mutation is required.
mutations are evaluated in order, and are reinvoked according to
the reinvocationPolicy.
The mutations of a policy are invoked for each binding of this policy
and reinvocation of mutations occurs on a per binding basis.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration defines the configuration for the webhook.</p>
</td>
</tr>
<tr>
<td>
<code>evaluation</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyEvaluationConfiguration">
MutatingPolicyEvaluationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>EvaluationConfiguration defines the configuration for mutating policy evaluation.</p>
</td>
</tr>
<tr>
<td>
<code>reinvocationPolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#reinvocationpolicytype-v1-admissionregistration">
Kubernetes admissionregistration/v1.ReinvocationPolicyType
</a>
</em>
</td>
<td>
<p>reinvocationPolicy indicates whether mutations may be called multiple times per MutatingAdmissionPolicyBinding
as part of a single admission evaluation.
Allowed values are &ldquo;Never&rdquo; and &ldquo;IfNeeded&rdquo;.</p>
<p>Never: These mutations will not be called more than once per binding in a single admission evaluation.</p>
<p>IfNeeded: These mutations may be invoked more than once per binding for a single admission request and there is no guarantee of
order with respect to other admission plugins, admission webhooks, bindings of this policy and admission policies.  Mutations are only
reinvoked when mutations change the object after this mutation is invoked.
Required.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.MutatingPolicyStatus">MutatingPolicyStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicy">MutatingPolicy</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>conditionStatus</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ConditionStatus">
ConditionStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>autogen</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyAutogenStatus">
MutatingPolicyAutogenStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>generated</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Generated indicates whether a MutatingAdmissionPolicy is generated from the policy or not</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Notary">Notary
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Attestor">Attestor</a>)
</p>
<p>
<p>Notary defines attestor configuration for Notary based signatures</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>certs</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.StringOrExpression">
StringOrExpression
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Certs define the cert chain for Notary signature verification</p>
</td>
</tr>
<tr>
<td>
<code>tsaCerts</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.StringOrExpression">
StringOrExpression
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>TSACerts define the cert chain for verifying timestamps of notary signature</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.OrphanDownstreamOnPolicyDeleteConfiguration">OrphanDownstreamOnPolicyDeleteConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicyEvaluationConfiguration">GeneratingPolicyEvaluationConfiguration</a>)
</p>
<p>
<p>OrphanDownstreamOnPolicyDeleteConfiguration defines the configuration for orphaning downstream resources on policy delete.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>enabled</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Enabled controls whether generated resources should be deleted when the policy that generated
them is deleted with synchronization enabled. This option is only applicable to generate rules of the data type.
Optional. Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.PodControllersGenerationConfiguration">PodControllersGenerationConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogenConfiguration">ImageValidatingPolicyAutogenConfiguration</a>, 
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyAutogenConfiguration">MutatingPolicyAutogenConfiguration</a>, 
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyAutogenConfiguration">ValidatingPolicyAutogenConfiguration</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>controllers</code><br/>
<em>
[]string
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.PolicyConditionType">PolicyConditionType
(<code>string</code> alias)</p></h3>
<p>
</p>
<h3 id="policies.kyverno.io/v1alpha1.PolicyExceptionSpec">PolicyExceptionSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.PolicyException">PolicyException</a>)
</p>
<p>
<p>PolicyExceptionSpec stores policy exception spec</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>policyRefs</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.PolicyRef">
[]PolicyRef
</a>
</em>
</td>
<td>
<p>PolicyRefs identifies the policies to which the exception is applied.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConditions is a list of CEL expressions that must be met for a resource to be excluded.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.PolicyRef">PolicyRef
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.PolicyExceptionSpec">PolicyExceptionSpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the name of the policy</p>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
<em>
string
</em>
</td>
<td>
<p>Kind is the kind of the policy</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Referrer">Referrer
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Attestation">Attestation</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>type</code><br/>
<em>
string
</em>
</td>
<td>
<p>Type defines the type of attestation attached to the image.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Source">Source
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Cosign">Cosign</a>)
</p>
<p>
<p>Source specifies the location of the signature / attestations.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>repository</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Repository defines the location from where to pull the signature / attestations.</p>
</td>
</tr>
<tr>
<td>
<code>PullSecrets</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#localobjectreference-v1-core">
[]Kubernetes core/v1.LocalObjectReference
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>SignaturePullSecrets is an optional list of references to secrets in the
same namespace as the deploying resource for pulling any of the signatures
used by this Source.</p>
</td>
</tr>
<tr>
<td>
<code>tagPrefix</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>TagPrefix is an optional prefix that signature and attestations have.
This is the &lsquo;tag based discovery&rsquo; and in the future once references are
fully supported that should likely be the preferred way to handle these.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.StringOrExpression">StringOrExpression
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Certificate">Certificate</a>, 
<a href="#policies.kyverno.io/v1alpha1.Notary">Notary</a>)
</p>
<p>
<p>StringOrExpression contains either a raw string input or a CEL expression</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>value</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Value defines the raw string input.</p>
</td>
</tr>
<tr>
<td>
<code>expression</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Expression defines the a CEL expression input.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.SynchronizationConfiguration">SynchronizationConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicyEvaluationConfiguration">GeneratingPolicyEvaluationConfiguration</a>)
</p>
<p>
<p>SynchronizationConfiguration defines the configuration for the synchronization of generated resources.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>enabled</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Enabled controls if generated resources should be kept in-sync with their source resource.
If Synchronize is set to &ldquo;true&rdquo; changes to generated resources will be overwritten with resource
data from Data or the resource specified in the Clone declaration.
Optional. Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.TUF">TUF
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.Cosign">Cosign</a>)
</p>
<p>
<p>TUF defines the configuration to fetch sigstore root</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>root</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.TUFRoot">
TUFRoot
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Root defines the path or data of the trusted root</p>
</td>
</tr>
<tr>
<td>
<code>mirror</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Mirror is the base URL of Sigstore TUF repository</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.TUFRoot">TUFRoot
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.TUF">TUF</a>)
</p>
<p>
<p>TUFRoot defines the path or data of the trusted root</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>path</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Path is the URL or File location of the TUF root</p>
</td>
</tr>
<tr>
<td>
<code>data</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Data is the base64 encoded TUF root</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.Target">Target
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicyAutogen">ImageValidatingPolicyAutogen</a>, 
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicyAutogen">MutatingPolicyAutogen</a>, 
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyAutogen">ValidatingPolicyAutogen</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>group</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>version</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>resource</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ValidatingPolicyAutogen">ValidatingPolicyAutogen
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyAutogenStatus">ValidatingPolicyAutogenStatus</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>targets</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.Target">
[]Target
</a>
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicySpec">
ValidatingPolicySpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
</table>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ValidatingPolicyAutogenConfiguration">ValidatingPolicyAutogenConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicySpec">ValidatingPolicySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>podControllers</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.PodControllersGenerationConfiguration">
PodControllersGenerationConfiguration
</a>
</em>
</td>
<td>
<p>PodControllers specifies whether to generate a pod controllers rules.</p>
</td>
</tr>
<tr>
<td>
<code>validatingAdmissionPolicy</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.VapGenerationConfiguration">
VapGenerationConfiguration
</a>
</em>
</td>
<td>
<p>ValidatingAdmissionPolicy specifies whether to generate a Kubernetes ValidatingAdmissionPolicy.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ValidatingPolicyAutogenStatus">ValidatingPolicyAutogenStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyStatus">ValidatingPolicyStatus</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>configs</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyAutogen">
map[string]github.com/kyverno/kyverno/api/policies.kyverno.io/v1alpha1.ValidatingPolicyAutogen
</a>
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ValidatingPolicySpec">ValidatingPolicySpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicy">ValidatingPolicy</a>, 
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyAutogen">ValidatingPolicyAutogen</a>)
</p>
<p>
<p>ValidatingPolicySpec is the specification of the desired behavior of the ValidatingPolicy.</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>matchConstraints</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchresources-v1-admissionregistration">
Kubernetes admissionregistration/v1.MatchResources
</a>
</em>
</td>
<td>
<p>MatchConstraints specifies what resources this policy is designed to validate.
The AdmissionPolicy cares about a request if it matches <em>all</em> Constraints.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>validations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#validation-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Validation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Validations contain CEL expressions which is used to apply the validation.
Validations and AuditAnnotations may not both be empty; a minimum of one Validations or AuditAnnotations is
required.</p>
</td>
</tr>
<tr>
<td>
<code>failurePolicy</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#failurepolicytype-v1-admissionregistration">
Kubernetes admissionregistration/v1.FailurePolicyType
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>failurePolicy defines how to handle failures for the admission policy. Failures can
occur from CEL expression parse errors, type check errors, runtime errors and invalid
or mis-configured policy definitions or bindings.</p>
<p>failurePolicy does not define how validations that evaluate to false are handled.</p>
<p>When failurePolicy is set to Fail, the validationActions field define how failures are enforced.</p>
<p>Allowed values are Ignore or Fail. Defaults to Fail.</p>
</td>
</tr>
<tr>
<td>
<code>auditAnnotations</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#auditannotation-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.AuditAnnotation
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>auditAnnotations contains CEL expressions which are used to produce audit
annotations for the audit event of the API request.
validations and auditAnnotations may not both be empty; a least one of validations or auditAnnotations is
required.</p>
</td>
</tr>
<tr>
<td>
<code>matchConditions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#matchcondition-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.MatchCondition
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>MatchConditions is a list of conditions that must be met for a request to be validated.
Match conditions filter requests that have already been matched by the rules,
namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.
There are a maximum of 64 match conditions allowed.</p>
<p>If a parameter object is provided, it can be accessed via the <code>params</code> handle in the same
manner as validation expressions.</p>
<p>The exact matching logic is (in order):
1. If ANY matchCondition evaluates to FALSE, the policy is skipped.
2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.
3. If any matchCondition evaluates to an error (but none are FALSE):
- If failurePolicy=Fail, reject the request
- If failurePolicy=Ignore, the policy is skipped</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#variable-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.Variable
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Variables contain definitions of variables that can be used in composition of other expressions.
Each variable is defined as a named CEL expression.
The variables defined here will be available under <code>variables</code> in other expressions of the policy
except MatchConditions because MatchConditions are evaluated before the rest of the policy.</p>
<p>The expression of a variable can refer to other variables defined earlier in the list but not those after.
Thus, Variables must be sorted by the order of first appearance and acyclic.</p>
</td>
</tr>
<tr>
<td>
<code>autogen</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyAutogenConfiguration">
ValidatingPolicyAutogenConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>AutogenConfiguration defines the configuration for the generation controller.</p>
</td>
</tr>
<tr>
<td>
<code>validationActions</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#validationaction-v1-admissionregistration">
[]Kubernetes admissionregistration/v1.ValidationAction
</a>
</em>
</td>
<td>
<p>ValidationAction specifies the action to be taken when the matched resource violates the policy.
Required.</p>
</td>
</tr>
<tr>
<td>
<code>webhookConfiguration</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.WebhookConfiguration">
WebhookConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>WebhookConfiguration defines the configuration for the webhook.</p>
</td>
</tr>
<tr>
<td>
<code>evaluation</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.EvaluationConfiguration">
EvaluationConfiguration
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>EvaluationConfiguration defines the configuration for the policy evaluation.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ValidatingPolicyStatus">ValidatingPolicyStatus
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicy">ValidatingPolicy</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>conditionStatus</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ConditionStatus">
ConditionStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>autogen</code><br/>
<em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyAutogenStatus">
ValidatingPolicyAutogenStatus
</a>
</em>
</td>
<td>
<em>(Optional)</em>
</td>
</tr>
<tr>
<td>
<code>generated</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Generated indicates whether a ValidatingAdmissionPolicy/MutatingAdmissionPolicy is generated from the policy or not</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.ValidationConfiguration">ValidationConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">ImageValidatingPolicySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>mutateDigest</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>MutateDigest enables replacement of image tags with digests.
Defaults to true.</p>
</td>
</tr>
<tr>
<td>
<code>verifyDigest</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>VerifyDigest validates that images have a digest.</p>
</td>
</tr>
<tr>
<td>
<code>required</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>Required validates that images are verified, i.e., have passed a signature or attestation check.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.VapGenerationConfiguration">VapGenerationConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicyAutogenConfiguration">ValidatingPolicyAutogenConfiguration</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>enabled</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Enabled specifies whether to generate a Kubernetes ValidatingAdmissionPolicy.
Optional. Defaults to &ldquo;false&rdquo; if not specified.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="policies.kyverno.io/v1alpha1.WebhookConfiguration">WebhookConfiguration
</h3>
<p>
(<em>Appears on:</em>
<a href="#policies.kyverno.io/v1alpha1.GeneratingPolicySpec">GeneratingPolicySpec</a>, 
<a href="#policies.kyverno.io/v1alpha1.ImageValidatingPolicySpec">ImageValidatingPolicySpec</a>, 
<a href="#policies.kyverno.io/v1alpha1.MutatingPolicySpec">MutatingPolicySpec</a>, 
<a href="#policies.kyverno.io/v1alpha1.ValidatingPolicySpec">ValidatingPolicySpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>timeoutSeconds</code><br/>
<em>
int32
</em>
</td>
<td>
<p>TimeoutSeconds specifies the maximum time in seconds allowed to apply this policy.
After the configured time expires, the admission request may fail, or may simply ignore the policy results,
based on the failure policy. The default timeout is 10s, the value must be between 1 and 30 seconds.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h2 id="reports.kyverno.io/v1">reports.kyverno.io/v1</h2>
<p>
</p>
Resource Types:
<ul><li>
<a href="#reports.kyverno.io/v1.ClusterEphemeralReport">ClusterEphemeralReport</a>
</li><li>
<a href="#reports.kyverno.io/v1.EphemeralReport">EphemeralReport</a>
</li></ul>
<hr />
<h3 id="reports.kyverno.io/v1.ClusterEphemeralReport">ClusterEphemeralReport
</h3>
<p>
<p>ClusterEphemeralReport is the Schema for the ClusterEphemeralReports API</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
reports.kyverno.io/v1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>ClusterEphemeralReport</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#reports.kyverno.io/v1.EphemeralReportSpec">
EphemeralReportSpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>owner</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#ownerreference-v1-meta">
Kubernetes meta/v1.OwnerReference
</a>
</em>
</td>
<td>
<p>Owner is a reference to the report owner (e.g. a Deployment, Namespace, or Node)</p>
</td>
</tr>
<tr>
<td>
<code>summary</code><br/>
<em>
openreports.io/apis/openreports.io/v1alpha1.ReportSummary
</em>
</td>
<td>
<em>(Optional)</em>
<p>PolicyReportSummary provides a summary of results</p>
</td>
</tr>
<tr>
<td>
<code>results</code><br/>
<em>
[]openreports.io/apis/openreports.io/v1alpha1.ReportResult
</em>
</td>
<td>
<em>(Optional)</em>
<p>PolicyReportResult provides result details</p>
</td>
</tr>
</table>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="reports.kyverno.io/v1.EphemeralReport">EphemeralReport
</h3>
<p>
<p>EphemeralReport is the Schema for the EphemeralReports API</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
reports.kyverno.io/v1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>EphemeralReport</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#reports.kyverno.io/v1.EphemeralReportSpec">
EphemeralReportSpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>owner</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#ownerreference-v1-meta">
Kubernetes meta/v1.OwnerReference
</a>
</em>
</td>
<td>
<p>Owner is a reference to the report owner (e.g. a Deployment, Namespace, or Node)</p>
</td>
</tr>
<tr>
<td>
<code>summary</code><br/>
<em>
openreports.io/apis/openreports.io/v1alpha1.ReportSummary
</em>
</td>
<td>
<em>(Optional)</em>
<p>PolicyReportSummary provides a summary of results</p>
</td>
</tr>
<tr>
<td>
<code>results</code><br/>
<em>
[]openreports.io/apis/openreports.io/v1alpha1.ReportResult
</em>
</td>
<td>
<em>(Optional)</em>
<p>PolicyReportResult provides result details</p>
</td>
</tr>
</table>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="reports.kyverno.io/v1.EphemeralReportSpec">EphemeralReportSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#reports.kyverno.io/v1.ClusterEphemeralReport">ClusterEphemeralReport</a>, 
<a href="#reports.kyverno.io/v1.EphemeralReport">EphemeralReport</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>owner</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#ownerreference-v1-meta">
Kubernetes meta/v1.OwnerReference
</a>
</em>
</td>
<td>
<p>Owner is a reference to the report owner (e.g. a Deployment, Namespace, or Node)</p>
</td>
</tr>
<tr>
<td>
<code>summary</code><br/>
<em>
openreports.io/apis/openreports.io/v1alpha1.ReportSummary
</em>
</td>
<td>
<em>(Optional)</em>
<p>PolicyReportSummary provides a summary of results</p>
</td>
</tr>
<tr>
<td>
<code>results</code><br/>
<em>
[]openreports.io/apis/openreports.io/v1alpha1.ReportResult
</em>
</td>
<td>
<em>(Optional)</em>
<p>PolicyReportResult provides result details</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="reports.kyverno.io/v1.ReportInterface">ReportInterface
</h3>
<p>
<p>ReportInterface abstracts the concrete report change request type</p>
</p>
<h2 id="wgpolicyk8s.io/v1alpha2">wgpolicyk8s.io/v1alpha2</h2>
Resource Types:
<ul><li>
<a href="#wgpolicyk8s.io/v1alpha2.ClusterPolicyReport">ClusterPolicyReport</a>
</li></ul>
<hr />
<h3 id="wgpolicyk8s.io/v1alpha2.ClusterPolicyReport">ClusterPolicyReport
</h3>
<p>
<p>ClusterPolicyReport is the Schema for the clusterpolicyreports API</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
wgpolicyk8s.io/v1alpha2
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>ClusterPolicyReport</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>scope</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectreference-v1-core">
Kubernetes core/v1.ObjectReference
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Scope is an optional reference to the report scope (e.g. a Deployment, Namespace, or Node)</p>
</td>
</tr>
<tr>
<td>
<code>scopeSelector</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#labelselector-v1-meta">
Kubernetes meta/v1.LabelSelector
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ScopeSelector is an optional selector for multiple scopes (e.g. Pods).
Either one of, or none of, but not both of, Scope or ScopeSelector should be specified.</p>
</td>
</tr>
<tr>
<td>
<code>summary</code><br/>
<em>
<a href="#wgpolicyk8s.io/v1alpha2.PolicyReportSummary">
PolicyReportSummary
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PolicyReportSummary provides a summary of results</p>
</td>
</tr>
<tr>
<td>
<code>results</code><br/>
<em>
<a href="#wgpolicyk8s.io/v1alpha2.PolicyReportResult">
[]PolicyReportResult
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PolicyReportResult provides result details</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="wgpolicyk8s.io/v1alpha2.PolicyReport">PolicyReport
</h3>
<p>
<p>PolicyReport is the Schema for the policyreports API</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>scope</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectreference-v1-core">
Kubernetes core/v1.ObjectReference
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Scope is an optional reference to the report scope (e.g. a Deployment, Namespace, or Node)</p>
</td>
</tr>
<tr>
<td>
<code>scopeSelector</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#labelselector-v1-meta">
Kubernetes meta/v1.LabelSelector
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>ScopeSelector is an optional selector for multiple scopes (e.g. Pods).
Either one of, or none of, but not both of, Scope or ScopeSelector should be specified.</p>
</td>
</tr>
<tr>
<td>
<code>summary</code><br/>
<em>
<a href="#wgpolicyk8s.io/v1alpha2.PolicyReportSummary">
PolicyReportSummary
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PolicyReportSummary provides a summary of results</p>
</td>
</tr>
<tr>
<td>
<code>results</code><br/>
<em>
<a href="#wgpolicyk8s.io/v1alpha2.PolicyReportResult">
[]PolicyReportResult
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>PolicyReportResult provides result details</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="wgpolicyk8s.io/v1alpha2.PolicyReportResult">PolicyReportResult
</h3>
<p>
(<em>Appears on:</em>
<a href="#wgpolicyk8s.io/v1alpha2.ClusterPolicyReport">ClusterPolicyReport</a>, 
<a href="#wgpolicyk8s.io/v1alpha2.PolicyReport">PolicyReport</a>)
</p>
<p>
<p>PolicyReportResult provides the result for an individual policy</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>source</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Source is an identifier for the policy engine that manages this report</p>
</td>
</tr>
<tr>
<td>
<code>policy</code><br/>
<em>
string
</em>
</td>
<td>
<p>Policy is the name or identifier of the policy</p>
</td>
</tr>
<tr>
<td>
<code>rule</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Rule is the name or identifier of the rule within the policy</p>
</td>
</tr>
<tr>
<td>
<code>resources</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectreference-v1-core">
[]Kubernetes core/v1.ObjectReference
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Subjects is an optional reference to the checked Kubernetes resources</p>
</td>
</tr>
<tr>
<td>
<code>resourceSelector</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#labelselector-v1-meta">
Kubernetes meta/v1.LabelSelector
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>SubjectSelector is an optional label selector for checked Kubernetes resources.
For example, a policy result may apply to all pods that match a label.
Either a Subject or a SubjectSelector can be specified.
If neither are provided, the result is assumed to be for the policy report scope.</p>
</td>
</tr>
<tr>
<td>
<code>message</code><br/>
<em>
string
</em>
</td>
<td>
<p>Description is a short user friendly message for the policy rule</p>
</td>
</tr>
<tr>
<td>
<code>result</code><br/>
<em>
<a href="#wgpolicyk8s.io/v1alpha2.PolicyResult">
PolicyResult
</a>
</em>
</td>
<td>
<p>Result indicates the outcome of the policy rule execution</p>
</td>
</tr>
<tr>
<td>
<code>scored</code><br/>
<em>
bool
</em>
</td>
<td>
<p>Scored indicates if this result is scored</p>
</td>
</tr>
<tr>
<td>
<code>properties</code><br/>
<em>
map[string]string
</em>
</td>
<td>
<p>Properties provides additional information for the policy rule</p>
</td>
</tr>
<tr>
<td>
<code>timestamp</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#timestamp-v1-meta">
Kubernetes meta/v1.Timestamp
</a>
</em>
</td>
<td>
<p>Timestamp indicates the time the result was found</p>
</td>
</tr>
<tr>
<td>
<code>category</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Category indicates policy category</p>
</td>
</tr>
<tr>
<td>
<code>severity</code><br/>
<em>
<a href="#wgpolicyk8s.io/v1alpha2.PolicySeverity">
PolicySeverity
</a>
</em>
</td>
<td>
<em>(Optional)</em>
<p>Severity indicates policy check result criticality</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="wgpolicyk8s.io/v1alpha2.PolicyReportSummary">PolicyReportSummary
</h3>
<p>
(<em>Appears on:</em>
<a href="#wgpolicyk8s.io/v1alpha2.ClusterPolicyReport">ClusterPolicyReport</a>, 
<a href="#wgpolicyk8s.io/v1alpha2.PolicyReport">PolicyReport</a>)
</p>
<p>
<p>PolicyReportSummary provides a status count summary</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>pass</code><br/>
<em>
int
</em>
</td>
<td>
<em>(Optional)</em>
<p>Pass provides the count of policies whose requirements were met</p>
</td>
</tr>
<tr>
<td>
<code>fail</code><br/>
<em>
int
</em>
</td>
<td>
<em>(Optional)</em>
<p>Fail provides the count of policies whose requirements were not met</p>
</td>
</tr>
<tr>
<td>
<code>warn</code><br/>
<em>
int
</em>
</td>
<td>
<em>(Optional)</em>
<p>Warn provides the count of non-scored policies whose requirements were not met</p>
</td>
</tr>
<tr>
<td>
<code>error</code><br/>
<em>
int
</em>
</td>
<td>
<em>(Optional)</em>
<p>Error provides the count of policies that could not be evaluated</p>
</td>
</tr>
<tr>
<td>
<code>skip</code><br/>
<em>
int
</em>
</td>
<td>
<em>(Optional)</em>
<p>Skip indicates the count of policies that were not selected for evaluation</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="wgpolicyk8s.io/v1alpha2.PolicyResult">PolicyResult
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#wgpolicyk8s.io/v1alpha2.PolicyReportResult">PolicyReportResult</a>)
</p>
<p>
<p>PolicyResult has one of the following values:
- pass: indicates that the policy requirements are met
- fail: indicates that the policy requirements are not met
- warn: indicates that the policy requirements and not met, and the policy is not scored
- error: indicates that the policy could not be evaluated
- skip: indicates that the policy was not selected based on user inputs or applicability</p>
</p>
<h3 id="wgpolicyk8s.io/v1alpha2.PolicySeverity">PolicySeverity
(<code>string</code> alias)</p></h3>
<p>
(<em>Appears on:</em>
<a href="#wgpolicyk8s.io/v1alpha2.PolicyReportResult">PolicyReportResult</a>)
</p>
<p>
<p>PolicySeverity has one of the following values:
- critical
- high
- low
- medium
- info</p>
</p>
</div>
<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
</body>
</html>

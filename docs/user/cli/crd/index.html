<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
<title>Kyverno API</title>
<style>
.bg-blue {
color: #ffffff;
background-color: #1589dd;
}
</style>
</head>
<body>
<div class="container">
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
<a class="navbar-brand" href="#"><p><b>Packages : </b></p></a>
<ul style="list-style:none">
<li>
<a href="#cli.kyverno.io%2fv1alpha1"><b style="color: white">cli.kyverno.io/v1alpha1</b></a>
</li>
</ul>
</nav>
<h2 id="cli.kyverno.io/v1alpha1">cli.kyverno.io/v1alpha1</h2>
Resource Types:
<ul><li>
<a href="#cli.kyverno.io/v1alpha1.Context">Context</a>
</li><li>
<a href="#cli.kyverno.io/v1alpha1.Test">Test</a>
</li><li>
<a href="#cli.kyverno.io/v1alpha1.UserInfo">UserInfo</a>
</li><li>
<a href="#cli.kyverno.io/v1alpha1.Values">Values</a>
</li></ul>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.Context">Context
</h3>
<p>
<p>Values declares values to be loaded by the Kyverno CLI</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
cli.kyverno.io/v1alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>Context</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>spec</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.ContextSpec">
ContextSpec
</a>
</em>
</td>
<td>
<br/>
<br/>
<table class="table table-striped">
<tr>
<td>
<code>resources</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#unstructured-unstructured-v1">
[]Kubernetes meta/v1/unstructured.Unstructured
</a>
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>images</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.ImageData">
[]ImageData
</a>
</em>
</td>
<td>
</td>
</tr>
</table>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.Test">Test
</h3>
<p>
<p>Test declares a test</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
cli.kyverno.io/v1alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>Test</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the name of the test.
This field is deprecated, use <code>metadata.name</code> instead</p>
</td>
</tr>
<tr>
<td>
<code>policies</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>Policies are the policies to be used in the test</p>
</td>
</tr>
<tr>
<td>
<code>resources</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>Resources are the resource to be used in the test</p>
</td>
</tr>
<tr>
<td>
<code>jsonPayload</code><br/>
<em>
string
</em>
</td>
<td>
<p>JSONPayload is the JSON payload to be used in the test</p>
</td>
</tr>
<tr>
<td>
<code>targetResources</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>Target Resources are for policies that have mutate existing</p>
</td>
</tr>
<tr>
<td>
<code>variables</code><br/>
<em>
string
</em>
</td>
<td>
<p>Variables is the values to be used in the test</p>
</td>
</tr>
<tr>
<td>
<code>userinfo</code><br/>
<em>
string
</em>
</td>
<td>
<p>UserInfo is the user info to be used in the test</p>
</td>
</tr>
<tr>
<td>
<code>results</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.TestResult">
[]TestResult
</a>
</em>
</td>
<td>
<p>Results are the results to be checked in the test</p>
</td>
</tr>
<tr>
<td>
<code>checks</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.CheckResult">
[]CheckResult
</a>
</em>
</td>
<td>
<p>Checks are the verifications to be checked in the test</p>
</td>
</tr>
<tr>
<td>
<code>values</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.ValuesSpec">
ValuesSpec
</a>
</em>
</td>
<td>
<p>Values are the values to be used in the test</p>
</td>
</tr>
<tr>
<td>
<code>exceptions</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>PolicyExceptions are the policy exceptions to be used in the test</p>
</td>
</tr>
<tr>
<td>
<code>context</code><br/>
<em>
string
</em>
</td>
<td>
<p>Context file containing context data for CEL policies</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.UserInfo">UserInfo
</h3>
<p>
<p>UserInfo declares user infos to be loaded by the Kyverno CLI</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
cli.kyverno.io/v1alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>UserInfo</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>RequestInfo</code><br/>
<em>
github.com/kyverno/kyverno/api/kyverno/v2.RequestInfo
</em>
</td>
<td>
<p>
(Members of <code>RequestInfo</code> are embedded into this type.)
</p>
<p>RequestInfo declares user infos</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.Values">Values
</h3>
<p>
<p>Values declares values to be loaded by the Kyverno CLI</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>apiVersion</code><br/>
string</td>
<td>
<code>
cli.kyverno.io/v1alpha1
</code>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
string
</td>
<td><code>Values</code></td>
</tr>
<tr>
<td>
<code>metadata</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#objectmeta-v1-meta">
Kubernetes meta/v1.ObjectMeta
</a>
</em>
</td>
<td>
Refer to the Kubernetes API documentation for the fields of the
<code>metadata</code> field.
</td>
</tr>
<tr>
<td>
<code>ValuesSpec</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.ValuesSpec">
ValuesSpec
</a>
</em>
</td>
<td>
<p>
(Members of <code>ValuesSpec</code> are embedded into this type.)
</p>
<p>ValuesSpec declares values</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.CheckMatch">CheckMatch
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.CheckResult">CheckResult</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>resource</code><br/>
<em>
github.com/kyverno/kyverno-json/pkg/apis/policy/v1alpha1.Any
</em>
</td>
<td>
<p>Resource filters engine responses</p>
</td>
</tr>
<tr>
<td>
<code>policy</code><br/>
<em>
github.com/kyverno/kyverno-json/pkg/apis/policy/v1alpha1.Any
</em>
</td>
<td>
<p>Policy filters engine responses</p>
</td>
</tr>
<tr>
<td>
<code>rule</code><br/>
<em>
github.com/kyverno/kyverno-json/pkg/apis/policy/v1alpha1.Any
</em>
</td>
<td>
<p>Rule filters rule responses</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.CheckResult">CheckResult
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.Test">Test</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>match</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.CheckMatch">
CheckMatch
</a>
</em>
</td>
<td>
<p>Match tells how to match relevant rule responses</p>
</td>
</tr>
<tr>
<td>
<code>assert</code><br/>
<em>
github.com/kyverno/kyverno-json/pkg/apis/policy/v1alpha1.Any
</em>
</td>
<td>
<p>Assert contains assertion to be performed on the relevant rule responses</p>
</td>
</tr>
<tr>
<td>
<code>error</code><br/>
<em>
github.com/kyverno/kyverno-json/pkg/apis/policy/v1alpha1.Any
</em>
</td>
<td>
<p>Error contains negative assertion to be performed on the relevant rule responses</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.ContextSpec">ContextSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.Context">Context</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>resources</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#unstructured-unstructured-v1">
[]Kubernetes meta/v1/unstructured.Unstructured
</a>
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>images</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.ImageData">
[]ImageData
</a>
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.ImageData">ImageData
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.ContextSpec">ContextSpec</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>image</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>resolvedImage</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>registry</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>repository</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>tag</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>digest</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>imageIndex</code><br/>
<em>
github.com/kyverno/kyverno-json/pkg/apis/policy/v1alpha1.Any
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>manifest</code><br/>
<em>
github.com/kyverno/kyverno-json/pkg/apis/policy/v1alpha1.Any
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>config</code><br/>
<em>
github.com/kyverno/kyverno-json/pkg/apis/policy/v1alpha1.Any
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.NamespaceSelector">NamespaceSelector
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.ValuesSpec">ValuesSpec</a>)
</p>
<p>
<p>NamespaceSelector declares labels for a given namespace</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the namespace name</p>
</td>
</tr>
<tr>
<td>
<code>labels</code><br/>
<em>
map[string]string
</em>
</td>
<td>
<p>Labels are the labels for the given namespace</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.Policy">Policy
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.ValuesSpec">ValuesSpec</a>)
</p>
<p>
<p>Policy declares values for a given policy</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the policy name</p>
</td>
</tr>
<tr>
<td>
<code>resources</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.Resource">
[]Resource
</a>
</em>
</td>
<td>
<p>Resources are values for specific resources</p>
</td>
</tr>
<tr>
<td>
<code>rules</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.Rule">
[]Rule
</a>
</em>
</td>
<td>
<p>Rules are values for specific policy rules</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.Resource">Resource
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.Policy">Policy</a>)
</p>
<p>
<p>Resource declares values for a given resource</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the name of the resource</p>
</td>
</tr>
<tr>
<td>
<code>values</code><br/>
<em>
map[string]interface{}
</em>
</td>
<td>
<p>Values are the values for the given resource</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.Rule">Rule
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.Policy">Policy</a>)
</p>
<p>
<p>Rule declares values for a given policy rule</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
<p>Name is the name of the ppolicy rule</p>
</td>
</tr>
<tr>
<td>
<code>values</code><br/>
<em>
map[string]interface{}
</em>
</td>
<td>
<p>Values are the values for the given policy rule</p>
</td>
</tr>
<tr>
<td>
<code>foreachValues</code><br/>
<em>
map[string][]interface{}
</em>
</td>
<td>
<p>ForeachValues are the foreach values for the given policy rule</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.Subresource">Subresource
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.ValuesSpec">ValuesSpec</a>)
</p>
<p>
<p>Subresource declares subresource/parent resource mapping</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>subresource</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#apiresource-v1-meta">
Kubernetes meta/v1.APIResource
</a>
</em>
</td>
<td>
<p>Subresource declares the subresource api</p>
</td>
</tr>
<tr>
<td>
<code>parentResource</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#apiresource-v1-meta">
Kubernetes meta/v1.APIResource
</a>
</em>
</td>
<td>
<p>ParentResource declares the parent resource api</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.TestResourceSpec">TestResourceSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.TestResultData">TestResultData</a>)
</p>
<p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>group</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>version</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>namespace</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>subresource</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
<tr>
<td>
<code>name</code><br/>
<em>
string
</em>
</td>
<td>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.TestResult">TestResult
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.Test">Test</a>)
</p>
<p>
<p>TestResult declares a test result</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>TestResultBase</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.TestResultBase">
TestResultBase
</a>
</em>
</td>
<td>
<p>
(Members of <code>TestResultBase</code> are embedded into this type.)
</p>
</td>
</tr>
<tr>
<td>
<code>TestResultData</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.TestResultData">
TestResultData
</a>
</em>
</td>
<td>
<p>
(Members of <code>TestResultData</code> are embedded into this type.)
</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.TestResultBase">TestResultBase
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.TestResult">TestResult</a>)
</p>
<p>
<p>TestResultBase declares a test result base fields</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>policy</code><br/>
<em>
string
</em>
</td>
<td>
<p>Policy mentions the name of the policy.</p>
</td>
</tr>
<tr>
<td>
<code>rule</code><br/>
<em>
string
</em>
</td>
<td>
<em>(Optional)</em>
<p>Rule mentions the name of the rule in the policy.
It&rsquo;s required in case policy is a kyverno policy.</p>
</td>
</tr>
<tr>
<td>
<code>isValidatingAdmissionPolicy</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>IsValidatingAdmissionPolicy indicates if the policy is a validating admission policy.
It&rsquo;s required in case the policy is a validating admission policy.</p>
</td>
</tr>
<tr>
<td>
<code>isMutatingAdmissionPolicy</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>IsMutatingAdmissionPolicy indicates if the policy is a mutating admission policy.</p>
</td>
</tr>
<tr>
<td>
<code>isValidatingPolicy</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>IsValidatingPolicy indicates if the policy is a validating policy.
It&rsquo;s required in case the policy is a validating policy.</p>
</td>
</tr>
<tr>
<td>
<code>isDeletingPolicy</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>IsDeletingPolicy indicates if the policy is a deleting policy.
It&rsquo;s required in case the policy is a deleting policy.</p>
</td>
</tr>
<tr>
<td>
<code>isImageValidatingPolicy</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>IsImageValidatingPolicy indicates if the policy is an image validating policy.
It&rsquo;s required in case the policy is an image validating policy.</p>
</td>
</tr>
<tr>
<td>
<code>isGeneratingPolicy</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>IsGeneratingPolicy indicates if the policy is a generating policy.
It&rsquo;s required in case the policy is a generating policy.</p>
</td>
</tr>
<tr>
<td>
<code>isMutatingPolicy</code><br/>
<em>
bool
</em>
</td>
<td>
<em>(Optional)</em>
<p>IsMutatingPolicy indicates if the policy is a mutating policy.
It&rsquo;s required in case the policy is a mutating policy.</p>
</td>
</tr>
<tr>
<td>
<code>result</code><br/>
<em>
openreports.io/apis/openreports.io/v1alpha1.Result
</em>
</td>
<td>
<p>Result mentions the result that the user is expecting.
Possible values are pass, fail and skip.</p>
</td>
</tr>
<tr>
<td>
<code>kind</code><br/>
<em>
string
</em>
</td>
<td>
<p>Kind mentions the kind of the resource on which the policy is to be applied.</p>
</td>
</tr>
<tr>
<td>
<code>patchedResources</code><br/>
<em>
string
</em>
</td>
<td>
<p>PatchedResource takes a resource configuration file in yaml format from
the user to compare it against the Kyverno mutated resource configuration.
Multiple resources can be passed in the same file</p>
</td>
</tr>
<tr>
<td>
<code>generatedResource</code><br/>
<em>
string
</em>
</td>
<td>
<p>GeneratedResource takes a resource configuration file in yaml format from
the user to compare it against the Kyverno generated resource configuration.</p>
</td>
</tr>
<tr>
<td>
<code>cloneSourceResource</code><br/>
<em>
string
</em>
</td>
<td>
<p>CloneSourceResource takes the resource configuration file in yaml format
from the user which is meant to be cloned by the generate rule.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.TestResultData">TestResultData
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.TestResult">TestResult</a>)
</p>
<p>
<p>TestResultData declares a test result data</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>resources</code><br/>
<em>
[]string
</em>
</td>
<td>
<p>Resources gives us the list of resources on which the policy is going to be applied.</p>
</td>
</tr>
<tr>
<td>
<code>resourceSpecs</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.TestResourceSpec">
[]TestResourceSpec
</a>
</em>
</td>
<td>
<p>Resources gives us the list of resources on which the policy is going to be applied.</p>
</td>
</tr>
</tbody>
</table>
<hr />
<h3 id="cli.kyverno.io/v1alpha1.ValuesSpec">ValuesSpec
</h3>
<p>
(<em>Appears on:</em>
<a href="#cli.kyverno.io/v1alpha1.Test">Test</a>, 
<a href="#cli.kyverno.io/v1alpha1.Values">Values</a>)
</p>
<p>
<p>ValuesSpec declares values to be loaded by the Kyverno CLI</p>
</p>
<table class="table table-striped">
<thead class="thead-dark">
<tr>
<th>Field</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<code>globalValues</code><br/>
<em>
map[string]interface{}
</em>
</td>
<td>
<p>GlobalValues are the global values</p>
</td>
</tr>
<tr>
<td>
<code>policies</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.Policy">
[]Policy
</a>
</em>
</td>
<td>
<p>Policies are the policy values</p>
</td>
</tr>
<tr>
<td>
<code>namespaceSelector</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.NamespaceSelector">
[]NamespaceSelector
</a>
</em>
</td>
<td>
<p>NamespaceSelectors are the namespace labels</p>
</td>
</tr>
<tr>
<td>
<code>namespaces</code><br/>
<em>
<a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.23/#namespace-v1-core">
[]Kubernetes core/v1.Namespace
</a>
</em>
</td>
<td>
<p>Namespaces are the namespaces</p>
</td>
</tr>
<tr>
<td>
<code>subresources</code><br/>
<em>
<a href="#cli.kyverno.io/v1alpha1.Subresource">
[]Subresource
</a>
</em>
</td>
<td>
<p>Subresources are the subresource/parent resource mappings</p>
</td>
</tr>
</tbody>
</table>
<hr />
</div>
<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
</body>
</html>

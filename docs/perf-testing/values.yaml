features:
  admissionReports:
    enabled: false
  omitEvents:
    eventTypes:
    - PolicyViolation
    - PolicyApplied
    - PolicyError
    - PolicySkipped

admissionController:

  serviceMonitor:
    enabled: true

  container:
    image:
      tag: release-1.11

    resources:
      limits:
        memory: 2Gi
      requests:
        cpu: 1
        memory: 1Gi

reportsController:
  serviceMonitor:
    enabled: true

  container:
    image:
      tag: release-1.11

    resources:
      limits:
        memory: 10Gi
      requests:
        cpu: 1
        memory: 1Gi
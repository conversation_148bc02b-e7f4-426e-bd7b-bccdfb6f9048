---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: (devel)
  name: contexts.cli.kyverno.io
spec:
  group: cli.kyverno.io
  names:
    kind: Context
    listKind: ContextList
    plural: contexts
    singular: context
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Values declares values to be loaded by the Kyverno CLI
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              images:
                items:
                  properties:
                    config:
                      description: Any can be any type.
                      type: object
                      x-kubernetes-preserve-unknown-fields: true
                    digest:
                      type: string
                    image:
                      type: string
                    imageIndex:
                      description: Any can be any type.
                      type: object
                      x-kubernetes-preserve-unknown-fields: true
                    manifest:
                      description: Any can be any type.
                      type: object
                      x-kubernetes-preserve-unknown-fields: true
                    registry:
                      type: string
                    repository:
                      type: string
                    resolvedImage:
                      type: string
                    tag:
                      type: string
                  required:
                  - image
                  - registry
                  - repository
                  - resolvedImage
                  type: object
                type: array
              resources:
                items:
                  type: object
                type: array
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true

package commands

var websiteUrl = `https://kyverno.io/docs/kyverno-cli`

var description = []string{
	`Kubernetes Native Policy Management.`,
	``,
	`The Kyverno CLI provides a command-line interface to work with Kyverno resources.`,
	`It can be used to validate and test policy behavior to resources prior to adding them to a cluster.`,
	``,
	`The Kyverno CLI comes with additional commands to help creating and manipulating various Kyverno resources.`,
	``,
	`NOTE: To enable experimental commands, environment variable "KYVERNO_EXPERIMENTAL" should be set true or 1.`,
}

apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  creationTimestamp: null
  name: add-network-policy
spec:
  steps:
  - name: step-01
    try:
    - apply:
        file: old-resource.yaml
  - name: step-02
    try:
    - apply:
        file: ../add-network-policy.yaml
    - assert:
        file: policy-ready.yaml
  - name: step-03
    try:
    - apply:
        file: ../.kyverno-test/resource.yaml
  - name: step-04
    try:
    - assert:
        file: ../.kyverno-test/generatedResource.yaml
    - error:
        file: notGeneratedResource.yaml
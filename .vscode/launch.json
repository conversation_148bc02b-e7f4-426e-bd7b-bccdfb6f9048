{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Kyverno",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/kyverno",
            "args": [
                "--kubeconfig=${userHome}/.kube/config",
                "--serverIP=<SERVER-IP>:9443",
                "--backgroundServiceAccountName=system:serviceaccount:kyverno:kyverno-background-controller",
                "--reportsServiceAccountName=system:serviceaccount:kyverno:kyverno-reports-controller",
                "-v=2",
                "--caSecretName=kyverno-svc.kyverno.svc.kyverno-tls-ca",
                "--tlsSecretName=kyverno-svc.kyverno.svc.kyverno-tls-pair",
            ],
            "env": {
                "KYVERNO_NAMESPACE": "kyverno",
                "KYVERNO_SERVICEACCOUNT_NAME": "kyverno-admission-controller",
                "KYVERNO_DEPLOYMENT": "dummy",
                "KYVERNO_POD_NAME": "dummy",
                "INIT_CONFIG": "kyverno",
                "METRICS_CONFIG": "kyverno-metrics",
            }
        },
        {
            "name": "Launch Reports Controller",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/reports-controller",
            "args": [
                "--kubeconfig=${userHome}/.kube/config",
            ],
            "env": {
                "KYVERNO_NAMESPACE": "kyverno",
                "KYVERNO_SERVICEACCOUNT_NAME": "kyverno-reports-controller",
                "KYVERNO_DEPLOYMENT": "dummy",
                "KYVERNO_POD_NAME": "dummy",
                "INIT_CONFIG": "kyverno",
                "METRICS_CONFIG": "kyverno-metrics",
            }
        },
        {
            "name": "Launch Background Controller",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/background-controller",
            "args": [
                "--kubeconfig=${userHome}/.kube/config",
                "--controllerRuntimeMetricsAddress=0"
            ],
            "env": {
                "KYVERNO_NAMESPACE": "kyverno",
                "KYVERNO_SERVICEACCOUNT_NAME": "kyverno-background-controller",
                "KYVERNO_DEPLOYMENT": "dummy",
                "KYVERNO_POD_NAME": "dummy",
                "INIT_CONFIG": "kyverno",
                "METRICS_CONFIG": "kyverno-metrics",
            }
        },
        {
            "name": "Launch Cleanup Controller",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/cleanup-controller",
            "args": [
                "--kubeconfig=${userHome}/.kube/config",
                "--serverIP=<SERVER-IP>:9443",
                "--caSecretName=kyverno-cleanup-controller.kyverno.svc.kyverno-tls-ca",
                "--tlsSecretName=kyverno-cleanup-controller.kyverno.svc.kyverno-tls-pair",
            ],
            "env": {
                "KYVERNO_NAMESPACE": "kyverno",
                "KYVERNO_SERVICEACCOUNT_NAME": "kyverno-cleanup-controller",
                "KYVERNO_SVC": "kyverno-cleanup-controller",
                "KYVERNO_DEPLOYMENT": "dummy",
                "KYVERNO_POD_NAME": "dummy",
                "INIT_CONFIG": "kyverno",
                "METRICS_CONFIG": "kyverno-metrics",
            }
        },
        {
            "name": "Launch CLI",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/cli/kubectl-kyverno",
            "cwd": "${workspaceFolder}",
            "args": [
                "test",
                "test/cli"
            ],
        }
    ]
}
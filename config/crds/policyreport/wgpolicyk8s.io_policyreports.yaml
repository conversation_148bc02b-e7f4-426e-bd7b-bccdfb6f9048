---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: (devel)
  name: policyreports.wgpolicyk8s.io
spec:
  group: wgpolicyk8s.io
  names:
    kind: PolicyReport
    listKind: PolicyReportList
    plural: policyreports
    shortNames:
    - polr
    singular: policyreport
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .scope.kind
      name: Kind
      type: string
    - jsonPath: .scope.name
      name: Name
      type: string
    - jsonPath: .summary.pass
      name: Pass
      type: integer
    - jsonPath: .summary.fail
      name: Fail
      type: integer
    - jsonPath: .summary.warn
      name: Warn
      type: integer
    - jsonPath: .summary.error
      name: Error
      type: integer
    - jsonPath: .summary.skip
      name: Skip
      type: integer
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha2
    schema:
      openAPIV3Schema:
        description: PolicyReport is the Schema for the policyreports API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          results:
            description: PolicyReportResult provides result details
            items:
              description: PolicyReportResult provides the result for an individual
                policy
              properties:
                category:
                  description: Category indicates policy category
                  type: string
                message:
                  description: Description is a short user friendly message for the
                    policy rule
                  type: string
                policy:
                  description: Policy is the name or identifier of the policy
                  type: string
                properties:
                  additionalProperties:
                    type: string
                  description: Properties provides additional information for the
                    policy rule
                  type: object
                resourceSelector:
                  description: |-
                    SubjectSelector is an optional label selector for checked Kubernetes resources.
                    For example, a policy result may apply to all pods that match a label.
                    Either a Subject or a SubjectSelector can be specified.
                    If neither are provided, the result is assumed to be for the policy report scope.
                  properties:
                    matchExpressions:
                      description: matchExpressions is a list of label selector requirements.
                        The requirements are ANDed.
                      items:
                        description: |-
                          A label selector requirement is a selector that contains values, a key, and an operator that
                          relates the key and values.
                        properties:
                          key:
                            description: key is the label key that the selector applies
                              to.
                            type: string
                          operator:
                            description: |-
                              operator represents a key's relationship to a set of values.
                              Valid operators are In, NotIn, Exists and DoesNotExist.
                            type: string
                          values:
                            description: |-
                              values is an array of string values. If the operator is In or NotIn,
                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                              the values array must be empty. This array is replaced during a strategic
                              merge patch.
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        required:
                        - key
                        - operator
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    matchLabels:
                      additionalProperties:
                        type: string
                      description: |-
                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                      type: object
                  type: object
                  x-kubernetes-map-type: atomic
                resources:
                  description: Subjects is an optional reference to the checked Kubernetes
                    resources
                  items:
                    description: ObjectReference contains enough information to let
                      you inspect or modify the referred object.
                    properties:
                      apiVersion:
                        description: API version of the referent.
                        type: string
                      fieldPath:
                        description: |-
                          If referring to a piece of an object instead of an entire object, this string
                          should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                          For example, if the object reference is to a container within a pod, this would take on a value like:
                          "spec.containers{name}" (where "name" refers to the name of the container that triggered
                          the event) or if no container name is specified "spec.containers[2]" (container with
                          index 2 in this pod). This syntax is chosen only to have some well-defined way of
                          referencing a part of an object.
                        type: string
                      kind:
                        description: |-
                          Kind of the referent.
                          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                        type: string
                      name:
                        description: |-
                          Name of the referent.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                        type: string
                      namespace:
                        description: |-
                          Namespace of the referent.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                        type: string
                      resourceVersion:
                        description: |-
                          Specific resourceVersion to which this reference is made, if any.
                          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                        type: string
                      uid:
                        description: |-
                          UID of the referent.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                        type: string
                    type: object
                    x-kubernetes-map-type: atomic
                  type: array
                result:
                  description: Result indicates the outcome of the policy rule execution
                  enum:
                  - pass
                  - fail
                  - warn
                  - error
                  - skip
                  type: string
                rule:
                  description: Rule is the name or identifier of the rule within the
                    policy
                  type: string
                scored:
                  description: Scored indicates if this result is scored
                  type: boolean
                severity:
                  description: Severity indicates policy check result criticality
                  enum:
                  - critical
                  - high
                  - low
                  - medium
                  - info
                  type: string
                source:
                  description: Source is an identifier for the policy engine that
                    manages this report
                  type: string
                timestamp:
                  description: Timestamp indicates the time the result was found
                  properties:
                    nanos:
                      description: |-
                        Non-negative fractions of a second at nanosecond resolution. Negative
                        second values with fractions must still have non-negative nanos values
                        that count forward in time. Must be from 0 to 999,999,999
                        inclusive. This field may be limited in precision depending on context.
                      format: int32
                      type: integer
                    seconds:
                      description: |-
                        Represents seconds of UTC time since Unix epoch
                        1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
                        9999-12-31T23:59:59Z inclusive.
                      format: int64
                      type: integer
                  required:
                  - nanos
                  - seconds
                  type: object
              required:
              - policy
              type: object
            type: array
          scope:
            description: Scope is an optional reference to the report scope (e.g.
              a Deployment, Namespace, or Node)
            properties:
              apiVersion:
                description: API version of the referent.
                type: string
              fieldPath:
                description: |-
                  If referring to a piece of an object instead of an entire object, this string
                  should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                  For example, if the object reference is to a container within a pod, this would take on a value like:
                  "spec.containers{name}" (where "name" refers to the name of the container that triggered
                  the event) or if no container name is specified "spec.containers[2]" (container with
                  index 2 in this pod). This syntax is chosen only to have some well-defined way of
                  referencing a part of an object.
                type: string
              kind:
                description: |-
                  Kind of the referent.
                  More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                type: string
              name:
                description: |-
                  Name of the referent.
                  More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                type: string
              namespace:
                description: |-
                  Namespace of the referent.
                  More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                type: string
              resourceVersion:
                description: |-
                  Specific resourceVersion to which this reference is made, if any.
                  More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                type: string
              uid:
                description: |-
                  UID of the referent.
                  More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                type: string
            type: object
            x-kubernetes-map-type: atomic
          scopeSelector:
            description: |-
              ScopeSelector is an optional selector for multiple scopes (e.g. Pods).
              Either one of, or none of, but not both of, Scope or ScopeSelector should be specified.
            properties:
              matchExpressions:
                description: matchExpressions is a list of label selector requirements.
                  The requirements are ANDed.
                items:
                  description: |-
                    A label selector requirement is a selector that contains values, a key, and an operator that
                    relates the key and values.
                  properties:
                    key:
                      description: key is the label key that the selector applies
                        to.
                      type: string
                    operator:
                      description: |-
                        operator represents a key's relationship to a set of values.
                        Valid operators are In, NotIn, Exists and DoesNotExist.
                      type: string
                    values:
                      description: |-
                        values is an array of string values. If the operator is In or NotIn,
                        the values array must be non-empty. If the operator is Exists or DoesNotExist,
                        the values array must be empty. This array is replaced during a strategic
                        merge patch.
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                  required:
                  - key
                  - operator
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              matchLabels:
                additionalProperties:
                  type: string
                description: |-
                  matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                  map is equivalent to an element of matchExpressions, whose key field is "key", the
                  operator is "In", and the values array contains only "value". The requirements are ANDed.
                type: object
            type: object
            x-kubernetes-map-type: atomic
          summary:
            description: PolicyReportSummary provides a summary of results
            properties:
              error:
                description: Error provides the count of policies that could not be
                  evaluated
                type: integer
              fail:
                description: Fail provides the count of policies whose requirements
                  were not met
                type: integer
              pass:
                description: Pass provides the count of policies whose requirements
                  were met
                type: integer
              skip:
                description: Skip indicates the count of policies that were not selected
                  for evaluation
                type: integer
              warn:
                description: Warn provides the count of non-scored policies whose
                  requirements were not met
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources: {}

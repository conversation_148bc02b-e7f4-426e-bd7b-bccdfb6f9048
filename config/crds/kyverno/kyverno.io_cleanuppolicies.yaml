---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: (devel)
  name: cleanuppolicies.kyverno.io
spec:
  group: kyverno.io
  names:
    categories:
    - kyverno
    kind: CleanupPolicy
    listKind: CleanupPolicyList
    plural: cleanuppolicies
    shortNames:
    - cleanpol
    singular: cleanuppolicy
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.schedule
      name: Schedule
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v2
    schema:
      openAPIV3Schema:
        description: CleanupPolicy defines a rule for resource cleanup.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec declares policy behaviors.
            properties:
              conditions:
                description: Conditions defines the conditions used to select the
                  resources which will be cleaned up.
                properties:
                  all:
                    description: |-
                      AllConditions enable variable-based conditional rule execution. This is useful for
                      finer control of when an rule is applied. A condition can reference object data
                      using JMESPath notation.
                      Here, all of the conditions need to pass.
                    items:
                      properties:
                        key:
                          description: Key is the context entry (using JMESPath) for
                            conditional rule evaluation.
                          x-kubernetes-preserve-unknown-fields: true
                        message:
                          description: Message is an optional display message
                          type: string
                        operator:
                          description: |-
                            Operator is the conditional operation to perform. Valid operators are:
                            Equals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,
                            GreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,
                            DurationLessThanOrEquals, DurationLessThan
                          enum:
                          - Equals
                          - NotEquals
                          - AnyIn
                          - AllIn
                          - AnyNotIn
                          - AllNotIn
                          - GreaterThanOrEquals
                          - GreaterThan
                          - LessThanOrEquals
                          - LessThan
                          - DurationGreaterThanOrEquals
                          - DurationGreaterThan
                          - DurationLessThanOrEquals
                          - DurationLessThan
                          type: string
                        value:
                          description: |-
                            Value is the conditional value, or set of values. The values can be fixed set
                            or can be variables declared using JMESPath.
                          x-kubernetes-preserve-unknown-fields: true
                      type: object
                    type: array
                  any:
                    description: |-
                      AnyConditions enable variable-based conditional rule execution. This is useful for
                      finer control of when an rule is applied. A condition can reference object data
                      using JMESPath notation.
                      Here, at least one of the conditions need to pass.
                    items:
                      properties:
                        key:
                          description: Key is the context entry (using JMESPath) for
                            conditional rule evaluation.
                          x-kubernetes-preserve-unknown-fields: true
                        message:
                          description: Message is an optional display message
                          type: string
                        operator:
                          description: |-
                            Operator is the conditional operation to perform. Valid operators are:
                            Equals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,
                            GreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,
                            DurationLessThanOrEquals, DurationLessThan
                          enum:
                          - Equals
                          - NotEquals
                          - AnyIn
                          - AllIn
                          - AnyNotIn
                          - AllNotIn
                          - GreaterThanOrEquals
                          - GreaterThan
                          - LessThanOrEquals
                          - LessThan
                          - DurationGreaterThanOrEquals
                          - DurationGreaterThan
                          - DurationLessThanOrEquals
                          - DurationLessThan
                          type: string
                        value:
                          description: |-
                            Value is the conditional value, or set of values. The values can be fixed set
                            or can be variables declared using JMESPath.
                          x-kubernetes-preserve-unknown-fields: true
                      type: object
                    type: array
                type: object
              context:
                description: Context defines variables and data sources that can be
                  used during rule execution.
                items:
                  description: |-
                    ContextEntry adds variables and data sources to a rule Context. Either a
                    ConfigMap reference or a APILookup must be provided.
                  oneOf:
                  - required:
                    - configMap
                  - required:
                    - apiCall
                  - required:
                    - imageRegistry
                  - required:
                    - variable
                  - required:
                    - globalReference
                  properties:
                    apiCall:
                      description: |-
                        APICall is an HTTP request to the Kubernetes API server, or other JSON web service.
                        The data returned is stored in the context with the name for the context entry.
                      properties:
                        data:
                          description: |-
                            The data object specifies the POST data sent to the server.
                            Only applicable when the method field is set to POST.
                          items:
                            description: RequestData contains the HTTP POST data
                            properties:
                              key:
                                description: Key is a unique identifier for the data
                                  value
                                type: string
                              value:
                                description: Value is the data value
                                x-kubernetes-preserve-unknown-fields: true
                            required:
                            - key
                            - value
                            type: object
                          type: array
                        default:
                          description: |-
                            Default is an optional arbitrary JSON object that the context
                            value is set to, if the apiCall returns error.
                          x-kubernetes-preserve-unknown-fields: true
                        jmesPath:
                          description: |-
                            JMESPath is an optional JSON Match Expression that can be used to
                            transform the JSON response returned from the server. For example
                            a JMESPath of "items | length(@)" applied to the API server response
                            for the URLPath "/apis/apps/v1/deployments" will return the total count
                            of deployments across all namespaces.
                          type: string
                        method:
                          default: GET
                          description: Method is the HTTP request type (GET or POST).
                            Defaults to GET.
                          enum:
                          - GET
                          - POST
                          type: string
                        service:
                          description: |-
                            Service is an API call to a JSON web service.
                            This is used for non-Kubernetes API server calls.
                            It's mutually exclusive with the URLPath field.
                          properties:
                            caBundle:
                              description: |-
                                CABundle is a PEM encoded CA bundle which will be used to validate
                                the server certificate.
                              type: string
                            headers:
                              description: Headers is a list of optional HTTP headers
                                to be included in the request.
                              items:
                                properties:
                                  key:
                                    description: Key is the header key
                                    type: string
                                  value:
                                    description: Value is the header value
                                    type: string
                                required:
                                - key
                                - value
                                type: object
                              type: array
                            url:
                              description: |-
                                URL is the JSON web service URL. A typical form is
                                `https://{service}.{namespace}:{port}/{path}`.
                              type: string
                          required:
                          - url
                          type: object
                        urlPath:
                          description: |-
                            URLPath is the URL path to be used in the HTTP GET or POST request to the
                            Kubernetes API server (e.g. "/api/v1/namespaces" or  "/apis/apps/v1/deployments").
                            The format required is the same format used by the `kubectl get --raw` command.
                            See https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls
                            for details.
                            It's mutually exclusive with the Service field.
                          type: string
                      type: object
                    configMap:
                      description: ConfigMap is the ConfigMap reference.
                      properties:
                        name:
                          description: Name is the ConfigMap name.
                          type: string
                        namespace:
                          description: Namespace is the ConfigMap namespace.
                          type: string
                      required:
                      - name
                      type: object
                    globalReference:
                      description: GlobalContextEntryReference is a reference to a
                        cached global context entry.
                      properties:
                        jmesPath:
                          description: |-
                            JMESPath is an optional JSON Match Expression that can be used to
                            transform the JSON response returned from the server. For example
                            a JMESPath of "items | length(@)" applied to the API server response
                            for the URLPath "/apis/apps/v1/deployments" will return the total count
                            of deployments across all namespaces.
                          type: string
                        name:
                          description: Name of the global context entry
                          type: string
                      required:
                      - name
                      type: object
                    imageRegistry:
                      description: |-
                        ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image
                        details.
                      properties:
                        imageRegistryCredentials:
                          description: ImageRegistryCredentials provides credentials
                            that will be used for authentication with registry
                          properties:
                            allowInsecureRegistry:
                              description: AllowInsecureRegistry allows insecure access
                                to a registry.
                              type: boolean
                            providers:
                              description: |-
                                Providers specifies a list of OCI Registry names, whose authentication providers are provided.
                                It can be of one of these values: default,google,azure,amazon,github.
                              items:
                                description: ImageRegistryCredentialsProvidersType
                                  provides the list of credential providers required.
                                enum:
                                - default
                                - amazon
                                - azure
                                - google
                                - github
                                type: string
                              type: array
                            secrets:
                              description: |-
                                Secrets specifies a list of secrets that are provided for credentials.
                                Secrets must live in the Kyverno namespace.
                              items:
                                type: string
                              type: array
                          type: object
                        jmesPath:
                          description: |-
                            JMESPath is an optional JSON Match Expression that can be used to
                            transform the ImageData struct returned as a result of processing
                            the image reference.
                          type: string
                        reference:
                          description: |-
                            Reference is image reference to a container image in the registry.
                            Example: ghcr.io/kyverno/kyverno:latest
                          type: string
                      required:
                      - reference
                      type: object
                    name:
                      description: Name is the variable name.
                      type: string
                    variable:
                      description: Variable defines an arbitrary JMESPath context
                        variable that can be defined inline.
                      properties:
                        default:
                          description: |-
                            Default is an optional arbitrary JSON object that the variable may take if the JMESPath
                            expression evaluates to nil
                          x-kubernetes-preserve-unknown-fields: true
                        jmesPath:
                          description: |-
                            JMESPath is an optional JMESPath Expression that can be used to
                            transform the variable.
                          type: string
                        value:
                          description: Value is any arbitrary JSON object representable
                            in YAML or JSON form.
                          x-kubernetes-preserve-unknown-fields: true
                      type: object
                  required:
                  - name
                  type: object
                type: array
              deletionPropagationPolicy:
                description: DeletionPropagationPolicy defines how resources will
                  be deleted (Foreground, Background, Orphan).
                enum:
                - Foreground
                - Background
                - Orphan
                type: string
              exclude:
                description: |-
                  ExcludeResources defines when cleanuppolicy should not be applied. The exclude
                  criteria can include resource information (e.g. kind, name, namespace, labels)
                  and admission review request information like the name or role.
                not:
                  required:
                  - any
                  - all
                properties:
                  all:
                    description: All allows specifying resources which will be ANDed
                    items:
                      description: ResourceFilter allow users to "AND" or "OR" between
                        resources
                      properties:
                        clusterRoles:
                          description: ClusterRoles is the list of cluster-wide role
                            names for the user.
                          items:
                            type: string
                          type: array
                        resources:
                          description: ResourceDescription contains information about
                            the resource being created or modified.
                          not:
                            required:
                            - name
                            - names
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              description: |-
                                Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
                                and values support the wildcard characters "*" (matches zero or many characters) and
                                "?" (matches at least one character).
                              type: object
                            kinds:
                              description: Kinds is a list of resource kinds.
                              items:
                                type: string
                              type: array
                            name:
                              description: |-
                                Name is the name of the resource. The name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                                NOTE: "Name" is being deprecated in favor of "Names".
                              type: string
                            names:
                              description: |-
                                Names are the names of the resources. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            namespaceSelector:
                              description: |-
                                NamespaceSelector is a label selector for the resource namespace. Label keys and values
                                in `matchLabels` support the wildcard characters `*` (matches zero or many characters)
                                and `?` (matches one character).Wildcards allows writing label selectors like
                                ["storage.k8s.io/*": "*"]. Note that using ["*" : "*"] matches any key and value but
                                does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                Namespaces is a list of namespaces names. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            operations:
                              description: Operations can contain values ["CREATE,
                                "UPDATE", "CONNECT", "DELETE"], which are used to
                                match a specific action.
                              items:
                                description: AdmissionOperation can have one of the
                                  values CREATE, UPDATE, CONNECT, DELETE, which are
                                  used to match a specific action.
                                enum:
                                - CREATE
                                - CONNECT
                                - UPDATE
                                - DELETE
                                type: string
                              type: array
                            selector:
                              description: |-
                                Selector is a label selector. Label keys and values in `matchLabels` support the wildcard
                                characters `*` (matches zero or many characters) and `?` (matches one character).
                                Wildcards allows writing label selectors like ["storage.k8s.io/*": "*"]. Note that
                                using ["*" : "*"] matches any key and value but does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        roles:
                          description: Roles is the list of namespaced role names
                            for the user.
                          items:
                            type: string
                          type: array
                        subjects:
                          description: Subjects is the list of subject names like
                            users, user groups, and service accounts.
                          items:
                            description: |-
                              Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,
                              or a value for non-objects such as user and group names.
                            properties:
                              apiGroup:
                                description: |-
                                  APIGroup holds the API group of the referenced subject.
                                  Defaults to "" for ServiceAccount subjects.
                                  Defaults to "rbac.authorization.k8s.io" for User and Group subjects.
                                type: string
                              kind:
                                description: |-
                                  Kind of object being referenced. Values defined by this API group are "User", "Group", and "ServiceAccount".
                                  If the Authorizer does not recognized the kind value, the Authorizer should report an error.
                                type: string
                              name:
                                description: Name of the object being referenced.
                                type: string
                              namespace:
                                description: |-
                                  Namespace of the referenced object.  If the object kind is non-namespace, such as "User" or "Group", and this value is not empty
                                  the Authorizer should report an error.
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          type: array
                      type: object
                    type: array
                  any:
                    description: Any allows specifying resources which will be ORed
                    items:
                      description: ResourceFilter allow users to "AND" or "OR" between
                        resources
                      properties:
                        clusterRoles:
                          description: ClusterRoles is the list of cluster-wide role
                            names for the user.
                          items:
                            type: string
                          type: array
                        resources:
                          description: ResourceDescription contains information about
                            the resource being created or modified.
                          not:
                            required:
                            - name
                            - names
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              description: |-
                                Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
                                and values support the wildcard characters "*" (matches zero or many characters) and
                                "?" (matches at least one character).
                              type: object
                            kinds:
                              description: Kinds is a list of resource kinds.
                              items:
                                type: string
                              type: array
                            name:
                              description: |-
                                Name is the name of the resource. The name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                                NOTE: "Name" is being deprecated in favor of "Names".
                              type: string
                            names:
                              description: |-
                                Names are the names of the resources. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            namespaceSelector:
                              description: |-
                                NamespaceSelector is a label selector for the resource namespace. Label keys and values
                                in `matchLabels` support the wildcard characters `*` (matches zero or many characters)
                                and `?` (matches one character).Wildcards allows writing label selectors like
                                ["storage.k8s.io/*": "*"]. Note that using ["*" : "*"] matches any key and value but
                                does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                Namespaces is a list of namespaces names. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            operations:
                              description: Operations can contain values ["CREATE,
                                "UPDATE", "CONNECT", "DELETE"], which are used to
                                match a specific action.
                              items:
                                description: AdmissionOperation can have one of the
                                  values CREATE, UPDATE, CONNECT, DELETE, which are
                                  used to match a specific action.
                                enum:
                                - CREATE
                                - CONNECT
                                - UPDATE
                                - DELETE
                                type: string
                              type: array
                            selector:
                              description: |-
                                Selector is a label selector. Label keys and values in `matchLabels` support the wildcard
                                characters `*` (matches zero or many characters) and `?` (matches one character).
                                Wildcards allows writing label selectors like ["storage.k8s.io/*": "*"]. Note that
                                using ["*" : "*"] matches any key and value but does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        roles:
                          description: Roles is the list of namespaced role names
                            for the user.
                          items:
                            type: string
                          type: array
                        subjects:
                          description: Subjects is the list of subject names like
                            users, user groups, and service accounts.
                          items:
                            description: |-
                              Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,
                              or a value for non-objects such as user and group names.
                            properties:
                              apiGroup:
                                description: |-
                                  APIGroup holds the API group of the referenced subject.
                                  Defaults to "" for ServiceAccount subjects.
                                  Defaults to "rbac.authorization.k8s.io" for User and Group subjects.
                                type: string
                              kind:
                                description: |-
                                  Kind of object being referenced. Values defined by this API group are "User", "Group", and "ServiceAccount".
                                  If the Authorizer does not recognized the kind value, the Authorizer should report an error.
                                type: string
                              name:
                                description: Name of the object being referenced.
                                type: string
                              namespace:
                                description: |-
                                  Namespace of the referenced object.  If the object kind is non-namespace, such as "User" or "Group", and this value is not empty
                                  the Authorizer should report an error.
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          type: array
                      type: object
                    type: array
                type: object
              match:
                description: |-
                  MatchResources defines when cleanuppolicy should be applied. The match
                  criteria can include resource information (e.g. kind, name, namespace, labels)
                  and admission review request information like the user name or role.
                  At least one kind is required.
                not:
                  required:
                  - any
                  - all
                properties:
                  all:
                    description: All allows specifying resources which will be ANDed
                    items:
                      description: ResourceFilter allow users to "AND" or "OR" between
                        resources
                      properties:
                        clusterRoles:
                          description: ClusterRoles is the list of cluster-wide role
                            names for the user.
                          items:
                            type: string
                          type: array
                        resources:
                          description: ResourceDescription contains information about
                            the resource being created or modified.
                          not:
                            required:
                            - name
                            - names
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              description: |-
                                Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
                                and values support the wildcard characters "*" (matches zero or many characters) and
                                "?" (matches at least one character).
                              type: object
                            kinds:
                              description: Kinds is a list of resource kinds.
                              items:
                                type: string
                              type: array
                            name:
                              description: |-
                                Name is the name of the resource. The name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                                NOTE: "Name" is being deprecated in favor of "Names".
                              type: string
                            names:
                              description: |-
                                Names are the names of the resources. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            namespaceSelector:
                              description: |-
                                NamespaceSelector is a label selector for the resource namespace. Label keys and values
                                in `matchLabels` support the wildcard characters `*` (matches zero or many characters)
                                and `?` (matches one character).Wildcards allows writing label selectors like
                                ["storage.k8s.io/*": "*"]. Note that using ["*" : "*"] matches any key and value but
                                does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                Namespaces is a list of namespaces names. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            operations:
                              description: Operations can contain values ["CREATE,
                                "UPDATE", "CONNECT", "DELETE"], which are used to
                                match a specific action.
                              items:
                                description: AdmissionOperation can have one of the
                                  values CREATE, UPDATE, CONNECT, DELETE, which are
                                  used to match a specific action.
                                enum:
                                - CREATE
                                - CONNECT
                                - UPDATE
                                - DELETE
                                type: string
                              type: array
                            selector:
                              description: |-
                                Selector is a label selector. Label keys and values in `matchLabels` support the wildcard
                                characters `*` (matches zero or many characters) and `?` (matches one character).
                                Wildcards allows writing label selectors like ["storage.k8s.io/*": "*"]. Note that
                                using ["*" : "*"] matches any key and value but does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        roles:
                          description: Roles is the list of namespaced role names
                            for the user.
                          items:
                            type: string
                          type: array
                        subjects:
                          description: Subjects is the list of subject names like
                            users, user groups, and service accounts.
                          items:
                            description: |-
                              Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,
                              or a value for non-objects such as user and group names.
                            properties:
                              apiGroup:
                                description: |-
                                  APIGroup holds the API group of the referenced subject.
                                  Defaults to "" for ServiceAccount subjects.
                                  Defaults to "rbac.authorization.k8s.io" for User and Group subjects.
                                type: string
                              kind:
                                description: |-
                                  Kind of object being referenced. Values defined by this API group are "User", "Group", and "ServiceAccount".
                                  If the Authorizer does not recognized the kind value, the Authorizer should report an error.
                                type: string
                              name:
                                description: Name of the object being referenced.
                                type: string
                              namespace:
                                description: |-
                                  Namespace of the referenced object.  If the object kind is non-namespace, such as "User" or "Group", and this value is not empty
                                  the Authorizer should report an error.
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          type: array
                      type: object
                    type: array
                  any:
                    description: Any allows specifying resources which will be ORed
                    items:
                      description: ResourceFilter allow users to "AND" or "OR" between
                        resources
                      properties:
                        clusterRoles:
                          description: ClusterRoles is the list of cluster-wide role
                            names for the user.
                          items:
                            type: string
                          type: array
                        resources:
                          description: ResourceDescription contains information about
                            the resource being created or modified.
                          not:
                            required:
                            - name
                            - names
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              description: |-
                                Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
                                and values support the wildcard characters "*" (matches zero or many characters) and
                                "?" (matches at least one character).
                              type: object
                            kinds:
                              description: Kinds is a list of resource kinds.
                              items:
                                type: string
                              type: array
                            name:
                              description: |-
                                Name is the name of the resource. The name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                                NOTE: "Name" is being deprecated in favor of "Names".
                              type: string
                            names:
                              description: |-
                                Names are the names of the resources. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            namespaceSelector:
                              description: |-
                                NamespaceSelector is a label selector for the resource namespace. Label keys and values
                                in `matchLabels` support the wildcard characters `*` (matches zero or many characters)
                                and `?` (matches one character).Wildcards allows writing label selectors like
                                ["storage.k8s.io/*": "*"]. Note that using ["*" : "*"] matches any key and value but
                                does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                Namespaces is a list of namespaces names. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            operations:
                              description: Operations can contain values ["CREATE,
                                "UPDATE", "CONNECT", "DELETE"], which are used to
                                match a specific action.
                              items:
                                description: AdmissionOperation can have one of the
                                  values CREATE, UPDATE, CONNECT, DELETE, which are
                                  used to match a specific action.
                                enum:
                                - CREATE
                                - CONNECT
                                - UPDATE
                                - DELETE
                                type: string
                              type: array
                            selector:
                              description: |-
                                Selector is a label selector. Label keys and values in `matchLabels` support the wildcard
                                characters `*` (matches zero or many characters) and `?` (matches one character).
                                Wildcards allows writing label selectors like ["storage.k8s.io/*": "*"]. Note that
                                using ["*" : "*"] matches any key and value but does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        roles:
                          description: Roles is the list of namespaced role names
                            for the user.
                          items:
                            type: string
                          type: array
                        subjects:
                          description: Subjects is the list of subject names like
                            users, user groups, and service accounts.
                          items:
                            description: |-
                              Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,
                              or a value for non-objects such as user and group names.
                            properties:
                              apiGroup:
                                description: |-
                                  APIGroup holds the API group of the referenced subject.
                                  Defaults to "" for ServiceAccount subjects.
                                  Defaults to "rbac.authorization.k8s.io" for User and Group subjects.
                                type: string
                              kind:
                                description: |-
                                  Kind of object being referenced. Values defined by this API group are "User", "Group", and "ServiceAccount".
                                  If the Authorizer does not recognized the kind value, the Authorizer should report an error.
                                type: string
                              name:
                                description: Name of the object being referenced.
                                type: string
                              namespace:
                                description: |-
                                  Namespace of the referenced object.  If the object kind is non-namespace, such as "User" or "Group", and this value is not empty
                                  the Authorizer should report an error.
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          type: array
                      type: object
                    type: array
                type: object
              schedule:
                description: The schedule in Cron format
                type: string
            required:
            - match
            - schedule
            type: object
          status:
            description: Status contains policy runtime data.
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              lastExecutionTime:
                format: date-time
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
  - additionalPrinterColumns:
    - jsonPath: .spec.schedule
      name: Schedule
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    deprecated: true
    name: v2beta1
    schema:
      openAPIV3Schema:
        description: CleanupPolicy defines a rule for resource cleanup.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec declares policy behaviors.
            properties:
              conditions:
                description: Conditions defines the conditions used to select the
                  resources which will be cleaned up.
                properties:
                  all:
                    description: |-
                      AllConditions enable variable-based conditional rule execution. This is useful for
                      finer control of when an rule is applied. A condition can reference object data
                      using JMESPath notation.
                      Here, all of the conditions need to pass.
                    items:
                      properties:
                        key:
                          description: Key is the context entry (using JMESPath) for
                            conditional rule evaluation.
                          x-kubernetes-preserve-unknown-fields: true
                        message:
                          description: Message is an optional display message
                          type: string
                        operator:
                          description: |-
                            Operator is the conditional operation to perform. Valid operators are:
                            Equals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,
                            GreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,
                            DurationLessThanOrEquals, DurationLessThan
                          enum:
                          - Equals
                          - NotEquals
                          - AnyIn
                          - AllIn
                          - AnyNotIn
                          - AllNotIn
                          - GreaterThanOrEquals
                          - GreaterThan
                          - LessThanOrEquals
                          - LessThan
                          - DurationGreaterThanOrEquals
                          - DurationGreaterThan
                          - DurationLessThanOrEquals
                          - DurationLessThan
                          type: string
                        value:
                          description: |-
                            Value is the conditional value, or set of values. The values can be fixed set
                            or can be variables declared using JMESPath.
                          x-kubernetes-preserve-unknown-fields: true
                      type: object
                    type: array
                  any:
                    description: |-
                      AnyConditions enable variable-based conditional rule execution. This is useful for
                      finer control of when an rule is applied. A condition can reference object data
                      using JMESPath notation.
                      Here, at least one of the conditions need to pass.
                    items:
                      properties:
                        key:
                          description: Key is the context entry (using JMESPath) for
                            conditional rule evaluation.
                          x-kubernetes-preserve-unknown-fields: true
                        message:
                          description: Message is an optional display message
                          type: string
                        operator:
                          description: |-
                            Operator is the conditional operation to perform. Valid operators are:
                            Equals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,
                            GreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,
                            DurationLessThanOrEquals, DurationLessThan
                          enum:
                          - Equals
                          - NotEquals
                          - AnyIn
                          - AllIn
                          - AnyNotIn
                          - AllNotIn
                          - GreaterThanOrEquals
                          - GreaterThan
                          - LessThanOrEquals
                          - LessThan
                          - DurationGreaterThanOrEquals
                          - DurationGreaterThan
                          - DurationLessThanOrEquals
                          - DurationLessThan
                          type: string
                        value:
                          description: |-
                            Value is the conditional value, or set of values. The values can be fixed set
                            or can be variables declared using JMESPath.
                          x-kubernetes-preserve-unknown-fields: true
                      type: object
                    type: array
                type: object
              context:
                description: Context defines variables and data sources that can be
                  used during rule execution.
                items:
                  description: |-
                    ContextEntry adds variables and data sources to a rule Context. Either a
                    ConfigMap reference or a APILookup must be provided.
                  oneOf:
                  - required:
                    - configMap
                  - required:
                    - apiCall
                  - required:
                    - imageRegistry
                  - required:
                    - variable
                  - required:
                    - globalReference
                  properties:
                    apiCall:
                      description: |-
                        APICall is an HTTP request to the Kubernetes API server, or other JSON web service.
                        The data returned is stored in the context with the name for the context entry.
                      properties:
                        data:
                          description: |-
                            The data object specifies the POST data sent to the server.
                            Only applicable when the method field is set to POST.
                          items:
                            description: RequestData contains the HTTP POST data
                            properties:
                              key:
                                description: Key is a unique identifier for the data
                                  value
                                type: string
                              value:
                                description: Value is the data value
                                x-kubernetes-preserve-unknown-fields: true
                            required:
                            - key
                            - value
                            type: object
                          type: array
                        default:
                          description: |-
                            Default is an optional arbitrary JSON object that the context
                            value is set to, if the apiCall returns error.
                          x-kubernetes-preserve-unknown-fields: true
                        jmesPath:
                          description: |-
                            JMESPath is an optional JSON Match Expression that can be used to
                            transform the JSON response returned from the server. For example
                            a JMESPath of "items | length(@)" applied to the API server response
                            for the URLPath "/apis/apps/v1/deployments" will return the total count
                            of deployments across all namespaces.
                          type: string
                        method:
                          default: GET
                          description: Method is the HTTP request type (GET or POST).
                            Defaults to GET.
                          enum:
                          - GET
                          - POST
                          type: string
                        service:
                          description: |-
                            Service is an API call to a JSON web service.
                            This is used for non-Kubernetes API server calls.
                            It's mutually exclusive with the URLPath field.
                          properties:
                            caBundle:
                              description: |-
                                CABundle is a PEM encoded CA bundle which will be used to validate
                                the server certificate.
                              type: string
                            headers:
                              description: Headers is a list of optional HTTP headers
                                to be included in the request.
                              items:
                                properties:
                                  key:
                                    description: Key is the header key
                                    type: string
                                  value:
                                    description: Value is the header value
                                    type: string
                                required:
                                - key
                                - value
                                type: object
                              type: array
                            url:
                              description: |-
                                URL is the JSON web service URL. A typical form is
                                `https://{service}.{namespace}:{port}/{path}`.
                              type: string
                          required:
                          - url
                          type: object
                        urlPath:
                          description: |-
                            URLPath is the URL path to be used in the HTTP GET or POST request to the
                            Kubernetes API server (e.g. "/api/v1/namespaces" or  "/apis/apps/v1/deployments").
                            The format required is the same format used by the `kubectl get --raw` command.
                            See https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls
                            for details.
                            It's mutually exclusive with the Service field.
                          type: string
                      type: object
                    configMap:
                      description: ConfigMap is the ConfigMap reference.
                      properties:
                        name:
                          description: Name is the ConfigMap name.
                          type: string
                        namespace:
                          description: Namespace is the ConfigMap namespace.
                          type: string
                      required:
                      - name
                      type: object
                    globalReference:
                      description: GlobalContextEntryReference is a reference to a
                        cached global context entry.
                      properties:
                        jmesPath:
                          description: |-
                            JMESPath is an optional JSON Match Expression that can be used to
                            transform the JSON response returned from the server. For example
                            a JMESPath of "items | length(@)" applied to the API server response
                            for the URLPath "/apis/apps/v1/deployments" will return the total count
                            of deployments across all namespaces.
                          type: string
                        name:
                          description: Name of the global context entry
                          type: string
                      required:
                      - name
                      type: object
                    imageRegistry:
                      description: |-
                        ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image
                        details.
                      properties:
                        imageRegistryCredentials:
                          description: ImageRegistryCredentials provides credentials
                            that will be used for authentication with registry
                          properties:
                            allowInsecureRegistry:
                              description: AllowInsecureRegistry allows insecure access
                                to a registry.
                              type: boolean
                            providers:
                              description: |-
                                Providers specifies a list of OCI Registry names, whose authentication providers are provided.
                                It can be of one of these values: default,google,azure,amazon,github.
                              items:
                                description: ImageRegistryCredentialsProvidersType
                                  provides the list of credential providers required.
                                enum:
                                - default
                                - amazon
                                - azure
                                - google
                                - github
                                type: string
                              type: array
                            secrets:
                              description: |-
                                Secrets specifies a list of secrets that are provided for credentials.
                                Secrets must live in the Kyverno namespace.
                              items:
                                type: string
                              type: array
                          type: object
                        jmesPath:
                          description: |-
                            JMESPath is an optional JSON Match Expression that can be used to
                            transform the ImageData struct returned as a result of processing
                            the image reference.
                          type: string
                        reference:
                          description: |-
                            Reference is image reference to a container image in the registry.
                            Example: ghcr.io/kyverno/kyverno:latest
                          type: string
                      required:
                      - reference
                      type: object
                    name:
                      description: Name is the variable name.
                      type: string
                    variable:
                      description: Variable defines an arbitrary JMESPath context
                        variable that can be defined inline.
                      properties:
                        default:
                          description: |-
                            Default is an optional arbitrary JSON object that the variable may take if the JMESPath
                            expression evaluates to nil
                          x-kubernetes-preserve-unknown-fields: true
                        jmesPath:
                          description: |-
                            JMESPath is an optional JMESPath Expression that can be used to
                            transform the variable.
                          type: string
                        value:
                          description: Value is any arbitrary JSON object representable
                            in YAML or JSON form.
                          x-kubernetes-preserve-unknown-fields: true
                      type: object
                  required:
                  - name
                  type: object
                type: array
              deletionPropagationPolicy:
                description: DeletionPropagationPolicy defines how resources will
                  be deleted (Foreground, Background, Orphan).
                enum:
                - Foreground
                - Background
                - Orphan
                type: string
              exclude:
                description: |-
                  ExcludeResources defines when cleanuppolicy should not be applied. The exclude
                  criteria can include resource information (e.g. kind, name, namespace, labels)
                  and admission review request information like the name or role.
                not:
                  required:
                  - any
                  - all
                properties:
                  all:
                    description: All allows specifying resources which will be ANDed
                    items:
                      description: ResourceFilter allow users to "AND" or "OR" between
                        resources
                      properties:
                        clusterRoles:
                          description: ClusterRoles is the list of cluster-wide role
                            names for the user.
                          items:
                            type: string
                          type: array
                        resources:
                          description: ResourceDescription contains information about
                            the resource being created or modified.
                          not:
                            required:
                            - name
                            - names
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              description: |-
                                Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
                                and values support the wildcard characters "*" (matches zero or many characters) and
                                "?" (matches at least one character).
                              type: object
                            kinds:
                              description: Kinds is a list of resource kinds.
                              items:
                                type: string
                              type: array
                            name:
                              description: |-
                                Name is the name of the resource. The name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                                NOTE: "Name" is being deprecated in favor of "Names".
                              type: string
                            names:
                              description: |-
                                Names are the names of the resources. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            namespaceSelector:
                              description: |-
                                NamespaceSelector is a label selector for the resource namespace. Label keys and values
                                in `matchLabels` support the wildcard characters `*` (matches zero or many characters)
                                and `?` (matches one character).Wildcards allows writing label selectors like
                                ["storage.k8s.io/*": "*"]. Note that using ["*" : "*"] matches any key and value but
                                does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                Namespaces is a list of namespaces names. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            operations:
                              description: Operations can contain values ["CREATE,
                                "UPDATE", "CONNECT", "DELETE"], which are used to
                                match a specific action.
                              items:
                                description: AdmissionOperation can have one of the
                                  values CREATE, UPDATE, CONNECT, DELETE, which are
                                  used to match a specific action.
                                enum:
                                - CREATE
                                - CONNECT
                                - UPDATE
                                - DELETE
                                type: string
                              type: array
                            selector:
                              description: |-
                                Selector is a label selector. Label keys and values in `matchLabels` support the wildcard
                                characters `*` (matches zero or many characters) and `?` (matches one character).
                                Wildcards allows writing label selectors like ["storage.k8s.io/*": "*"]. Note that
                                using ["*" : "*"] matches any key and value but does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        roles:
                          description: Roles is the list of namespaced role names
                            for the user.
                          items:
                            type: string
                          type: array
                        subjects:
                          description: Subjects is the list of subject names like
                            users, user groups, and service accounts.
                          items:
                            description: |-
                              Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,
                              or a value for non-objects such as user and group names.
                            properties:
                              apiGroup:
                                description: |-
                                  APIGroup holds the API group of the referenced subject.
                                  Defaults to "" for ServiceAccount subjects.
                                  Defaults to "rbac.authorization.k8s.io" for User and Group subjects.
                                type: string
                              kind:
                                description: |-
                                  Kind of object being referenced. Values defined by this API group are "User", "Group", and "ServiceAccount".
                                  If the Authorizer does not recognized the kind value, the Authorizer should report an error.
                                type: string
                              name:
                                description: Name of the object being referenced.
                                type: string
                              namespace:
                                description: |-
                                  Namespace of the referenced object.  If the object kind is non-namespace, such as "User" or "Group", and this value is not empty
                                  the Authorizer should report an error.
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          type: array
                      type: object
                    type: array
                  any:
                    description: Any allows specifying resources which will be ORed
                    items:
                      description: ResourceFilter allow users to "AND" or "OR" between
                        resources
                      properties:
                        clusterRoles:
                          description: ClusterRoles is the list of cluster-wide role
                            names for the user.
                          items:
                            type: string
                          type: array
                        resources:
                          description: ResourceDescription contains information about
                            the resource being created or modified.
                          not:
                            required:
                            - name
                            - names
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              description: |-
                                Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
                                and values support the wildcard characters "*" (matches zero or many characters) and
                                "?" (matches at least one character).
                              type: object
                            kinds:
                              description: Kinds is a list of resource kinds.
                              items:
                                type: string
                              type: array
                            name:
                              description: |-
                                Name is the name of the resource. The name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                                NOTE: "Name" is being deprecated in favor of "Names".
                              type: string
                            names:
                              description: |-
                                Names are the names of the resources. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            namespaceSelector:
                              description: |-
                                NamespaceSelector is a label selector for the resource namespace. Label keys and values
                                in `matchLabels` support the wildcard characters `*` (matches zero or many characters)
                                and `?` (matches one character).Wildcards allows writing label selectors like
                                ["storage.k8s.io/*": "*"]. Note that using ["*" : "*"] matches any key and value but
                                does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                Namespaces is a list of namespaces names. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            operations:
                              description: Operations can contain values ["CREATE,
                                "UPDATE", "CONNECT", "DELETE"], which are used to
                                match a specific action.
                              items:
                                description: AdmissionOperation can have one of the
                                  values CREATE, UPDATE, CONNECT, DELETE, which are
                                  used to match a specific action.
                                enum:
                                - CREATE
                                - CONNECT
                                - UPDATE
                                - DELETE
                                type: string
                              type: array
                            selector:
                              description: |-
                                Selector is a label selector. Label keys and values in `matchLabels` support the wildcard
                                characters `*` (matches zero or many characters) and `?` (matches one character).
                                Wildcards allows writing label selectors like ["storage.k8s.io/*": "*"]. Note that
                                using ["*" : "*"] matches any key and value but does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        roles:
                          description: Roles is the list of namespaced role names
                            for the user.
                          items:
                            type: string
                          type: array
                        subjects:
                          description: Subjects is the list of subject names like
                            users, user groups, and service accounts.
                          items:
                            description: |-
                              Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,
                              or a value for non-objects such as user and group names.
                            properties:
                              apiGroup:
                                description: |-
                                  APIGroup holds the API group of the referenced subject.
                                  Defaults to "" for ServiceAccount subjects.
                                  Defaults to "rbac.authorization.k8s.io" for User and Group subjects.
                                type: string
                              kind:
                                description: |-
                                  Kind of object being referenced. Values defined by this API group are "User", "Group", and "ServiceAccount".
                                  If the Authorizer does not recognized the kind value, the Authorizer should report an error.
                                type: string
                              name:
                                description: Name of the object being referenced.
                                type: string
                              namespace:
                                description: |-
                                  Namespace of the referenced object.  If the object kind is non-namespace, such as "User" or "Group", and this value is not empty
                                  the Authorizer should report an error.
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          type: array
                      type: object
                    type: array
                type: object
              match:
                description: |-
                  MatchResources defines when cleanuppolicy should be applied. The match
                  criteria can include resource information (e.g. kind, name, namespace, labels)
                  and admission review request information like the user name or role.
                  At least one kind is required.
                not:
                  required:
                  - any
                  - all
                properties:
                  all:
                    description: All allows specifying resources which will be ANDed
                    items:
                      description: ResourceFilter allow users to "AND" or "OR" between
                        resources
                      properties:
                        clusterRoles:
                          description: ClusterRoles is the list of cluster-wide role
                            names for the user.
                          items:
                            type: string
                          type: array
                        resources:
                          description: ResourceDescription contains information about
                            the resource being created or modified.
                          not:
                            required:
                            - name
                            - names
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              description: |-
                                Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
                                and values support the wildcard characters "*" (matches zero or many characters) and
                                "?" (matches at least one character).
                              type: object
                            kinds:
                              description: Kinds is a list of resource kinds.
                              items:
                                type: string
                              type: array
                            name:
                              description: |-
                                Name is the name of the resource. The name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                                NOTE: "Name" is being deprecated in favor of "Names".
                              type: string
                            names:
                              description: |-
                                Names are the names of the resources. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            namespaceSelector:
                              description: |-
                                NamespaceSelector is a label selector for the resource namespace. Label keys and values
                                in `matchLabels` support the wildcard characters `*` (matches zero or many characters)
                                and `?` (matches one character).Wildcards allows writing label selectors like
                                ["storage.k8s.io/*": "*"]. Note that using ["*" : "*"] matches any key and value but
                                does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                Namespaces is a list of namespaces names. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            operations:
                              description: Operations can contain values ["CREATE,
                                "UPDATE", "CONNECT", "DELETE"], which are used to
                                match a specific action.
                              items:
                                description: AdmissionOperation can have one of the
                                  values CREATE, UPDATE, CONNECT, DELETE, which are
                                  used to match a specific action.
                                enum:
                                - CREATE
                                - CONNECT
                                - UPDATE
                                - DELETE
                                type: string
                              type: array
                            selector:
                              description: |-
                                Selector is a label selector. Label keys and values in `matchLabels` support the wildcard
                                characters `*` (matches zero or many characters) and `?` (matches one character).
                                Wildcards allows writing label selectors like ["storage.k8s.io/*": "*"]. Note that
                                using ["*" : "*"] matches any key and value but does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        roles:
                          description: Roles is the list of namespaced role names
                            for the user.
                          items:
                            type: string
                          type: array
                        subjects:
                          description: Subjects is the list of subject names like
                            users, user groups, and service accounts.
                          items:
                            description: |-
                              Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,
                              or a value for non-objects such as user and group names.
                            properties:
                              apiGroup:
                                description: |-
                                  APIGroup holds the API group of the referenced subject.
                                  Defaults to "" for ServiceAccount subjects.
                                  Defaults to "rbac.authorization.k8s.io" for User and Group subjects.
                                type: string
                              kind:
                                description: |-
                                  Kind of object being referenced. Values defined by this API group are "User", "Group", and "ServiceAccount".
                                  If the Authorizer does not recognized the kind value, the Authorizer should report an error.
                                type: string
                              name:
                                description: Name of the object being referenced.
                                type: string
                              namespace:
                                description: |-
                                  Namespace of the referenced object.  If the object kind is non-namespace, such as "User" or "Group", and this value is not empty
                                  the Authorizer should report an error.
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          type: array
                      type: object
                    type: array
                  any:
                    description: Any allows specifying resources which will be ORed
                    items:
                      description: ResourceFilter allow users to "AND" or "OR" between
                        resources
                      properties:
                        clusterRoles:
                          description: ClusterRoles is the list of cluster-wide role
                            names for the user.
                          items:
                            type: string
                          type: array
                        resources:
                          description: ResourceDescription contains information about
                            the resource being created or modified.
                          not:
                            required:
                            - name
                            - names
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              description: |-
                                Annotations is a  map of annotations (key-value pairs of type string). Annotation keys
                                and values support the wildcard characters "*" (matches zero or many characters) and
                                "?" (matches at least one character).
                              type: object
                            kinds:
                              description: Kinds is a list of resource kinds.
                              items:
                                type: string
                              type: array
                            name:
                              description: |-
                                Name is the name of the resource. The name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                                NOTE: "Name" is being deprecated in favor of "Names".
                              type: string
                            names:
                              description: |-
                                Names are the names of the resources. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            namespaceSelector:
                              description: |-
                                NamespaceSelector is a label selector for the resource namespace. Label keys and values
                                in `matchLabels` support the wildcard characters `*` (matches zero or many characters)
                                and `?` (matches one character).Wildcards allows writing label selectors like
                                ["storage.k8s.io/*": "*"]. Note that using ["*" : "*"] matches any key and value but
                                does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                Namespaces is a list of namespaces names. Each name supports wildcard characters
                                "*" (matches zero or many characters) and "?" (at least one character).
                              items:
                                type: string
                              type: array
                            operations:
                              description: Operations can contain values ["CREATE,
                                "UPDATE", "CONNECT", "DELETE"], which are used to
                                match a specific action.
                              items:
                                description: AdmissionOperation can have one of the
                                  values CREATE, UPDATE, CONNECT, DELETE, which are
                                  used to match a specific action.
                                enum:
                                - CREATE
                                - CONNECT
                                - UPDATE
                                - DELETE
                                type: string
                              type: array
                            selector:
                              description: |-
                                Selector is a label selector. Label keys and values in `matchLabels` support the wildcard
                                characters `*` (matches zero or many characters) and `?` (matches one character).
                                Wildcards allows writing label selectors like ["storage.k8s.io/*": "*"]. Note that
                                using ["*" : "*"] matches any key and value but does not match an empty label set.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        roles:
                          description: Roles is the list of namespaced role names
                            for the user.
                          items:
                            type: string
                          type: array
                        subjects:
                          description: Subjects is the list of subject names like
                            users, user groups, and service accounts.
                          items:
                            description: |-
                              Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,
                              or a value for non-objects such as user and group names.
                            properties:
                              apiGroup:
                                description: |-
                                  APIGroup holds the API group of the referenced subject.
                                  Defaults to "" for ServiceAccount subjects.
                                  Defaults to "rbac.authorization.k8s.io" for User and Group subjects.
                                type: string
                              kind:
                                description: |-
                                  Kind of object being referenced. Values defined by this API group are "User", "Group", and "ServiceAccount".
                                  If the Authorizer does not recognized the kind value, the Authorizer should report an error.
                                type: string
                              name:
                                description: Name of the object being referenced.
                                type: string
                              namespace:
                                description: |-
                                  Namespace of the referenced object.  If the object kind is non-namespace, such as "User" or "Group", and this value is not empty
                                  the Authorizer should report an error.
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          type: array
                      type: object
                    type: array
                type: object
              schedule:
                description: The schedule in Cron format
                type: string
            required:
            - match
            - schedule
            type: object
          status:
            description: Status contains policy runtime data.
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              lastExecutionTime:
                format: date-time
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: false
    subresources:
      status: {}

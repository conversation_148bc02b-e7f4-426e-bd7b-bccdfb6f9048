# Enhanced Logging for 'Any' Block Condition Failures

## Overview

This enhancement improves the debugging experience when 'any' block conditions fail in Kyverno policies by providing more context in log messages.

## Problem Statement

Previously, when an 'any' block condition failed, the log message was generic and didn't provide enough context to identify which specific 'any' block failed or where it was located in the policy:

```
no condition passed for 'any' block
```

This made debugging difficult, especially when policies contained multiple 'any' blocks.

## Solution

The enhanced logging now includes:

1. **Index information**: Which specific 'any' block failed (when there are multiple)
2. **Context type**: Where the condition was being evaluated (precondition, match, deny condition, etc.)
3. **All original information**: The actual conditions that failed are still included

## Enhanced Log Format

### Before
```
no condition passed for 'any' block
```

### After
```
no condition passed for 'any' block for index '0' at 'precondition'
no condition passed for 'any' block for index '1' at 'match condition'
no condition passed for 'any' block for index '0' at 'deny condition'
```

## Context Types

The following context types are now included in the logs:

- `precondition` - Rule preconditions
- `deny condition` - Deny conditions in validation rules
- `match condition` - Match conditions (future enhancement)
- `background condition` - Background processing conditions
- `background condition (old resource)` - Background processing for old resources
- `generate precondition` - Generate rule preconditions
- `attestation condition` - Image verification attestation conditions

## Example Policy and Log Output

### Example Policy
```yaml
apiVersion: kyverno.io/v1
kind: ClusterPolicy
metadata:
  name: restrict-node-selection
spec:
  validationFailureAction: Enforce
  rules:
  - name: autogen-restrict-nodename
    match:
      any:
      - resources:
          kinds:
          - Deployment
    preconditions:
      any:
      - key: "{{ request.object.spec.template.spec.nodeSelector.foo || '' }}"
        operator: AnyNotIn
        value: ["bar", "baz"]
      - key: "{{ request.object.metadata.name }}"
        operator: Equals
        value: "allowed-deployment"
    validate:
      message: "Node selection is restricted"
      pattern:
        spec:
          template:
            spec:
              nodeSelector:
                foo: "!baz"
```

### Enhanced Log Output
```
2025-07-17T08:47:36Z INFO no condition passed for 'any' block for index '0' at 'precondition' 
  any=[{"key":"{{ request.object.spec.template.spec.nodeSelector.foo || '' }}","operator":"AnyNotIn","value":"[\"bar\", \"baz\"]"},{"key":"{{ request.object.metadata.name }}","operator":"Equals","value":"allowed-deployment"}] 
  policy.name=restrict-node-selection 
  rule.name=autogen-restrict-nodename 
  new.kind=Deployment 
  new.namespace=foo 
  new.name=foo-deployment
```

## Benefits

1. **Faster Debugging**: Developers can quickly identify which 'any' block failed
2. **Better Context**: Understanding where the failure occurred (precondition vs match vs deny)
3. **Multiple Block Support**: When policies have multiple 'any' blocks, the index helps identify the specific one
4. **Backward Compatibility**: All existing functionality remains unchanged

## Implementation Details

### New Functions Added

- `EvaluateConditionsWithContext()` - Evaluates conditions with additional context information
- `EvaluateAnyAllConditionsWithContext()` - Evaluates any/all conditions with context

### Modified Functions

- `evaluateAnyAllConditions()` - Now accepts context type and index parameters
- `CheckPreconditions()` - Now uses context-aware evaluation
- `CheckDenyPreconditions()` - Now uses context-aware evaluation

### Backward Compatibility

All existing functions remain available and unchanged:
- `EvaluateConditions()` - Wrapper that calls the new context-aware function
- `EvaluateAnyAllConditions()` - Wrapper that calls the new context-aware function

## Testing

The enhancement includes comprehensive tests that verify:
- Enhanced logging works correctly with different context types
- Backward compatibility is maintained
- No regressions in existing functionality
- Multiple 'any' blocks are properly indexed

## Usage for Contributors

When adding new condition evaluation points, use the context-aware functions:

```go
// Instead of:
pass, msg, err := variables.EvaluateConditions(logger, jsonContext, conditions)

// Use:
pass, msg, err := variables.EvaluateConditionsWithContext(logger, jsonContext, conditions, "your-context-type")
```

This ensures that users get the enhanced debugging information when conditions fail.

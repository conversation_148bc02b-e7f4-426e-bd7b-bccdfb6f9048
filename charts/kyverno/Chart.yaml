apiVersion: v2
type: application
name: kyverno
version: v0.0.0
appVersion: latest
icon: https://github.com/kyverno/kyverno/raw/main/img/logo.png
description: Kubernetes Native Policy Management
keywords:
  - kubernetes
  - nirmata
  - policy agent
  - policy
  - validating webhook
  - admission controller
  - mutation
  - mutate
  - validate
  - generate
  - supply chain
  - security
home: https://kyverno.io/
sources:
  - https://github.com/kyverno/kyverno
maintainers:
  - name: Nirmata
    url: https://kyverno.io/
kubeVersion: ">=1.25.0-0"
annotations:
  artifacthub.io/operator: "false"
  artifacthub.io/prerelease: "false"
  artifacthub.io/links: |
    - name: Documentation
      url: https://kyverno.io/docs
  # valid kinds are: added, changed, deprecated, removed, fixed and security
  artifacthub.io/changes: |
    - kind: fixed
      description: Ensure spec.template.metadata isn't null
    - kind: removed
      description: Remove the `delete` permission for policyexceptions in the admission controller
    - kind: changed
      description: Enable the flag `--generateValidatingAdmissionPolicy` by default in the admission controller.
    - kind: changed
      description: Enable the flag `--validatingAdmissionPolicyReports` by default in the reports controller.
dependencies:
  - name: grafana
    version: v0.0.0
    condition: grafana.enabled
  - name: crds
    version: v0.0.0
    condition: crds.install
  - name: openreports
    version: "0.1.0"
    repository: "https://openreports.github.io/reports-api"
    condition: openreports.enabled

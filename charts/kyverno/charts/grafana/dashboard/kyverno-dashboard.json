{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 472, "links": [], "panels": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 42, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "# Kyverno\nA Kubernetes-native policy management engine\n\n#### About this dashboard\n\nThis dashboard represents generic insights that can be extracted from a cluster with Kyverno running.\n\n#### For more details around the metrics\n\nCheckout the [official docs of Kyverno metrics](https://kyverno.io/docs/monitoring/)", "mode": "markdown"}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "refId": "A"}], "transparent": true, "type": "text"}, {"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 12, "panels": [], "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "refId": "A"}], "title": "Latest Status", "type": "row"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "text", "value": null}, {"color": "green", "value": 0}, {"color": "#eab839", "value": 25}, {"color": "red", "value": 50}, {"color": "red", "value": 100}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 7}, "id": 29, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_policy_results_total{rule_result=\"fail\", cluster=~\"$cluster\"}[24h]) or vector(0))*100/sum(increase(kyverno_policy_results_total{cluster=~\"$cluster\"}[24h]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Rule Execution Failure Rate (Last 24 Hours)", "transparent": true, "type": "gauge"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 8, "y": 7}, "id": 2, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "count(count(kyverno_policy_rule_info_total{policy_type=\"cluster\",cluster=~\"$cluster\"}==1) by (policy_name))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Cluster Policies", "type": "stat"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 12, "y": 7}, "id": 3, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "count(count(kyverno_policy_rule_info_total{policy_type=\"namespaced\", cluster=~\"$cluster\"}==1) by (policy_name))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Policies", "type": "stat"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "text", "value": null}, {"color": "green", "value": 0}, {"color": "#eab839", "value": 25}, {"color": "red", "value": 50}, {"color": "red", "value": 100}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 7}, "id": 28, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_policy_results_total{rule_result=\"fail\", policy_background_mode=\"true\", cluster=~\"$cluster\"}[24h]) or vector(0))*100/sum(increase(kyverno_policy_results_total{policy_background_mode=\"true\", cluster=~\"$cluster\"}[24h]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Background Scans Failure Rate (Last 24 Hours)", "transparent": true, "type": "gauge"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 6, "y": 12}, "id": 4, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "count(count(kyverno_policy_rule_info_total{rule_type=\"validate\", cluster=~\"$cluster\"}==1) by (rule_name))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Validate Rules", "type": "stat"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 10, "y": 12}, "id": 23, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "count(count(kyverno_policy_rule_info_total{rule_type=\"mutate\", cluster=~\"$cluster\"}==1) by (rule_name))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Mutate Rules", "type": "stat"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 14, "y": 12}, "id": 6, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "count(count(kyverno_policy_rule_info_total{rule_type=\"generate\", cluster=~\"$cluster\"}==1) by (rule_name))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Generate Rules", "type": "stat"}, {"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 26, "panels": [], "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "refId": "A"}], "title": "Policy-Rule Results", "type": "row"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pass"}, "properties": [{"id": "color", "value": {"fixedColor": "rgb(43, 219, 23)", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "fail"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 17}, "id": 15, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_policy_results_total{rule_execution_cause=\"admission_request\", cluster=~\"$cluster\"}[5m])) by (rule_result)", "interval": "", "legendFormat": "Admission Review Result: {{rule_result}}", "refId": "A"}], "title": "Admission Review Results (per-rule)", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pass"}, "properties": [{"id": "color", "value": {"fixedColor": "rgb(43, 219, 23)", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "fail"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 17}, "id": 17, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_policy_results_total{rule_execution_cause=\"background_scan\", cluster=~\"$cluster\"}[5m])) by (rule_result)", "interval": "", "legendFormat": "Background Scan Result: {{rule_result}}", "refId": "A"}], "title": "Background Scan Results (per-rule)", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster"}, "properties": [{"id": "color", "value": {"fixedColor": "#5794F2", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "namespaced"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 16, "w": 8, "x": 16, "y": 17}, "id": 30, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(sum(increase(kyverno_policy_results_total{rule_result=\"fail\", cluster=~\"$cluster\"}[5m])) by (policy_name, policy_type)) by (policy_type)", "interval": "", "legendFormat": "Policy Type: {{policy_type}}", "refId": "A"}], "title": "Policy Failures", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pass"}, "properties": [{"id": "color", "value": {"fixedColor": "rgb(43, 219, 23)", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "fail"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 25}, "id": 31, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(sum(increase(kyverno_policy_results_total{rule_execution_cause=\"admission_request\", cluster=~\"$cluster\"}[5m])) by (policy_name, rule_result)) by (rule_result)", "interval": "", "legendFormat": "Admission Review Result: {{rule_result}}", "refId": "A"}], "title": "Admission Review Results (per-policy)", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pass"}, "properties": [{"id": "color", "value": {"fixedColor": "rgb(43, 219, 23)", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "fail"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 25}, "id": 32, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(sum(increase(kyverno_policy_results_total{rule_execution_cause=\"background_scan\", cluster=~\"$cluster\"}[5m])) by (policy_name, rule_result)) by (rule_result)", "interval": "", "legendFormat": "Background Scan Result: {{rule_result}}", "refId": "A"}], "title": "Background Scan Results (per-policy)", "type": "timeseries"}, {"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 33}, "id": 19, "panels": [], "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "refId": "A"}], "title": "Policy-Rule Info", "type": "row"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster"}, "properties": [{"id": "color", "value": {"fixedColor": "#5794F2", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "namespaced"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF7383", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 34}, "id": 16, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "count(count(kyverno_policy_rule_info_total{cluster=~\"$cluster\"}==1) by (policy_name, policy_type)) by (policy_type)", "interval": "", "legendFormat": "Policy Type: {{policy_type}}", "refId": "A"}], "title": "Active Policies (by policy type)", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "audit"}, "properties": [{"id": "color", "value": {"fixedColor": "#37872D", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "enforce"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF9830", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 34}, "id": 20, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "count(count(kyverno_policy_rule_info_total{cluster=~\"$cluster\"}==1) by (policy_name, policy_validation_mode)) by (policy_validation_mode)", "interval": "", "legendFormat": "Policy Validation Mode: {{policy_validation_mode}}", "refId": "A"}], "title": "Active Policies (by policy validation action)", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster"}, "properties": [{"id": "color", "value": {"fixedColor": "#B877D9", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 34}, "id": 24, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "count(count(kyverno_policy_rule_info_total{policy_background_mode=\"true\", cluster=~\"$cluster\"}==1) by (policy_name, policy_type)) by (policy_type)", "interval": "", "legendFormat": "Policy Type: {{policy_type}}", "refId": "A"}], "title": "Active Policies running in background mode", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 42}, "id": 21, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "count(count(kyverno_policy_rule_info_total{policy_namespace!=\"-\", cluster=~\"$cluster\"}==1) by (policy_name, policy_namespace)) by (policy_namespace)", "interval": "", "legendFormat": "Namespace: {{policy_namespace}}", "refId": "A"}], "title": "Active Namespaced Policies (by namespaces)", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "mutate"}, "properties": [{"id": "color", "value": {"fixedColor": "rgb(169, 58, 227)", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "validate"}, "properties": [{"id": "color", "value": {"fixedColor": "rgb(255, 232, 0)", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 42}, "id": 14, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "count(count(kyverno_policy_rule_info_total{cluster=~\"$cluster\"}==1) by (rule_type, rule_name)) by (rule_type)", "interval": "", "legendFormat": "Rule Type: {{rule_type}}", "refId": "A"}], "title": "Active Rules (by rule type)", "type": "timeseries"}, {"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 34, "panels": [], "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "refId": "A"}], "title": "Policy-Rule Execution Latency", "type": "row"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 0, "y": 51}, "id": 36, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(rate(kyverno_policy_execution_duration_seconds_sum{cluster=~\"$cluster\"}[5m])) by (rule_type) / sum(rate(kyverno_policy_execution_duration_seconds_count{cluster=~\"$cluster\"}[5m])) by (rule_type)", "interval": "", "legendFormat": "Rule Type: {{rule_type}}", "refId": "A"}], "title": "Average Rule Execution Latency Over Time", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "clocks"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster"}, "properties": [{"id": "color", "value": {"fixedColor": "#5794F2", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "namespaced"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 9, "x": 9, "y": 51}, "id": 37, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(rate(kyverno_policy_execution_duration_seconds_sum{cluster=~\"$cluster\"}[5m])) by (policy_type) / sum(rate(kyverno_policy_execution_duration_seconds_count{cluster=~\"$cluster\"}[5m])) by (policy_type)", "interval": "", "legendFormat": "Policy Type: {{policy_type}}", "refId": "A"}], "title": "Average Policy Execution Latency Over Time", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "purple", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 51}, "id": 39, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(kyverno_policy_execution_duration_seconds_sum{cluster=~\"$cluster\"}) / sum(kyverno_policy_execution_duration_seconds_count{cluster=~\"$cluster\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Overall Average Rule Execution Latency", "type": "stat"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 55}, "id": 40, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "avg(sum(kyverno_policy_execution_duration_seconds_sum{cluster=~\"$cluster\"}) by (policy_name, policy_type) / sum(kyverno_policy_execution_duration_seconds_count{cluster=~\"$cluster\"}) by (policy_name, policy_type))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Overall Average Policy Execution Latency", "type": "stat"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 59}, "id": 52, "panels": [], "targets": [{"datasource": null, "refId": "A"}], "title": "Admission Review Latency", "type": "row"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 0, "y": 60}, "id": 53, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(rate(kyverno_admission_review_duration_seconds_sum{cluster=~\"$cluster\"}[5m])) by (resource_request_operation) / sum(rate(kyverno_admission_review_duration_seconds_count{cluster=~\"$cluster\"}[5m])) by (resource_request_operation)", "interval": "", "legendFormat": "Resource Operation: {{resource_request_operation}}", "refId": "A"}], "title": "Avg - Admission Review Duration Over Time (by operation)", "transparent": true, "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 9, "y": 60}, "id": 54, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(rate(kyverno_admission_review_duration_seconds_sum{cluster=~\"$cluster\"}[5m])) by (resource_kind) / sum(rate(kyverno_admission_review_duration_seconds_count{cluster=~\"$cluster\"}[5m])) by (resource_kind)", "interval": "", "legendFormat": "Resource Kind: {{resource_kind}}", "refId": "A"}], "title": "Avg - Admission Review Duration Over Time (by resource kind)", "transparent": true, "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 60}, "id": 50, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_admission_requests_total{cluster=~\"$cluster\"}[5m]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Rate - Incoming Admission Requests (per 5m)", "type": "stat"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "purple", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 64}, "id": 55, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(kyverno_admission_review_duration_seconds_sum{cluster=~\"$cluster\"})/sum(kyverno_admission_review_duration_seconds_count{cluster=~\"$cluster\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Avg - Overall Admission Review Duration", "type": "stat"}, {"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 68}, "id": 8, "panels": [], "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "refId": "A"}], "title": "Policy Changes", "type": "row"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Change type: created"}, "properties": [{"id": "color", "value": {"fixedColor": "#5794F2", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 9, "x": 0, "y": 69}, "id": 10, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_policy_changes_total{cluster=~\"$cluster\"}[5m])) by (policy_change_type)", "interval": "", "legendFormat": "Change type: {{policy_change_type}}", "refId": "A"}], "title": "Policy Changes Over Time (by change type)", "transparent": true, "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 9, "x": 9, "y": 69}, "id": 13, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_policy_changes_total{cluster=~\"$cluster\"}[5m])) by (policy_type)", "interval": "", "legendFormat": "Policy Type: {{policy_type}}", "refId": "A"}], "title": "Policy Changes Over Time (by policy type)", "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "orange", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 69}, "id": 49, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_policy_changes_total{cluster=~\"$cluster\"}[24h]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Total Policy Changes (Last 24 Hours)", "type": "stat"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 73}, "id": 48, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(rate(kyverno_policy_changes_total{cluster=~\"$cluster\"}[5m]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Rate - Policy Changes Happening (last 5m)", "type": "stat"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 77}, "id": 44, "panels": [], "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "refId": "A"}], "title": "Admission Requests", "type": "row"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Change type: created"}, "properties": [{"id": "color", "value": {"fixedColor": "#5794F2", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 9, "x": 0, "y": 78}, "id": 45, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_admission_requests_total{cluster=~\"$cluster\"}[5m])) by (resource_request_operation)", "interval": "", "legendFormat": "Resource Operation: {{resource_request_operation}}", "refId": "A"}], "title": "Admission Requests (by operation)", "transparent": true, "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Change type: created"}, "properties": [{"id": "color", "value": {"fixedColor": "#5794F2", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 9, "x": 9, "y": 78}, "id": 46, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.1.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_admission_requests_total{cluster=~\"$cluster\"}[5m])) by (resource_kind)", "interval": "", "legendFormat": "Resource Kind: {{resource_kind}}", "refId": "A"}], "title": "Admission Requests (by resource kind)", "transparent": true, "type": "timeseries"}, {"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 78}, "id": 47, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "exemplar": true, "expr": "sum(increase(kyverno_admission_requests_total{cluster=~\"$cluster\"}[24h]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Total Admission Requests (Last 24 Hours)", "type": "stat"}], "refresh": false, "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": null, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "DS_PROMETHEUS_KYVERNO", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".*", "current": {"selected": false, "text": ["All"], "value": ["$__all"]}, "datasource": {"uid": "${DS_PROMETHEUS_KYVERNO}"}, "definition": "label_values(kyverno_policy_results_total, cluster)", "hide": 0, "includeAll": true, "label": "Cluster", "multi": true, "name": "cluster", "options": [], "query": "label_values(kyverno_policy_results_total, cluster)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Kyverno Metrics", "uid": "Rg8lWBG7k", "version": 2, "weekStart": ""}
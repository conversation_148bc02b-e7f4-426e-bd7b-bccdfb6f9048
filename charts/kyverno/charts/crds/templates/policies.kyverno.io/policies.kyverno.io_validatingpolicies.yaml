{{- if .Values.groups.policies.validatingpolicies }}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  labels:
    {{- include "kyverno.crds.labels" . | nindent 4 }}
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    controller-gen.kubebuilder.io/version: v0.17.3
  name: validatingpolicies.policies.kyverno.io
spec:
  group: policies.kyverno.io
  names:
    categories:
    - kyverno
    kind: ValidatingPolicy
    listKind: ValidatingPolicyList
    plural: validatingpolicies
    shortNames:
    - vpol
    singular: validatingpolicy
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    - jsonPath: .status.conditionStatus.ready
      name: READY
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ValidatingPolicySpec is the specification of the desired
              behavior of the ValidatingPolicy.
            properties:
              auditAnnotations:
                description: |-
                  auditAnnotations contains CEL expressions which are used to produce audit
                  annotations for the audit event of the API request.
                  validations and auditAnnotations may not both be empty; a least one of validations or auditAnnotations is
                  required.
                items:
                  description: AuditAnnotation describes how to produce an audit annotation
                    for an API request.
                  properties:
                    key:
                      description: |-
                        key specifies the audit annotation key. The audit annotation keys of
                        a ValidatingAdmissionPolicy must be unique. The key must be a qualified
                        name ([A-Za-z0-9][-A-Za-z0-9_.]*) no more than 63 bytes in length.

                        The key is combined with the resource name of the
                        ValidatingAdmissionPolicy to construct an audit annotation key:
                        "{ValidatingAdmissionPolicy name}/{key}".

                        If an admission webhook uses the same resource name as this ValidatingAdmissionPolicy
                        and the same audit annotation key, the annotation key will be identical.
                        In this case, the first annotation written with the key will be included
                        in the audit event and all subsequent annotations with the same key
                        will be discarded.

                        Required.
                      type: string
                    valueExpression:
                      description: |-
                        valueExpression represents the expression which is evaluated by CEL to
                        produce an audit annotation value. The expression must evaluate to either
                        a string or null value. If the expression evaluates to a string, the
                        audit annotation is included with the string value. If the expression
                        evaluates to null or empty string the audit annotation will be omitted.
                        The valueExpression may be no longer than 5kb in length.
                        If the result of the valueExpression is more than 10kb in length, it
                        will be truncated to 10kb.

                        If multiple ValidatingAdmissionPolicyBinding resources match an
                        API request, then the valueExpression will be evaluated for
                        each binding. All unique values produced by the valueExpressions
                        will be joined together in a comma-separated list.

                        Required.
                      type: string
                  required:
                  - key
                  - valueExpression
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              autogen:
                description: AutogenConfiguration defines the configuration for the
                  generation controller.
                properties:
                  podControllers:
                    description: PodControllers specifies whether to generate a pod
                      controllers rules.
                    properties:
                      controllers:
                        items:
                          type: string
                        type: array
                    type: object
                  validatingAdmissionPolicy:
                    description: ValidatingAdmissionPolicy specifies whether to generate
                      a Kubernetes ValidatingAdmissionPolicy.
                    properties:
                      enabled:
                        description: |-
                          Enabled specifies whether to generate a Kubernetes ValidatingAdmissionPolicy.
                          Optional. Defaults to "false" if not specified.
                        type: boolean
                    type: object
                type: object
              evaluation:
                description: EvaluationConfiguration defines the configuration for
                  the policy evaluation.
                properties:
                  admission:
                    description: Admission controls policy evaluation during admission.
                    properties:
                      enabled:
                        default: true
                        description: |-
                          Enabled controls if rules are applied during admission.
                          Optional. Default value is "true".
                        type: boolean
                    type: object
                  background:
                    description: Background  controls policy evaluation during background
                      scan.
                    properties:
                      enabled:
                        default: true
                        description: |-
                          Enabled controls if rules are applied to existing resources during a background scan.
                          Optional. Default value is "true". The value must be set to "false" if the policy rule
                          uses variables that are only available in the admission review request (e.g. user name).
                        type: boolean
                    type: object
                  mode:
                    description: |-
                      Mode is the mode of policy evaluation.
                      Allowed values are "Kubernetes" or "JSON".
                      Optional. Default value is "Kubernetes".
                    type: string
                type: object
              failurePolicy:
                description: |-
                  failurePolicy defines how to handle failures for the admission policy. Failures can
                  occur from CEL expression parse errors, type check errors, runtime errors and invalid
                  or mis-configured policy definitions or bindings.

                  failurePolicy does not define how validations that evaluate to false are handled.

                  When failurePolicy is set to Fail, the validationActions field define how failures are enforced.

                  Allowed values are Ignore or Fail. Defaults to Fail.
                enum:
                - Ignore
                - Fail
                type: string
              matchConditions:
                description: |-
                  MatchConditions is a list of conditions that must be met for a request to be validated.
                  Match conditions filter requests that have already been matched by the rules,
                  namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.
                  There are a maximum of 64 match conditions allowed.

                  If a parameter object is provided, it can be accessed via the `params` handle in the same
                  manner as validation expressions.

                  The exact matching logic is (in order):
                    1. If ANY matchCondition evaluates to FALSE, the policy is skipped.
                    2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.
                    3. If any matchCondition evaluates to an error (but none are FALSE):
                       - If failurePolicy=Fail, reject the request
                       - If failurePolicy=Ignore, the policy is skipped
                items:
                  description: MatchCondition represents a condition which must by
                    fulfilled for a request to be sent to a webhook.
                  properties:
                    expression:
                      description: |-
                        Expression represents the expression which will be evaluated by CEL. Must evaluate to bool.
                        CEL expressions have access to the contents of the AdmissionRequest and Authorizer, organized into CEL variables:

                        'object' - The object from the incoming request. The value is null for DELETE requests.
                        'oldObject' - The existing object. The value is null for CREATE requests.
                        'request' - Attributes of the admission request(/pkg/apis/admission/types.go#AdmissionRequest).
                        'authorizer' - A CEL Authorizer. May be used to perform authorization checks for the principal (user or service account) of the request.
                          See https://pkg.go.dev/k8s.io/apiserver/pkg/cel/library#Authz
                        'authorizer.requestResource' - A CEL ResourceCheck constructed from the 'authorizer' and configured with the
                          request resource.
                        Documentation on CEL: https://kubernetes.io/docs/reference/using-api/cel/

                        Required.
                      type: string
                    name:
                      description: |-
                        Name is an identifier for this match condition, used for strategic merging of MatchConditions,
                        as well as providing an identifier for logging purposes. A good name should be descriptive of
                        the associated expression.
                        Name must be a qualified name consisting of alphanumeric characters, '-', '_' or '.', and
                        must start and end with an alphanumeric character (e.g. 'MyName',  or 'my.name',  or
                        '123-abc', regex used for validation is '([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9]') with an
                        optional DNS subdomain prefix and '/' (e.g. 'example.com/MyName')

                        Required.
                      type: string
                  required:
                  - expression
                  - name
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - name
                x-kubernetes-list-type: map
              matchConstraints:
                description: |-
                  MatchConstraints specifies what resources this policy is designed to validate.
                  The AdmissionPolicy cares about a request if it matches _all_ Constraints.
                  Required.
                properties:
                  excludeResourceRules:
                    description: |-
                      ExcludeResourceRules describes what operations on what resources/subresources the ValidatingAdmissionPolicy should not care about.
                      The exclude rules take precedence over include rules (if a resource matches both, it is excluded)
                    items:
                      description: NamedRuleWithOperations is a tuple of Operations
                        and Resources with ResourceNames.
                      properties:
                        apiGroups:
                          description: |-
                            APIGroups is the API groups the resources belong to. '*' is all groups.
                            If '*' is present, the length of the slice must be one.
                            Required.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        apiVersions:
                          description: |-
                            APIVersions is the API versions the resources belong to. '*' is all versions.
                            If '*' is present, the length of the slice must be one.
                            Required.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        operations:
                          description: |-
                            Operations is the operations the admission hook cares about - CREATE, UPDATE, DELETE, CONNECT or *
                            for all of those operations and any future admission operations that are added.
                            If '*' is present, the length of the slice must be one.
                            Required.
                          items:
                            description: OperationType specifies an operation for
                              a request.
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        resourceNames:
                          description: ResourceNames is an optional white list of
                            names that the rule applies to.  An empty set means that
                            everything is allowed.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        resources:
                          description: |-
                            Resources is a list of resources this rule applies to.

                            For example:
                            'pods' means pods.
                            'pods/log' means the log subresource of pods.
                            '*' means all resources, but not subresources.
                            'pods/*' means all subresources of pods.
                            '*/scale' means all scale subresources.
                            '*/*' means all resources and their subresources.

                            If wildcard is present, the validation rule will ensure resources do not
                            overlap with each other.

                            Depending on the enclosing object, subresources might not be allowed.
                            Required.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        scope:
                          description: |-
                            scope specifies the scope of this rule.
                            Valid values are "Cluster", "Namespaced", and "*"
                            "Cluster" means that only cluster-scoped resources will match this rule.
                            Namespace API objects are cluster-scoped.
                            "Namespaced" means that only namespaced resources will match this rule.
                            "*" means that there are no scope restrictions.
                            Subresources match the scope of their parent resource.
                            Default is "*".
                          type: string
                      type: object
                      x-kubernetes-map-type: atomic
                    type: array
                    x-kubernetes-list-type: atomic
                  matchPolicy:
                    description: |-
                      matchPolicy defines how the "MatchResources" list is used to match incoming requests.
                      Allowed values are "Exact" or "Equivalent".

                      - Exact: match a request only if it exactly matches a specified rule.
                      For example, if deployments can be modified via apps/v1, apps/v1beta1, and extensions/v1beta1,
                      but "rules" only included `apiGroups:["apps"], apiVersions:["v1"], resources: ["deployments"]`,
                      a request to apps/v1beta1 or extensions/v1beta1 would not be sent to the ValidatingAdmissionPolicy.

                      - Equivalent: match a request if modifies a resource listed in rules, even via another API group or version.
                      For example, if deployments can be modified via apps/v1, apps/v1beta1, and extensions/v1beta1,
                      and "rules" only included `apiGroups:["apps"], apiVersions:["v1"], resources: ["deployments"]`,
                      a request to apps/v1beta1 or extensions/v1beta1 would be converted to apps/v1 and sent to the ValidatingAdmissionPolicy.

                      Defaults to "Equivalent"
                    type: string
                  namespaceSelector:
                    description: |-
                      NamespaceSelector decides whether to run the admission control policy on an object based
                      on whether the namespace for that object matches the selector. If the
                      object itself is a namespace, the matching is performed on
                      object.metadata.labels. If the object is another cluster scoped resource,
                      it never skips the policy.

                      For example, to run the webhook on any objects whose namespace is not
                      associated with "runlevel" of "0" or "1";  you will set the selector as
                      follows:
                      "namespaceSelector": {
                        "matchExpressions": [
                          {
                            "key": "runlevel",
                            "operator": "NotIn",
                            "values": [
                              "0",
                              "1"
                            ]
                          }
                        ]
                      }

                      If instead you want to only run the policy on any objects whose
                      namespace is associated with the "environment" of "prod" or "staging";
                      you will set the selector as follows:
                      "namespaceSelector": {
                        "matchExpressions": [
                          {
                            "key": "environment",
                            "operator": "In",
                            "values": [
                              "prod",
                              "staging"
                            ]
                          }
                        ]
                      }

                      See
                      https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
                      for more examples of label selectors.

                      Default to the empty LabelSelector, which matches everything.
                    properties:
                      matchExpressions:
                        description: matchExpressions is a list of label selector
                          requirements. The requirements are ANDed.
                        items:
                          description: |-
                            A label selector requirement is a selector that contains values, a key, and an operator that
                            relates the key and values.
                          properties:
                            key:
                              description: key is the label key that the selector
                                applies to.
                              type: string
                            operator:
                              description: |-
                                operator represents a key's relationship to a set of values.
                                Valid operators are In, NotIn, Exists and DoesNotExist.
                              type: string
                            values:
                              description: |-
                                values is an array of string values. If the operator is In or NotIn,
                                the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                the values array must be empty. This array is replaced during a strategic
                                merge patch.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          required:
                          - key
                          - operator
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      matchLabels:
                        additionalProperties:
                          type: string
                        description: |-
                          matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                          map is equivalent to an element of matchExpressions, whose key field is "key", the
                          operator is "In", and the values array contains only "value". The requirements are ANDed.
                        type: object
                    type: object
                    x-kubernetes-map-type: atomic
                  objectSelector:
                    description: |-
                      ObjectSelector decides whether to run the validation based on if the
                      object has matching labels. objectSelector is evaluated against both
                      the oldObject and newObject that would be sent to the cel validation, and
                      is considered to match if either object matches the selector. A null
                      object (oldObject in the case of create, or newObject in the case of
                      delete) or an object that cannot have labels (like a
                      DeploymentRollback or a PodProxyOptions object) is not considered to
                      match.
                      Use the object selector only if the webhook is opt-in, because end
                      users may skip the admission webhook by setting the labels.
                      Default to the empty LabelSelector, which matches everything.
                    properties:
                      matchExpressions:
                        description: matchExpressions is a list of label selector
                          requirements. The requirements are ANDed.
                        items:
                          description: |-
                            A label selector requirement is a selector that contains values, a key, and an operator that
                            relates the key and values.
                          properties:
                            key:
                              description: key is the label key that the selector
                                applies to.
                              type: string
                            operator:
                              description: |-
                                operator represents a key's relationship to a set of values.
                                Valid operators are In, NotIn, Exists and DoesNotExist.
                              type: string
                            values:
                              description: |-
                                values is an array of string values. If the operator is In or NotIn,
                                the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                the values array must be empty. This array is replaced during a strategic
                                merge patch.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          required:
                          - key
                          - operator
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      matchLabels:
                        additionalProperties:
                          type: string
                        description: |-
                          matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                          map is equivalent to an element of matchExpressions, whose key field is "key", the
                          operator is "In", and the values array contains only "value". The requirements are ANDed.
                        type: object
                    type: object
                    x-kubernetes-map-type: atomic
                  resourceRules:
                    description: |-
                      ResourceRules describes what operations on what resources/subresources the ValidatingAdmissionPolicy matches.
                      The policy cares about an operation if it matches _any_ Rule.
                    items:
                      description: NamedRuleWithOperations is a tuple of Operations
                        and Resources with ResourceNames.
                      properties:
                        apiGroups:
                          description: |-
                            APIGroups is the API groups the resources belong to. '*' is all groups.
                            If '*' is present, the length of the slice must be one.
                            Required.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        apiVersions:
                          description: |-
                            APIVersions is the API versions the resources belong to. '*' is all versions.
                            If '*' is present, the length of the slice must be one.
                            Required.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        operations:
                          description: |-
                            Operations is the operations the admission hook cares about - CREATE, UPDATE, DELETE, CONNECT or *
                            for all of those operations and any future admission operations that are added.
                            If '*' is present, the length of the slice must be one.
                            Required.
                          items:
                            description: OperationType specifies an operation for
                              a request.
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        resourceNames:
                          description: ResourceNames is an optional white list of
                            names that the rule applies to.  An empty set means that
                            everything is allowed.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        resources:
                          description: |-
                            Resources is a list of resources this rule applies to.

                            For example:
                            'pods' means pods.
                            'pods/log' means the log subresource of pods.
                            '*' means all resources, but not subresources.
                            'pods/*' means all subresources of pods.
                            '*/scale' means all scale subresources.
                            '*/*' means all resources and their subresources.

                            If wildcard is present, the validation rule will ensure resources do not
                            overlap with each other.

                            Depending on the enclosing object, subresources might not be allowed.
                            Required.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        scope:
                          description: |-
                            scope specifies the scope of this rule.
                            Valid values are "Cluster", "Namespaced", and "*"
                            "Cluster" means that only cluster-scoped resources will match this rule.
                            Namespace API objects are cluster-scoped.
                            "Namespaced" means that only namespaced resources will match this rule.
                            "*" means that there are no scope restrictions.
                            Subresources match the scope of their parent resource.
                            Default is "*".
                          type: string
                      type: object
                      x-kubernetes-map-type: atomic
                    type: array
                    x-kubernetes-list-type: atomic
                type: object
                x-kubernetes-map-type: atomic
              validationActions:
                description: |-
                  ValidationAction specifies the action to be taken when the matched resource violates the policy.
                  Required.
                items:
                  description: ValidationAction specifies a policy enforcement action.
                  enum:
                  - Deny
                  - Audit
                  - Warn
                  type: string
                type: array
                x-kubernetes-list-type: set
              validations:
                description: |-
                  Validations contain CEL expressions which is used to apply the validation.
                  Validations and AuditAnnotations may not both be empty; a minimum of one Validations or AuditAnnotations is
                  required.
                items:
                  description: Validation specifies the CEL expression which is used
                    to apply the validation.
                  properties:
                    expression:
                      description: "Expression represents the expression which will
                        be evaluated by CEL.\nref: https://github.com/google/cel-spec\nCEL
                        expressions have access to the contents of the API request/response,
                        organized into CEL variables as well as some other useful
                        variables:\n\n- 'object' - The object from the incoming request.
                        The value is null for DELETE requests.\n- 'oldObject' - The
                        existing object. The value is null for CREATE requests.\n-
                        'request' - Attributes of the API request([ref](/pkg/apis/admission/types.go#AdmissionRequest)).\n-
                        'params' - Parameter resource referred to by the policy binding
                        being evaluated. Only populated if the policy has a ParamKind.\n-
                        'namespaceObject' - The namespace object that the incoming
                        object belongs to. The value is null for cluster-scoped resources.\n-
                        'variables' - Map of composited variables, from its name to
                        its lazily evaluated value.\n  For example, a variable named
                        'foo' can be accessed as 'variables.foo'.\n- 'authorizer'
                        - A CEL Authorizer. May be used to perform authorization checks
                        for the principal (user or service account) of the request.\n
                        \ See https://pkg.go.dev/k8s.io/apiserver/pkg/cel/library#Authz\n-
                        'authorizer.requestResource' - A CEL ResourceCheck constructed
                        from the 'authorizer' and configured with the\n  request resource.\n\nThe
                        `apiVersion`, `kind`, `metadata.name` and `metadata.generateName`
                        are always accessible from the root of the\nobject. No other
                        metadata properties are accessible.\n\nOnly property names
                        of the form `[a-zA-Z_.-/][a-zA-Z0-9_.-/]*` are accessible.\nAccessible
                        property names are escaped according to the following rules
                        when accessed in the expression:\n- '__' escapes to '__underscores__'\n-
                        '.' escapes to '__dot__'\n- '-' escapes to '__dash__'\n- '/'
                        escapes to '__slash__'\n- Property names that exactly match
                        a CEL RESERVED keyword escape to '__{keyword}__'. The keywords
                        are:\n\t  \"true\", \"false\", \"null\", \"in\", \"as\", \"break\",
                        \"const\", \"continue\", \"else\", \"for\", \"function\",
                        \"if\",\n\t  \"import\", \"let\", \"loop\", \"package\", \"namespace\",
                        \"return\".\nExamples:\n  - Expression accessing a property
                        named \"namespace\": {\"Expression\": \"object.__namespace__
                        > 0\"}\n  - Expression accessing a property named \"x-prop\":
                        {\"Expression\": \"object.x__dash__prop > 0\"}\n  - Expression
                        accessing a property named \"redact__d\": {\"Expression\":
                        \"object.redact__underscores__d > 0\"}\n\nEquality on arrays
                        with list type of 'set' or 'map' ignores element order, i.e.
                        [1, 2] == [2, 1].\nConcatenation on arrays with x-kubernetes-list-type
                        use the semantics of the list type:\n  - 'set': `X + Y` performs
                        a union where the array positions of all elements in `X` are
                        preserved and\n    non-intersecting elements in `Y` are appended,
                        retaining their partial order.\n  - 'map': `X + Y` performs
                        a merge where the array positions of all keys in `X` are preserved
                        but the values\n    are overwritten by values in `Y` when
                        the key sets of `X` and `Y` intersect. Elements in `Y` with\n
                        \   non-intersecting keys are appended, retaining their partial
                        order.\nRequired."
                      type: string
                    message:
                      description: |-
                        Message represents the message displayed when validation fails. The message is required if the Expression contains
                        line breaks. The message must not contain line breaks.
                        If unset, the message is "failed rule: {Rule}".
                        e.g. "must be a URL with the host matching spec.host"
                        If the Expression contains line breaks. Message is required.
                        The message must not contain line breaks.
                        If unset, the message is "failed Expression: {Expression}".
                      type: string
                    messageExpression:
                      description: |-
                        messageExpression declares a CEL expression that evaluates to the validation failure message that is returned when this rule fails.
                        Since messageExpression is used as a failure message, it must evaluate to a string.
                        If both message and messageExpression are present on a validation, then messageExpression will be used if validation fails.
                        If messageExpression results in a runtime error, the runtime error is logged, and the validation failure message is produced
                        as if the messageExpression field were unset. If messageExpression evaluates to an empty string, a string with only spaces, or a string
                        that contains line breaks, then the validation failure message will also be produced as if the messageExpression field were unset, and
                        the fact that messageExpression produced an empty string/string with only spaces/string with line breaks will be logged.
                        messageExpression has access to all the same variables as the `expression` except for 'authorizer' and 'authorizer.requestResource'.
                        Example:
                        "object.x must be less than max ("+string(params.max)+")"
                      type: string
                    reason:
                      description: |-
                        Reason represents a machine-readable description of why this validation failed.
                        If this is the first validation in the list to fail, this reason, as well as the
                        corresponding HTTP response code, are used in the
                        HTTP response to the client.
                        The currently supported reasons are: "Unauthorized", "Forbidden", "Invalid", "RequestEntityTooLarge".
                        If not set, StatusReasonInvalid is used in the response to the client.
                      type: string
                  required:
                  - expression
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              variables:
                description: |-
                  Variables contain definitions of variables that can be used in composition of other expressions.
                  Each variable is defined as a named CEL expression.
                  The variables defined here will be available under `variables` in other expressions of the policy
                  except MatchConditions because MatchConditions are evaluated before the rest of the policy.

                  The expression of a variable can refer to other variables defined earlier in the list but not those after.
                  Thus, Variables must be sorted by the order of first appearance and acyclic.
                items:
                  description: Variable is the definition of a variable that is used
                    for composition. A variable is defined as a named expression.
                  properties:
                    expression:
                      description: |-
                        Expression is the expression that will be evaluated as the value of the variable.
                        The CEL expression has access to the same identifiers as the CEL expressions in Validation.
                      type: string
                    name:
                      description: |-
                        Name is the name of the variable. The name must be a valid CEL identifier and unique among all variables.
                        The variable can be accessed in other expressions through `variables`
                        For example, if name is "foo", the variable will be available as `variables.foo`
                      type: string
                  required:
                  - expression
                  - name
                  type: object
                  x-kubernetes-map-type: atomic
                type: array
                x-kubernetes-list-map-keys:
                - name
                x-kubernetes-list-type: map
              webhookConfiguration:
                description: WebhookConfiguration defines the configuration for the
                  webhook.
                properties:
                  timeoutSeconds:
                    description: |-
                      TimeoutSeconds specifies the maximum time in seconds allowed to apply this policy.
                      After the configured time expires, the admission request may fail, or may simply ignore the policy results,
                      based on the failure policy. The default timeout is 10s, the value must be between 1 and 30 seconds.
                    format: int32
                    type: integer
                type: object
            type: object
          status:
            description: Status contains policy runtime data.
            properties:
              autogen:
                properties:
                  configs:
                    additionalProperties:
                      properties:
                        spec:
                          description: ValidatingPolicySpec is the specification of
                            the desired behavior of the ValidatingPolicy.
                          properties:
                            auditAnnotations:
                              description: |-
                                auditAnnotations contains CEL expressions which are used to produce audit
                                annotations for the audit event of the API request.
                                validations and auditAnnotations may not both be empty; a least one of validations or auditAnnotations is
                                required.
                              items:
                                description: AuditAnnotation describes how to produce
                                  an audit annotation for an API request.
                                properties:
                                  key:
                                    description: |-
                                      key specifies the audit annotation key. The audit annotation keys of
                                      a ValidatingAdmissionPolicy must be unique. The key must be a qualified
                                      name ([A-Za-z0-9][-A-Za-z0-9_.]*) no more than 63 bytes in length.

                                      The key is combined with the resource name of the
                                      ValidatingAdmissionPolicy to construct an audit annotation key:
                                      "{ValidatingAdmissionPolicy name}/{key}".

                                      If an admission webhook uses the same resource name as this ValidatingAdmissionPolicy
                                      and the same audit annotation key, the annotation key will be identical.
                                      In this case, the first annotation written with the key will be included
                                      in the audit event and all subsequent annotations with the same key
                                      will be discarded.

                                      Required.
                                    type: string
                                  valueExpression:
                                    description: |-
                                      valueExpression represents the expression which is evaluated by CEL to
                                      produce an audit annotation value. The expression must evaluate to either
                                      a string or null value. If the expression evaluates to a string, the
                                      audit annotation is included with the string value. If the expression
                                      evaluates to null or empty string the audit annotation will be omitted.
                                      The valueExpression may be no longer than 5kb in length.
                                      If the result of the valueExpression is more than 10kb in length, it
                                      will be truncated to 10kb.

                                      If multiple ValidatingAdmissionPolicyBinding resources match an
                                      API request, then the valueExpression will be evaluated for
                                      each binding. All unique values produced by the valueExpressions
                                      will be joined together in a comma-separated list.

                                      Required.
                                    type: string
                                required:
                                - key
                                - valueExpression
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            autogen:
                              description: AutogenConfiguration defines the configuration
                                for the generation controller.
                              properties:
                                podControllers:
                                  description: PodControllers specifies whether to
                                    generate a pod controllers rules.
                                  properties:
                                    controllers:
                                      items:
                                        type: string
                                      type: array
                                  type: object
                                validatingAdmissionPolicy:
                                  description: ValidatingAdmissionPolicy specifies
                                    whether to generate a Kubernetes ValidatingAdmissionPolicy.
                                  properties:
                                    enabled:
                                      description: |-
                                        Enabled specifies whether to generate a Kubernetes ValidatingAdmissionPolicy.
                                        Optional. Defaults to "false" if not specified.
                                      type: boolean
                                  type: object
                              type: object
                            evaluation:
                              description: EvaluationConfiguration defines the configuration
                                for the policy evaluation.
                              properties:
                                admission:
                                  description: Admission controls policy evaluation
                                    during admission.
                                  properties:
                                    enabled:
                                      default: true
                                      description: |-
                                        Enabled controls if rules are applied during admission.
                                        Optional. Default value is "true".
                                      type: boolean
                                  type: object
                                background:
                                  description: Background  controls policy evaluation
                                    during background scan.
                                  properties:
                                    enabled:
                                      default: true
                                      description: |-
                                        Enabled controls if rules are applied to existing resources during a background scan.
                                        Optional. Default value is "true". The value must be set to "false" if the policy rule
                                        uses variables that are only available in the admission review request (e.g. user name).
                                      type: boolean
                                  type: object
                                mode:
                                  description: |-
                                    Mode is the mode of policy evaluation.
                                    Allowed values are "Kubernetes" or "JSON".
                                    Optional. Default value is "Kubernetes".
                                  type: string
                              type: object
                            failurePolicy:
                              description: |-
                                failurePolicy defines how to handle failures for the admission policy. Failures can
                                occur from CEL expression parse errors, type check errors, runtime errors and invalid
                                or mis-configured policy definitions or bindings.

                                failurePolicy does not define how validations that evaluate to false are handled.

                                When failurePolicy is set to Fail, the validationActions field define how failures are enforced.

                                Allowed values are Ignore or Fail. Defaults to Fail.
                              enum:
                              - Ignore
                              - Fail
                              type: string
                            matchConditions:
                              description: |-
                                MatchConditions is a list of conditions that must be met for a request to be validated.
                                Match conditions filter requests that have already been matched by the rules,
                                namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.
                                There are a maximum of 64 match conditions allowed.

                                If a parameter object is provided, it can be accessed via the `params` handle in the same
                                manner as validation expressions.

                                The exact matching logic is (in order):
                                  1. If ANY matchCondition evaluates to FALSE, the policy is skipped.
                                  2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.
                                  3. If any matchCondition evaluates to an error (but none are FALSE):
                                     - If failurePolicy=Fail, reject the request
                                     - If failurePolicy=Ignore, the policy is skipped
                              items:
                                description: MatchCondition represents a condition
                                  which must by fulfilled for a request to be sent
                                  to a webhook.
                                properties:
                                  expression:
                                    description: |-
                                      Expression represents the expression which will be evaluated by CEL. Must evaluate to bool.
                                      CEL expressions have access to the contents of the AdmissionRequest and Authorizer, organized into CEL variables:

                                      'object' - The object from the incoming request. The value is null for DELETE requests.
                                      'oldObject' - The existing object. The value is null for CREATE requests.
                                      'request' - Attributes of the admission request(/pkg/apis/admission/types.go#AdmissionRequest).
                                      'authorizer' - A CEL Authorizer. May be used to perform authorization checks for the principal (user or service account) of the request.
                                        See https://pkg.go.dev/k8s.io/apiserver/pkg/cel/library#Authz
                                      'authorizer.requestResource' - A CEL ResourceCheck constructed from the 'authorizer' and configured with the
                                        request resource.
                                      Documentation on CEL: https://kubernetes.io/docs/reference/using-api/cel/

                                      Required.
                                    type: string
                                  name:
                                    description: |-
                                      Name is an identifier for this match condition, used for strategic merging of MatchConditions,
                                      as well as providing an identifier for logging purposes. A good name should be descriptive of
                                      the associated expression.
                                      Name must be a qualified name consisting of alphanumeric characters, '-', '_' or '.', and
                                      must start and end with an alphanumeric character (e.g. 'MyName',  or 'my.name',  or
                                      '123-abc', regex used for validation is '([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9]') with an
                                      optional DNS subdomain prefix and '/' (e.g. 'example.com/MyName')

                                      Required.
                                    type: string
                                required:
                                - expression
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            matchConstraints:
                              description: |-
                                MatchConstraints specifies what resources this policy is designed to validate.
                                The AdmissionPolicy cares about a request if it matches _all_ Constraints.
                                Required.
                              properties:
                                excludeResourceRules:
                                  description: |-
                                    ExcludeResourceRules describes what operations on what resources/subresources the ValidatingAdmissionPolicy should not care about.
                                    The exclude rules take precedence over include rules (if a resource matches both, it is excluded)
                                  items:
                                    description: NamedRuleWithOperations is a tuple
                                      of Operations and Resources with ResourceNames.
                                    properties:
                                      apiGroups:
                                        description: |-
                                          APIGroups is the API groups the resources belong to. '*' is all groups.
                                          If '*' is present, the length of the slice must be one.
                                          Required.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      apiVersions:
                                        description: |-
                                          APIVersions is the API versions the resources belong to. '*' is all versions.
                                          If '*' is present, the length of the slice must be one.
                                          Required.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      operations:
                                        description: |-
                                          Operations is the operations the admission hook cares about - CREATE, UPDATE, DELETE, CONNECT or *
                                          for all of those operations and any future admission operations that are added.
                                          If '*' is present, the length of the slice must be one.
                                          Required.
                                        items:
                                          description: OperationType specifies an
                                            operation for a request.
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      resourceNames:
                                        description: ResourceNames is an optional
                                          white list of names that the rule applies
                                          to.  An empty set means that everything
                                          is allowed.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      resources:
                                        description: |-
                                          Resources is a list of resources this rule applies to.

                                          For example:
                                          'pods' means pods.
                                          'pods/log' means the log subresource of pods.
                                          '*' means all resources, but not subresources.
                                          'pods/*' means all subresources of pods.
                                          '*/scale' means all scale subresources.
                                          '*/*' means all resources and their subresources.

                                          If wildcard is present, the validation rule will ensure resources do not
                                          overlap with each other.

                                          Depending on the enclosing object, subresources might not be allowed.
                                          Required.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      scope:
                                        description: |-
                                          scope specifies the scope of this rule.
                                          Valid values are "Cluster", "Namespaced", and "*"
                                          "Cluster" means that only cluster-scoped resources will match this rule.
                                          Namespace API objects are cluster-scoped.
                                          "Namespaced" means that only namespaced resources will match this rule.
                                          "*" means that there are no scope restrictions.
                                          Subresources match the scope of their parent resource.
                                          Default is "*".
                                        type: string
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchPolicy:
                                  description: |-
                                    matchPolicy defines how the "MatchResources" list is used to match incoming requests.
                                    Allowed values are "Exact" or "Equivalent".

                                    - Exact: match a request only if it exactly matches a specified rule.
                                    For example, if deployments can be modified via apps/v1, apps/v1beta1, and extensions/v1beta1,
                                    but "rules" only included `apiGroups:["apps"], apiVersions:["v1"], resources: ["deployments"]`,
                                    a request to apps/v1beta1 or extensions/v1beta1 would not be sent to the ValidatingAdmissionPolicy.

                                    - Equivalent: match a request if modifies a resource listed in rules, even via another API group or version.
                                    For example, if deployments can be modified via apps/v1, apps/v1beta1, and extensions/v1beta1,
                                    and "rules" only included `apiGroups:["apps"], apiVersions:["v1"], resources: ["deployments"]`,
                                    a request to apps/v1beta1 or extensions/v1beta1 would be converted to apps/v1 and sent to the ValidatingAdmissionPolicy.

                                    Defaults to "Equivalent"
                                  type: string
                                namespaceSelector:
                                  description: |-
                                    NamespaceSelector decides whether to run the admission control policy on an object based
                                    on whether the namespace for that object matches the selector. If the
                                    object itself is a namespace, the matching is performed on
                                    object.metadata.labels. If the object is another cluster scoped resource,
                                    it never skips the policy.

                                    For example, to run the webhook on any objects whose namespace is not
                                    associated with "runlevel" of "0" or "1";  you will set the selector as
                                    follows:
                                    "namespaceSelector": {
                                      "matchExpressions": [
                                        {
                                          "key": "runlevel",
                                          "operator": "NotIn",
                                          "values": [
                                            "0",
                                            "1"
                                          ]
                                        }
                                      ]
                                    }

                                    If instead you want to only run the policy on any objects whose
                                    namespace is associated with the "environment" of "prod" or "staging";
                                    you will set the selector as follows:
                                    "namespaceSelector": {
                                      "matchExpressions": [
                                        {
                                          "key": "environment",
                                          "operator": "In",
                                          "values": [
                                            "prod",
                                            "staging"
                                          ]
                                        }
                                      ]
                                    }

                                    See
                                    https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
                                    for more examples of label selectors.

                                    Default to the empty LabelSelector, which matches everything.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                objectSelector:
                                  description: |-
                                    ObjectSelector decides whether to run the validation based on if the
                                    object has matching labels. objectSelector is evaluated against both
                                    the oldObject and newObject that would be sent to the cel validation, and
                                    is considered to match if either object matches the selector. A null
                                    object (oldObject in the case of create, or newObject in the case of
                                    delete) or an object that cannot have labels (like a
                                    DeploymentRollback or a PodProxyOptions object) is not considered to
                                    match.
                                    Use the object selector only if the webhook is opt-in, because end
                                    users may skip the admission webhook by setting the labels.
                                    Default to the empty LabelSelector, which matches everything.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                resourceRules:
                                  description: |-
                                    ResourceRules describes what operations on what resources/subresources the ValidatingAdmissionPolicy matches.
                                    The policy cares about an operation if it matches _any_ Rule.
                                  items:
                                    description: NamedRuleWithOperations is a tuple
                                      of Operations and Resources with ResourceNames.
                                    properties:
                                      apiGroups:
                                        description: |-
                                          APIGroups is the API groups the resources belong to. '*' is all groups.
                                          If '*' is present, the length of the slice must be one.
                                          Required.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      apiVersions:
                                        description: |-
                                          APIVersions is the API versions the resources belong to. '*' is all versions.
                                          If '*' is present, the length of the slice must be one.
                                          Required.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      operations:
                                        description: |-
                                          Operations is the operations the admission hook cares about - CREATE, UPDATE, DELETE, CONNECT or *
                                          for all of those operations and any future admission operations that are added.
                                          If '*' is present, the length of the slice must be one.
                                          Required.
                                        items:
                                          description: OperationType specifies an
                                            operation for a request.
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      resourceNames:
                                        description: ResourceNames is an optional
                                          white list of names that the rule applies
                                          to.  An empty set means that everything
                                          is allowed.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      resources:
                                        description: |-
                                          Resources is a list of resources this rule applies to.

                                          For example:
                                          'pods' means pods.
                                          'pods/log' means the log subresource of pods.
                                          '*' means all resources, but not subresources.
                                          'pods/*' means all subresources of pods.
                                          '*/scale' means all scale subresources.
                                          '*/*' means all resources and their subresources.

                                          If wildcard is present, the validation rule will ensure resources do not
                                          overlap with each other.

                                          Depending on the enclosing object, subresources might not be allowed.
                                          Required.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      scope:
                                        description: |-
                                          scope specifies the scope of this rule.
                                          Valid values are "Cluster", "Namespaced", and "*"
                                          "Cluster" means that only cluster-scoped resources will match this rule.
                                          Namespace API objects are cluster-scoped.
                                          "Namespaced" means that only namespaced resources will match this rule.
                                          "*" means that there are no scope restrictions.
                                          Subresources match the scope of their parent resource.
                                          Default is "*".
                                        type: string
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                              x-kubernetes-map-type: atomic
                            validationActions:
                              description: |-
                                ValidationAction specifies the action to be taken when the matched resource violates the policy.
                                Required.
                              items:
                                description: ValidationAction specifies a policy enforcement
                                  action.
                                enum:
                                - Deny
                                - Audit
                                - Warn
                                type: string
                              type: array
                              x-kubernetes-list-type: set
                            validations:
                              description: |-
                                Validations contain CEL expressions which is used to apply the validation.
                                Validations and AuditAnnotations may not both be empty; a minimum of one Validations or AuditAnnotations is
                                required.
                              items:
                                description: Validation specifies the CEL expression
                                  which is used to apply the validation.
                                properties:
                                  expression:
                                    description: "Expression represents the expression
                                      which will be evaluated by CEL.\nref: https://github.com/google/cel-spec\nCEL
                                      expressions have access to the contents of the
                                      API request/response, organized into CEL variables
                                      as well as some other useful variables:\n\n-
                                      'object' - The object from the incoming request.
                                      The value is null for DELETE requests.\n- 'oldObject'
                                      - The existing object. The value is null for
                                      CREATE requests.\n- 'request' - Attributes of
                                      the API request([ref](/pkg/apis/admission/types.go#AdmissionRequest)).\n-
                                      'params' - Parameter resource referred to by
                                      the policy binding being evaluated. Only populated
                                      if the policy has a ParamKind.\n- 'namespaceObject'
                                      - The namespace object that the incoming object
                                      belongs to. The value is null for cluster-scoped
                                      resources.\n- 'variables' - Map of composited
                                      variables, from its name to its lazily evaluated
                                      value.\n  For example, a variable named 'foo'
                                      can be accessed as 'variables.foo'.\n- 'authorizer'
                                      - A CEL Authorizer. May be used to perform authorization
                                      checks for the principal (user or service account)
                                      of the request.\n  See https://pkg.go.dev/k8s.io/apiserver/pkg/cel/library#Authz\n-
                                      'authorizer.requestResource' - A CEL ResourceCheck
                                      constructed from the 'authorizer' and configured
                                      with the\n  request resource.\n\nThe `apiVersion`,
                                      `kind`, `metadata.name` and `metadata.generateName`
                                      are always accessible from the root of the\nobject.
                                      No other metadata properties are accessible.\n\nOnly
                                      property names of the form `[a-zA-Z_.-/][a-zA-Z0-9_.-/]*`
                                      are accessible.\nAccessible property names are
                                      escaped according to the following rules when
                                      accessed in the expression:\n- '__' escapes
                                      to '__underscores__'\n- '.' escapes to '__dot__'\n-
                                      '-' escapes to '__dash__'\n- '/' escapes to
                                      '__slash__'\n- Property names that exactly match
                                      a CEL RESERVED keyword escape to '__{keyword}__'.
                                      The keywords are:\n\t  \"true\", \"false\",
                                      \"null\", \"in\", \"as\", \"break\", \"const\",
                                      \"continue\", \"else\", \"for\", \"function\",
                                      \"if\",\n\t  \"import\", \"let\", \"loop\",
                                      \"package\", \"namespace\", \"return\".\nExamples:\n
                                      \ - Expression accessing a property named \"namespace\":
                                      {\"Expression\": \"object.__namespace__ > 0\"}\n
                                      \ - Expression accessing a property named \"x-prop\":
                                      {\"Expression\": \"object.x__dash__prop > 0\"}\n
                                      \ - Expression accessing a property named \"redact__d\":
                                      {\"Expression\": \"object.redact__underscores__d
                                      > 0\"}\n\nEquality on arrays with list type
                                      of 'set' or 'map' ignores element order, i.e.
                                      [1, 2] == [2, 1].\nConcatenation on arrays with
                                      x-kubernetes-list-type use the semantics of
                                      the list type:\n  - 'set': `X + Y` performs
                                      a union where the array positions of all elements
                                      in `X` are preserved and\n    non-intersecting
                                      elements in `Y` are appended, retaining their
                                      partial order.\n  - 'map': `X + Y` performs
                                      a merge where the array positions of all keys
                                      in `X` are preserved but the values\n    are
                                      overwritten by values in `Y` when the key sets
                                      of `X` and `Y` intersect. Elements in `Y` with\n
                                      \   non-intersecting keys are appended, retaining
                                      their partial order.\nRequired."
                                    type: string
                                  message:
                                    description: |-
                                      Message represents the message displayed when validation fails. The message is required if the Expression contains
                                      line breaks. The message must not contain line breaks.
                                      If unset, the message is "failed rule: {Rule}".
                                      e.g. "must be a URL with the host matching spec.host"
                                      If the Expression contains line breaks. Message is required.
                                      The message must not contain line breaks.
                                      If unset, the message is "failed Expression: {Expression}".
                                    type: string
                                  messageExpression:
                                    description: |-
                                      messageExpression declares a CEL expression that evaluates to the validation failure message that is returned when this rule fails.
                                      Since messageExpression is used as a failure message, it must evaluate to a string.
                                      If both message and messageExpression are present on a validation, then messageExpression will be used if validation fails.
                                      If messageExpression results in a runtime error, the runtime error is logged, and the validation failure message is produced
                                      as if the messageExpression field were unset. If messageExpression evaluates to an empty string, a string with only spaces, or a string
                                      that contains line breaks, then the validation failure message will also be produced as if the messageExpression field were unset, and
                                      the fact that messageExpression produced an empty string/string with only spaces/string with line breaks will be logged.
                                      messageExpression has access to all the same variables as the `expression` except for 'authorizer' and 'authorizer.requestResource'.
                                      Example:
                                      "object.x must be less than max ("+string(params.max)+")"
                                    type: string
                                  reason:
                                    description: |-
                                      Reason represents a machine-readable description of why this validation failed.
                                      If this is the first validation in the list to fail, this reason, as well as the
                                      corresponding HTTP response code, are used in the
                                      HTTP response to the client.
                                      The currently supported reasons are: "Unauthorized", "Forbidden", "Invalid", "RequestEntityTooLarge".
                                      If not set, StatusReasonInvalid is used in the response to the client.
                                    type: string
                                required:
                                - expression
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            variables:
                              description: |-
                                Variables contain definitions of variables that can be used in composition of other expressions.
                                Each variable is defined as a named CEL expression.
                                The variables defined here will be available under `variables` in other expressions of the policy
                                except MatchConditions because MatchConditions are evaluated before the rest of the policy.

                                The expression of a variable can refer to other variables defined earlier in the list but not those after.
                                Thus, Variables must be sorted by the order of first appearance and acyclic.
                              items:
                                description: Variable is the definition of a variable
                                  that is used for composition. A variable is defined
                                  as a named expression.
                                properties:
                                  expression:
                                    description: |-
                                      Expression is the expression that will be evaluated as the value of the variable.
                                      The CEL expression has access to the same identifiers as the CEL expressions in Validation.
                                    type: string
                                  name:
                                    description: |-
                                      Name is the name of the variable. The name must be a valid CEL identifier and unique among all variables.
                                      The variable can be accessed in other expressions through `variables`
                                      For example, if name is "foo", the variable will be available as `variables.foo`
                                    type: string
                                required:
                                - expression
                                - name
                                type: object
                                x-kubernetes-map-type: atomic
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            webhookConfiguration:
                              description: WebhookConfiguration defines the configuration
                                for the webhook.
                              properties:
                                timeoutSeconds:
                                  description: |-
                                    TimeoutSeconds specifies the maximum time in seconds allowed to apply this policy.
                                    After the configured time expires, the admission request may fail, or may simply ignore the policy results,
                                    based on the failure policy. The default timeout is 10s, the value must be between 1 and 30 seconds.
                                  format: int32
                                  type: integer
                              type: object
                          type: object
                        targets:
                          items:
                            properties:
                              group:
                                type: string
                              kind:
                                type: string
                              resource:
                                type: string
                              version:
                                type: string
                            required:
                            - kind
                            - resource
                            - version
                            type: object
                          type: array
                      required:
                      - spec
                      - targets
                      type: object
                    type: object
                type: object
              conditionStatus:
                description: ConditionStatus is the shared status across all policy
                  types
                properties:
                  conditions:
                    items:
                      description: Condition contains details for one aspect of the
                        current state of this API Resource.
                      properties:
                        lastTransitionTime:
                          description: |-
                            lastTransitionTime is the last time the condition transitioned from one status to another.
                            This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                          format: date-time
                          type: string
                        message:
                          description: |-
                            message is a human readable message indicating details about the transition.
                            This may be an empty string.
                          maxLength: 32768
                          type: string
                        observedGeneration:
                          description: |-
                            observedGeneration represents the .metadata.generation that the condition was set based upon.
                            For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                            with respect to the current state of the instance.
                          format: int64
                          minimum: 0
                          type: integer
                        reason:
                          description: |-
                            reason contains a programmatic identifier indicating the reason for the condition's last transition.
                            Producers of specific condition types may define expected values and meanings for this field,
                            and whether the values are considered a guaranteed API.
                            The value should be a CamelCase string.
                            This field may not be empty.
                          maxLength: 1024
                          minLength: 1
                          pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                          type: string
                        status:
                          description: status of the condition, one of True, False,
                            Unknown.
                          enum:
                          - "True"
                          - "False"
                          - Unknown
                          type: string
                        type:
                          description: type of condition in CamelCase or in foo.example.com/CamelCase.
                          maxLength: 316
                          pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                          type: string
                      required:
                      - lastTransitionTime
                      - message
                      - reason
                      - status
                      - type
                      type: object
                    type: array
                  message:
                    description: |-
                      Message is a human readable message indicating details about the generation of ValidatingAdmissionPolicy/MutatingAdmissionPolicy
                      It is an empty string when ValidatingAdmissionPolicy/MutatingAdmissionPolicy is successfully generated.
                    type: string
                  ready:
                    description: |-
                      The ready of a policy is a high-level summary of where the policy is in its lifecycle.
                      The conditions array, the reason and message fields contain more detail about the policy's status.
                    type: boolean
                type: object
              generated:
                description: Generated indicates whether a ValidatingAdmissionPolicy/MutatingAdmissionPolicy
                  is generated from the policy or not
                type: boolean
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
{{- end }}

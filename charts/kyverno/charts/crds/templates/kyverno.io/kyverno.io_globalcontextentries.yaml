{{- if .Values.groups.kyverno.globalcontextentries }}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  labels:
    {{- include "kyverno.crds.labels" . | nindent 4 }}
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    controller-gen.kubebuilder.io/version: v0.17.3
  name: globalcontextentries.kyverno.io
spec:
  group: kyverno.io
  names:
    categories:
    - kyverno
    kind: GlobalContextEntry
    listKind: GlobalContextEntryList
    plural: globalcontextentries
    shortNames:
    - gctxentry
    singular: globalcontextentry
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    - jsonPath: .spec.apiCall.refreshInterval
      name: REFRESH INTERVAL
      type: string
    - jsonPath: .status.lastRefreshTime
      name: LAST REFRESH
      type: date
    name: v2alpha1
    schema:
      openAPIV3Schema:
        description: GlobalContextEntry declares resources to be cached.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec declares policy exception behaviors.
            oneOf:
            - required:
              - kubernetesResource
            - required:
              - apiCall
            properties:
              apiCall:
                description: |-
                  Stores results from an API call which will be cached.
                  Mutually exclusive with KubernetesResource.
                  This can be used to make calls to external (non-Kubernetes API server) services.
                  It can also be used to make calls to the Kubernetes API server in such cases:
                  1. A POST is needed to create a resource.
                  2. Finer-grained control is needed. Example: To restrict the number of resources cached.
                properties:
                  data:
                    description: |-
                      The data object specifies the POST data sent to the server.
                      Only applicable when the method field is set to POST.
                    items:
                      description: RequestData contains the HTTP POST data
                      properties:
                        key:
                          description: Key is a unique identifier for the data value
                          type: string
                        value:
                          description: Value is the data value
                          x-kubernetes-preserve-unknown-fields: true
                      required:
                      - key
                      - value
                      type: object
                    type: array
                  method:
                    default: GET
                    description: Method is the HTTP request type (GET or POST). Defaults
                      to GET.
                    enum:
                    - GET
                    - POST
                    type: string
                  refreshInterval:
                    default: 10m
                    description: |-
                      RefreshInterval defines the interval in duration at which to poll the APICall.
                      The duration is a sequence of decimal numbers, each with optional fraction and a unit suffix,
                      such as "300ms", "1.5h" or "2h45m". Valid time units are "ns", "us" (or "µs"), "ms", "s", "m", "h".
                    format: duration
                    type: string
                  retryLimit:
                    default: 3
                    description: RetryLimit defines the number of times the APICall
                      should be retried in case of failure.
                    minimum: 1
                    type: integer
                  service:
                    description: |-
                      Service is an API call to a JSON web service.
                      This is used for non-Kubernetes API server calls.
                      It's mutually exclusive with the URLPath field.
                    properties:
                      caBundle:
                        description: |-
                          CABundle is a PEM encoded CA bundle which will be used to validate
                          the server certificate.
                        type: string
                      headers:
                        description: Headers is a list of optional HTTP headers to
                          be included in the request.
                        items:
                          properties:
                            key:
                              description: Key is the header key
                              type: string
                            value:
                              description: Value is the header value
                              type: string
                          required:
                          - key
                          - value
                          type: object
                        type: array
                      url:
                        description: |-
                          URL is the JSON web service URL. A typical form is
                          `https://{service}.{namespace}:{port}/{path}`.
                        type: string
                    required:
                    - url
                    type: object
                  urlPath:
                    description: |-
                      URLPath is the URL path to be used in the HTTP GET or POST request to the
                      Kubernetes API server (e.g. "/api/v1/namespaces" or  "/apis/apps/v1/deployments").
                      The format required is the same format used by the `kubectl get --raw` command.
                      See https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls
                      for details.
                      It's mutually exclusive with the Service field.
                    type: string
                type: object
              kubernetesResource:
                description: |-
                  Stores a list of Kubernetes resources which will be cached.
                  Mutually exclusive with APICall.
                properties:
                  group:
                    description: Group defines the group of the resource.
                    type: string
                  namespace:
                    description: |-
                      Namespace defines the namespace of the resource. Leave empty for cluster scoped resources.
                      If left empty for namespaced resources, all resources from all namespaces will be cached.
                    type: string
                  resource:
                    description: |-
                      Resource defines the type of the resource.
                      Requires the pluralized form of the resource kind in lowercase. (Ex., "deployments")
                    type: string
                  version:
                    description: Version defines the version of the resource.
                    type: string
                required:
                - resource
                - version
                type: object
              projections:
                description: Projections defines the list of JMESPath expressions
                  to extract values from the cached resource.
                items:
                  properties:
                    jmesPath:
                      description: JMESPath is the JMESPath expression to extract
                        the value from the cached resource.
                      type: string
                    name:
                      description: Name is the name to use for the extracted value
                        in the context.
                      type: string
                  required:
                  - jmesPath
                  - name
                  type: object
                type: array
            type: object
          status:
            description: Status contains globalcontextentry runtime data.
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              lastRefreshTime:
                description: Indicates the time when the globalcontextentry was last
                  refreshed successfully for the API Call
                format: date-time
                type: string
              ready:
                description: Deprecated in favor of Conditions
                type: boolean
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
{{- end }}

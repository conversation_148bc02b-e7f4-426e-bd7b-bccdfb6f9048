{{- if .Values.groups.kyverno.updaterequests }}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  labels:
    {{- include "kyverno.crds.labels" . | nindent 4 }}
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    controller-gen.kubebuilder.io/version: v0.17.3
  name: updaterequests.kyverno.io
spec:
  group: kyverno.io
  names:
    categories:
    - kyverno
    kind: UpdateRequest
    listKind: UpdateRequestList
    plural: updaterequests
    shortNames:
    - ur
    singular: updaterequest
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.policy
      name: Policy
      type: string
    - jsonPath: .spec.rule
      name: Rule
      type: string
    - jsonPath: .spec.requestType
      name: RuleType
      type: string
    - jsonPath: .spec.resource.kind
      name: ResourceKind
      type: string
    - jsonPath: .spec.resource.name
      name: ResourceName
      type: string
    - jsonPath: .spec.resource.namespace
      name: ResourceNamespace
      type: string
    - jsonPath: .status.state
      name: status
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    deprecated: true
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: UpdateRequest is a request to process mutate and generate rules
          in background.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ResourceSpec is the information to identify the trigger resource.
            properties:
              context:
                description: Context ...
                properties:
                  admissionRequestInfo:
                    description: AdmissionRequestInfoObject stores the admission request
                      and operation details
                    properties:
                      admissionRequest:
                        description: AdmissionRequest describes the admission.Attributes
                          for the admission request.
                        properties:
                          dryRun:
                            description: |-
                              DryRun indicates that modifications will definitely not be persisted for this request.
                              Defaults to false.
                            type: boolean
                          kind:
                            description: Kind is the fully-qualified type of object
                              being submitted (for example, v1.Pod or autoscaling.v1.Scale)
                            properties:
                              group:
                                type: string
                              kind:
                                type: string
                              version:
                                type: string
                            required:
                            - group
                            - kind
                            - version
                            type: object
                          name:
                            description: |-
                              Name is the name of the object as presented in the request.  On a CREATE operation, the client may omit name and
                              rely on the server to generate the name.  If that is the case, this field will contain an empty string.
                            type: string
                          namespace:
                            description: Namespace is the namespace associated with
                              the request (if any).
                            type: string
                          object:
                            description: Object is the object from the incoming request.
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                          oldObject:
                            description: OldObject is the existing object. Only populated
                              for DELETE and UPDATE requests.
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                          operation:
                            description: |-
                              Operation is the operation being performed. This may be different than the operation
                              requested. e.g. a patch can result in either a CREATE or UPDATE Operation.
                            type: string
                          options:
                            description: |-
                              Options is the operation option structure of the operation being performed.
                              e.g. `meta.k8s.io/v1.DeleteOptions` or `meta.k8s.io/v1.CreateOptions`. This may be
                              different than the options the caller provided. e.g. for a patch request the performed
                              Operation might be a CREATE, in which case the Options will a
                              `meta.k8s.io/v1.CreateOptions` even though the caller provided `meta.k8s.io/v1.PatchOptions`.
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                          requestKind:
                            description: |-
                              RequestKind is the fully-qualified type of the original API request (for example, v1.Pod or autoscaling.v1.Scale).
                              If this is specified and differs from the value in "kind", an equivalent match and conversion was performed.

                              For example, if deployments can be modified via apps/v1 and apps/v1beta1, and a webhook registered a rule of
                              `apiGroups:["apps"], apiVersions:["v1"], resources: ["deployments"]` and `matchPolicy: Equivalent`,
                              an API request to apps/v1beta1 deployments would be converted and sent to the webhook
                              with `kind: {group:"apps", version:"v1", kind:"Deployment"}` (matching the rule the webhook registered for),
                              and `requestKind: {group:"apps", version:"v1beta1", kind:"Deployment"}` (indicating the kind of the original API request).

                              See documentation for the "matchPolicy" field in the webhook configuration type for more details.
                            properties:
                              group:
                                type: string
                              kind:
                                type: string
                              version:
                                type: string
                            required:
                            - group
                            - kind
                            - version
                            type: object
                          requestResource:
                            description: |-
                              RequestResource is the fully-qualified resource of the original API request (for example, v1.pods).
                              If this is specified and differs from the value in "resource", an equivalent match and conversion was performed.

                              For example, if deployments can be modified via apps/v1 and apps/v1beta1, and a webhook registered a rule of
                              `apiGroups:["apps"], apiVersions:["v1"], resources: ["deployments"]` and `matchPolicy: Equivalent`,
                              an API request to apps/v1beta1 deployments would be converted and sent to the webhook
                              with `resource: {group:"apps", version:"v1", resource:"deployments"}` (matching the resource the webhook registered for),
                              and `requestResource: {group:"apps", version:"v1beta1", resource:"deployments"}` (indicating the resource of the original API request).

                              See documentation for the "matchPolicy" field in the webhook configuration type.
                            properties:
                              group:
                                type: string
                              resource:
                                type: string
                              version:
                                type: string
                            required:
                            - group
                            - resource
                            - version
                            type: object
                          requestSubResource:
                            description: |-
                              RequestSubResource is the name of the subresource of the original API request, if any (for example, "status" or "scale")
                              If this is specified and differs from the value in "subResource", an equivalent match and conversion was performed.
                              See documentation for the "matchPolicy" field in the webhook configuration type.
                            type: string
                          resource:
                            description: Resource is the fully-qualified resource
                              being requested (for example, v1.pods)
                            properties:
                              group:
                                type: string
                              resource:
                                type: string
                              version:
                                type: string
                            required:
                            - group
                            - resource
                            - version
                            type: object
                          subResource:
                            description: SubResource is the subresource being requested,
                              if any (for example, "status" or "scale")
                            type: string
                          uid:
                            description: |-
                              UID is an identifier for the individual request/response. It allows us to distinguish instances of requests which are
                              otherwise identical (parallel requests, requests when earlier requests did not modify etc)
                              The UID is meant to track the round trip (request/response) between the KAS and the WebHook, not the user request.
                              It is suitable for correlating log entries between the webhook and apiserver, for either auditing or debugging.
                            type: string
                          userInfo:
                            description: UserInfo is information about the requesting
                              user
                            properties:
                              extra:
                                additionalProperties:
                                  description: ExtraValue masks the value so protobuf
                                    can generate
                                  items:
                                    type: string
                                  type: array
                                description: Any additional information provided by
                                  the authenticator.
                                type: object
                              groups:
                                description: The names of groups this user is a part
                                  of.
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                              uid:
                                description: |-
                                  A unique value that identifies this user across time. If this user is
                                  deleted and another user by the same name is added, they will have
                                  different UIDs.
                                type: string
                              username:
                                description: The name that uniquely identifies this
                                  user among all active users.
                                type: string
                            type: object
                        required:
                        - kind
                        - operation
                        - resource
                        - uid
                        - userInfo
                        type: object
                      operation:
                        description: Operation is the type of resource operation being
                          checked for admission control
                        type: string
                    type: object
                  userInfo:
                    description: RequestInfo contains permission info carried in an
                      admission request.
                    properties:
                      clusterRoles:
                        description: ClusterRoles is a list of possible clusterRoles
                          send the request.
                        items:
                          type: string
                        nullable: true
                        type: array
                      roles:
                        description: Roles is a list of possible role send the request.
                        items:
                          type: string
                        nullable: true
                        type: array
                      userInfo:
                        description: UserInfo is the userInfo carried in the admission
                          request.
                        properties:
                          extra:
                            additionalProperties:
                              description: ExtraValue masks the value so protobuf
                                can generate
                              items:
                                type: string
                              type: array
                            description: Any additional information provided by the
                              authenticator.
                            type: object
                          groups:
                            description: The names of groups this user is a part of.
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          uid:
                            description: |-
                              A unique value that identifies this user across time. If this user is
                              deleted and another user by the same name is added, they will have
                              different UIDs.
                            type: string
                          username:
                            description: The name that uniquely identifies this user
                              among all active users.
                            type: string
                        type: object
                    type: object
                type: object
              deleteDownstream:
                description: DeleteDownstream represents whether the downstream needs
                  to be deleted.
                type: boolean
              policy:
                description: Specifies the name of the policy.
                type: string
              requestType:
                description: Type represents request type for background processing
                enum:
                - mutate
                - generate
                type: string
              resource:
                description: ResourceSpec is the information to identify the trigger
                  resource.
                properties:
                  apiVersion:
                    description: APIVersion specifies resource apiVersion.
                    type: string
                  kind:
                    description: Kind specifies resource kind.
                    type: string
                  name:
                    description: Name specifies the resource name.
                    type: string
                  namespace:
                    description: Namespace specifies resource namespace.
                    type: string
                  uid:
                    description: UID specifies the resource uid.
                    type: string
                type: object
              rule:
                description: Rule is the associate rule name of the current UR.
                type: string
              synchronize:
                description: |-
                  Synchronize represents the sync behavior of the corresponding rule
                  Optional. Defaults to "false" if not specified.
                type: boolean
            required:
            - context
            - deleteDownstream
            - policy
            - resource
            - rule
            type: object
          status:
            description: Status contains statistics related to update request.
            properties:
              generatedResources:
                description: |-
                  This will track the resources that are updated by the generate Policy.
                  Will be used during clean up resources.
                items:
                  properties:
                    apiVersion:
                      description: APIVersion specifies resource apiVersion.
                      type: string
                    kind:
                      description: Kind specifies resource kind.
                      type: string
                    name:
                      description: Name specifies the resource name.
                      type: string
                    namespace:
                      description: Namespace specifies resource namespace.
                      type: string
                    uid:
                      description: UID specifies the resource uid.
                      type: string
                  type: object
                type: array
              handler:
                description: Deprecated
                type: string
              message:
                description: Specifies request status message.
                type: string
              retryCount:
                type: integer
              state:
                description: State represents state of the update request.
                type: string
            required:
            - state
            type: object
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - additionalPrinterColumns:
    - jsonPath: .spec.policy
      name: Policy
      type: string
    - jsonPath: .spec.requestType
      name: RuleType
      type: string
    - jsonPath: .spec.resource.kind
      name: ResourceKind
      type: string
    - jsonPath: .spec.resource.name
      name: ResourceName
      type: string
    - jsonPath: .spec.resource.namespace
      name: ResourceNamespace
      type: string
    - jsonPath: .status.state
      name: status
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v2
    schema:
      openAPIV3Schema:
        description: UpdateRequest is a request to process mutate and generate rules
          in background.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ResourceSpec is the information to identify the trigger resource.
            properties:
              context:
                description: |-
                  Context represents admission request context.
                  It is used upon admission review only and is shared across rules within the same UR.
                properties:
                  admissionRequestInfo:
                    description: AdmissionRequestInfoObject stores the admission request
                      and operation details
                    properties:
                      admissionRequest:
                        description: AdmissionRequest describes the admission.Attributes
                          for the admission request.
                        properties:
                          dryRun:
                            description: |-
                              DryRun indicates that modifications will definitely not be persisted for this request.
                              Defaults to false.
                            type: boolean
                          kind:
                            description: Kind is the fully-qualified type of object
                              being submitted (for example, v1.Pod or autoscaling.v1.Scale)
                            properties:
                              group:
                                type: string
                              kind:
                                type: string
                              version:
                                type: string
                            required:
                            - group
                            - kind
                            - version
                            type: object
                          name:
                            description: |-
                              Name is the name of the object as presented in the request.  On a CREATE operation, the client may omit name and
                              rely on the server to generate the name.  If that is the case, this field will contain an empty string.
                            type: string
                          namespace:
                            description: Namespace is the namespace associated with
                              the request (if any).
                            type: string
                          object:
                            description: Object is the object from the incoming request.
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                          oldObject:
                            description: OldObject is the existing object. Only populated
                              for DELETE and UPDATE requests.
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                          operation:
                            description: |-
                              Operation is the operation being performed. This may be different than the operation
                              requested. e.g. a patch can result in either a CREATE or UPDATE Operation.
                            type: string
                          options:
                            description: |-
                              Options is the operation option structure of the operation being performed.
                              e.g. `meta.k8s.io/v1.DeleteOptions` or `meta.k8s.io/v1.CreateOptions`. This may be
                              different than the options the caller provided. e.g. for a patch request the performed
                              Operation might be a CREATE, in which case the Options will a
                              `meta.k8s.io/v1.CreateOptions` even though the caller provided `meta.k8s.io/v1.PatchOptions`.
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                          requestKind:
                            description: |-
                              RequestKind is the fully-qualified type of the original API request (for example, v1.Pod or autoscaling.v1.Scale).
                              If this is specified and differs from the value in "kind", an equivalent match and conversion was performed.

                              For example, if deployments can be modified via apps/v1 and apps/v1beta1, and a webhook registered a rule of
                              `apiGroups:["apps"], apiVersions:["v1"], resources: ["deployments"]` and `matchPolicy: Equivalent`,
                              an API request to apps/v1beta1 deployments would be converted and sent to the webhook
                              with `kind: {group:"apps", version:"v1", kind:"Deployment"}` (matching the rule the webhook registered for),
                              and `requestKind: {group:"apps", version:"v1beta1", kind:"Deployment"}` (indicating the kind of the original API request).

                              See documentation for the "matchPolicy" field in the webhook configuration type for more details.
                            properties:
                              group:
                                type: string
                              kind:
                                type: string
                              version:
                                type: string
                            required:
                            - group
                            - kind
                            - version
                            type: object
                          requestResource:
                            description: |-
                              RequestResource is the fully-qualified resource of the original API request (for example, v1.pods).
                              If this is specified and differs from the value in "resource", an equivalent match and conversion was performed.

                              For example, if deployments can be modified via apps/v1 and apps/v1beta1, and a webhook registered a rule of
                              `apiGroups:["apps"], apiVersions:["v1"], resources: ["deployments"]` and `matchPolicy: Equivalent`,
                              an API request to apps/v1beta1 deployments would be converted and sent to the webhook
                              with `resource: {group:"apps", version:"v1", resource:"deployments"}` (matching the resource the webhook registered for),
                              and `requestResource: {group:"apps", version:"v1beta1", resource:"deployments"}` (indicating the resource of the original API request).

                              See documentation for the "matchPolicy" field in the webhook configuration type.
                            properties:
                              group:
                                type: string
                              resource:
                                type: string
                              version:
                                type: string
                            required:
                            - group
                            - resource
                            - version
                            type: object
                          requestSubResource:
                            description: |-
                              RequestSubResource is the name of the subresource of the original API request, if any (for example, "status" or "scale")
                              If this is specified and differs from the value in "subResource", an equivalent match and conversion was performed.
                              See documentation for the "matchPolicy" field in the webhook configuration type.
                            type: string
                          resource:
                            description: Resource is the fully-qualified resource
                              being requested (for example, v1.pods)
                            properties:
                              group:
                                type: string
                              resource:
                                type: string
                              version:
                                type: string
                            required:
                            - group
                            - resource
                            - version
                            type: object
                          subResource:
                            description: SubResource is the subresource being requested,
                              if any (for example, "status" or "scale")
                            type: string
                          uid:
                            description: |-
                              UID is an identifier for the individual request/response. It allows us to distinguish instances of requests which are
                              otherwise identical (parallel requests, requests when earlier requests did not modify etc)
                              The UID is meant to track the round trip (request/response) between the KAS and the WebHook, not the user request.
                              It is suitable for correlating log entries between the webhook and apiserver, for either auditing or debugging.
                            type: string
                          userInfo:
                            description: UserInfo is information about the requesting
                              user
                            properties:
                              extra:
                                additionalProperties:
                                  description: ExtraValue masks the value so protobuf
                                    can generate
                                  items:
                                    type: string
                                  type: array
                                description: Any additional information provided by
                                  the authenticator.
                                type: object
                              groups:
                                description: The names of groups this user is a part
                                  of.
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                              uid:
                                description: |-
                                  A unique value that identifies this user across time. If this user is
                                  deleted and another user by the same name is added, they will have
                                  different UIDs.
                                type: string
                              username:
                                description: The name that uniquely identifies this
                                  user among all active users.
                                type: string
                            type: object
                        required:
                        - kind
                        - operation
                        - resource
                        - uid
                        - userInfo
                        type: object
                      operation:
                        description: Operation is the type of resource operation being
                          checked for admission control
                        type: string
                    type: object
                  userInfo:
                    description: RequestInfo contains permission info carried in an
                      admission request.
                    properties:
                      clusterRoles:
                        description: ClusterRoles is a list of possible clusterRoles
                          send the request.
                        items:
                          type: string
                        nullable: true
                        type: array
                      roles:
                        description: Roles is a list of possible role send the request.
                        items:
                          type: string
                        nullable: true
                        type: array
                      synchronize:
                        description: |-
                          DryRun indicates that modifications will definitely not be persisted for this request.
                          Defaults to false.
                        type: boolean
                      userInfo:
                        description: UserInfo is the userInfo carried in the admission
                          request.
                        properties:
                          extra:
                            additionalProperties:
                              description: ExtraValue masks the value so protobuf
                                can generate
                              items:
                                type: string
                              type: array
                            description: Any additional information provided by the
                              authenticator.
                            type: object
                          groups:
                            description: The names of groups this user is a part of.
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          uid:
                            description: |-
                              A unique value that identifies this user across time. If this user is
                              deleted and another user by the same name is added, they will have
                              different UIDs.
                            type: string
                          username:
                            description: The name that uniquely identifies this user
                              among all active users.
                            type: string
                        type: object
                    type: object
                type: object
              deleteDownstream:
                description: |-
                  DeleteDownstream represents whether the downstream needs to be deleted.
                  Deprecated
                type: boolean
              policy:
                description: Specifies the name of the policy.
                type: string
              requestType:
                description: Type represents request type for background processing
                enum:
                - mutate
                - generate
                - cel-generate
                - cel-mutate
                type: string
              resource:
                description: ResourceSpec is the information to identify the trigger
                  resource.
                properties:
                  apiVersion:
                    description: APIVersion specifies resource apiVersion.
                    type: string
                  kind:
                    description: Kind specifies resource kind.
                    type: string
                  name:
                    description: Name specifies the resource name.
                    type: string
                  namespace:
                    description: Namespace specifies resource namespace.
                    type: string
                  uid:
                    description: UID specifies the resource uid.
                    type: string
                type: object
              rule:
                description: Rule is the associate rule name of the current UR.
                type: string
              ruleContext:
                description: |-
                  RuleContext is the associate context to apply rules.
                  optional
                items:
                  properties:
                    deleteDownstream:
                      description: DeleteDownstream represents whether the downstream
                        needs to be deleted.
                      type: boolean
                    rule:
                      description: Rule is the associate rule name of the current
                        UR.
                      type: string
                    synchronize:
                      description: |-
                        Synchronize represents the sync behavior of the corresponding rule
                        Optional. Defaults to "false" if not specified.
                      type: boolean
                    trigger:
                      description: ResourceSpec is the information to identify the
                        trigger resource.
                      properties:
                        apiVersion:
                          description: APIVersion specifies resource apiVersion.
                          type: string
                        kind:
                          description: Kind specifies resource kind.
                          type: string
                        name:
                          description: Name specifies the resource name.
                          type: string
                        namespace:
                          description: Namespace specifies resource namespace.
                          type: string
                        uid:
                          description: UID specifies the resource uid.
                          type: string
                      type: object
                  required:
                  - deleteDownstream
                  - rule
                  - trigger
                  type: object
                type: array
              synchronize:
                description: |-
                  Synchronize represents the sync behavior of the corresponding rule
                  Optional. Defaults to "false" if not specified.
                  Deprecated, will be removed in 1.14.
                type: boolean
            required:
            - context
            - deleteDownstream
            - policy
            - resource
            - rule
            type: object
          status:
            description: Status contains statistics related to update request.
            properties:
              generatedResources:
                description: |-
                  This will track the resources that are updated by the generate Policy.
                  Will be used during clean up resources.
                items:
                  properties:
                    apiVersion:
                      description: APIVersion specifies resource apiVersion.
                      type: string
                    kind:
                      description: Kind specifies resource kind.
                      type: string
                    name:
                      description: Name specifies the resource name.
                      type: string
                    namespace:
                      description: Namespace specifies resource namespace.
                      type: string
                    uid:
                      description: UID specifies the resource uid.
                      type: string
                  type: object
                type: array
              message:
                description: Specifies request status message.
                type: string
              retryCount:
                type: integer
              state:
                description: State represents state of the update request.
                type: string
            required:
            - state
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
{{- end }}

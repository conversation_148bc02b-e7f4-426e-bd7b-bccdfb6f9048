# -- Internal settings used with `helm template` to generate install manifest
# @ignored
templating:
  enabled: false
  debug: false
  version: ~

reportsServer:
    # -- Kyverno reports-server is used in your cluster
  enabled: false

groups:
  # -- Install CRDs in group `kyverno.io`
  # -- This field can be overwritten by setting crds.labels in the parent chart
  kyverno:
    cleanuppolicies: true
    clustercleanuppolicies: true
    clusterpolicies: true
    globalcontextentries: true
    policies: true
    policyexceptions: true
    updaterequests: true

  # -- Install CRDs in group `reports.kyverno.io`
  policies:
    validatingpolicies: true
    imagevalidatingpolicies: true
    policyexceptions: true
    mutatingpolicies: true
    generatingpolicies: true
    deletingpolicies: true

  # -- This field can be overwritten by setting crds.labels in the parent chart
  reports:
    clusterephemeralreports: true
    ephemeralreports: true

  # -- Install CRDs in group `wgpolicyk8s.io`
  # -- This field can be overwritten by setting crds.labels in the parent chart
  wgpolicyk8s:
    clusterpolicyreports: true
    policyreports: true

# -- Additional CRDs annotations
# -- This field can be overwritten by setting crds.annotations in the parent chart
annotations: {}
  # argocd.argoproj.io/sync-options: Replace=true
  # strategy.spinnaker.io/replace: 'true'

# -- Additional CRDs labels
# -- This field can be overwritten by setting crds.labels in the parent chart
customLabels: {}

{{- if and (eq .Values.cleanupController.enabled true) (eq .Values.crds.groups.kyverno.cleanuppolicies false) }}
{{- fail "CRD cleanuppolicies disabled while cleanupController enabled" }}
{{- end }}
{{- if and (eq .Values.cleanupController.enabled true) (eq .Values.crds.groups.kyverno.clustercleanuppolicies false) }}
{{- fail "CRD clustercleanuppolicies disabled while cleanupController enabled" }}
{{- end }}
{{- if and (eq .Values.reportsController.enabled true) (eq .Values.reportsController.sanityChecks true) (eq .Values.crds.groups.wgpolicyk8s.clusterpolicyreports false) (eq .Values.reportsServer.enabled false) }}
{{- fail "CRD clusterpolicyreports disabled while reportsController enabled" }}
{{- end }}
{{- if and (eq .Values.reportsController.enabled true) (eq .Values.reportsController.sanityChecks true) (eq .Values.crds.groups.wgpolicyk8s.policyreports false) (eq .Values.reportsServer.enabled false) }}
{{- fail "CRD policyreports disabled while reportsController enabled" }}
{{- end }}
{{- if and (eq .Values.reportsController.enabled true) (eq .Values.reportsController.sanityChecks true) (eq .Values.crds.groups.reports.ephemeralreports false) (eq .Values.crds.reportsServer.enabled false) }}
{{- fail "CRD ephemeralreports disabled while reportsController enabled" }}
{{- end }}
{{- if and (eq .Values.reportsController.enabled true) (eq .Values.reportsController.sanityChecks true) (eq .Values.crds.groups.reports.clusterephemeralreports false) (eq .Values.crds.reportsServer.enabled false) }}
{{- fail "CRD clusterephemeralreports disabled while reportsController enabled" }}
{{- end }}

{{- if and (eq .Values.backgroundController.enabled true) (eq .Values.backgroundController.sanityChecks true) (eq .Values.crds.groups.reports.ephemeralreports false) (eq .Values.crds.reportsServer.enabled false) }}
{{- fail "CRD ephemeralreports disabled while reportsController enabled" }}
{{- end }}
{{- if and (eq .Values.backgroundController.enabled true) (eq .Values.backgroundController.sanityChecks true) (eq .Values.crds.groups.reports.clusterephemeralreports false) (eq .Values.crds.reportsServer.enabled false) }}
{{- fail "CRD clusterephemeralreports disabled while reportsController enabled" }}
{{- end }}

{{- if and (eq .Values.policyReportsCleanup.enabled true) (eq .Values.crds.groups.wgpolicyk8s.clusterpolicyreports false) (eq .Values.crds.reportsServer.enabled false) }}
{{- fail "CRD clusterpolicyreports disabled while policyReportsCleanup enabled" }}
{{- end }}
{{- if and (eq .Values.policyReportsCleanup.enabled true) (eq .Values.crds.groups.wgpolicyk8s.policyreports false) (eq .Values.crds.reportsServer.enabled false) }}
{{- fail "CRD policyreports disabled while policyReportsCleanup enabled" }}
{{- end }}

{{- if hasKey .Values "mode" -}}
  {{- fail "mode is not supported anymore, please remove it from your release and use admissionController.replicas instead." -}}
{{- end -}}

{{- if eq (include "kyverno.namespace" .) "kube-system" -}}
  {{- fail "Kyverno cannot be installed in namespace kube-system." -}}
{{- end -}}

{{- if not .Values.upgrade.fromV2 -}}
  {{- $v2 := lookup "apps/v1" "Deployment" (include "kyverno.namespace" .) (include "kyverno.fullname" .) -}}
  {{- if $v2 -}}
    {{- fail (join "\n" (list
      ""
      ""
      "  +--------------------------------------------------------------------------------------------------------------------------------------+"
      "  | An earlier Helm installation of Kyverno was detected.                                                                                |"
      "  | Given this chart version has significant breaking changes, the upgrade has been blocked.                                             |"
      "  | Please review the release notes and chart README section and then, once prepared, set `upgrade.fromV2: true` once ready to proceed.  |"
      "  +--------------------------------------------------------------------------------------------------------------------------------------+"
      ""
      ))
    -}}
  {{- end -}}
{{- end -}}

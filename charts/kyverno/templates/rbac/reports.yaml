{{- if .Values.admissionController.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.rbac.roleName" . }}:admin:reports
  labels:
    {{- include "kyverno.rbac.labels.admin" . | nindent 4 }}
rules:
  - apiGroups:
      - reports.kyverno.io
    resources:
      - ephemeralreports
      - clusterephemeralreports
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.rbac.roleName" . }}:view:reports
  labels:
    {{- include "kyverno.rbac.labels.view" . | nindent 4 }}
rules:
  - apiGroups:
      - reports.kyverno.io
    resources:
      - ephemeralreports
      - clusterephemeralreports
    verbs:
      - get
      - list
      - watch
{{- end -}}
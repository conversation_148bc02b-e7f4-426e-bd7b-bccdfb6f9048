{{- if .Values.admissionController.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.rbac.roleName" . }}:admin:policies
  labels:
    {{- include "kyverno.rbac.labels.admin" . | nindent 4 }}
rules:
  - apiGroups:
      - kyverno.io
    resources:
      - cleanuppolicies
      - clustercleanuppolicies
      - policies
      - clusterpolicies
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.rbac.roleName" . }}:view:policies
  labels:
    {{- include "kyverno.rbac.labels.view" . | nindent 4 }}
rules:
  - apiGroups:
      - kyverno.io
    resources:
      - cleanuppolicies
      - clustercleanuppolicies
      - policies
      - clusterpolicies
    verbs:
      - get
      - list
      - watch
{{- end -}}

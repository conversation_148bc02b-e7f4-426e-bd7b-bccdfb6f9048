{{- if .Values.reportsController.enabled -}}
{{- if .Values.reportsController.networkPolicy.enabled -}}
{{- if .Values.reportsController.metricsService.create -}}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ template "kyverno.reports-controller.name" . }}
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.reports-controller.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "kyverno.reports-controller.matchLabels" . | nindent 6 }}
  policyTypes:
    - Ingress
  {{- if .Values.reportsController.networkPolicy.ingressFrom }}
  ingress:
    - from:
        {{- toYaml .Values.reportsController.networkPolicy.ingressFrom | nindent 8 }}
      ports:
        - protocol: TCP
          port: {{ .Values.reportsController.metricsService.port }}
  {{- else }}
  ingress:
    - {}
  {{- end }}
{{- end -}}
{{- end -}}
{{- end -}}

{{- if .Values.reportsController.enabled -}}
{{- if .Values.reportsController.serviceMonitor.enabled -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "kyverno.reports-controller.name" . }}
  {{- if .Values.reportsController.serviceMonitor.namespace }}
  namespace: {{ .Values.reportsController.serviceMonitor.namespace }}
  {{- else }}
  namespace: {{ template "kyverno.namespace" . }}
  {{- end }}
  {{- with .Values.reportsController.serviceMonitor.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "kyverno.reports-controller.labels" . | nindent 4 }}
    {{- with .Values.reportsController.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "kyverno.reports-controller.matchLabels" . | nindent 6 }}
  namespaceSelector:
    matchNames:
    - {{ template "kyverno.namespace" . }}
  endpoints:
  - port: metrics-port
    interval: {{ .Values.reportsController.serviceMonitor.interval }}
    scrapeTimeout: {{ .Values.reportsController.serviceMonitor.scrapeTimeout }}
    {{- if .Values.reportsController.serviceMonitor.secure }}
    scheme: https
    tlsConfig:
      {{- toYaml .Values.reportsController.serviceMonitor.tlsConfig | nindent 8 }}
    {{- end }}
    {{- with .Values.reportsController.serviceMonitor.relabelings }}
    relabelings:
      {{- toYaml . | nindent 6 }}
    {{- end }}
    {{- with .Values.reportsController.serviceMonitor.metricRelabelings }}
    metricRelabelings:
      {{- toYaml . | nindent 6 }}
    {{- end }}
{{- end -}}
{{- end -}}

{{- if .Values.reportsController.enabled -}}
{{- if .Values.reportsController.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "kyverno.reports-controller.roleName" . }}
  labels:
    {{- include "kyverno.reports-controller.labels" . | nindent 4 }}
  namespace: {{ template "kyverno.namespace" . }}
rules:
  - apiGroups:
      - ''
    resources:
      - configmaps
    verbs:
      - get
      - list
      - watch
    resourceNames:
      - {{ include "kyverno.config.configMapName" . }}
      - {{ include "kyverno.config.metricsConfigMapName" . }}
  - apiGroups:
      - ''
    resources:
      - secrets
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - create
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - delete
      - get
      - patch
      - update
    resourceNames:
      - kyverno-reports-controller
{{- end -}}
{{- end -}}

{{- if .Values.reportsController.enabled -}}
{{- if not .Values.templating.debug -}}
{{- $automountSAToken := .Values.reportsController.rbac.serviceAccount.automountServiceAccountToken }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "kyverno.reports-controller.name" . }}
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.reports-controller.labels" . | nindent 4 }}
  {{- with .Values.reportsController.annotations }}
  annotations:
    {{- tpl (toYaml .) $ | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ template "kyverno.deployment.replicas" .Values.reportsController.replicas }}
  revisionHistoryLimit: {{ .Values.reportsController.revisionHistoryLimit }}
  {{- with .Values.reportsController.updateStrategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "kyverno.reports-controller.matchLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "kyverno.reports-controller.labels" . | nindent 8 }}
        {{- with .Values.reportsController.podLabels }}
        {{- tpl (toYaml .) $ | nindent 8 }}
        {{- end }}
      {{- with .Values.reportsController.podAnnotations }}
      annotations: {{ tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
    spec:
      {{- with .Values.reportsController.imagePullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- tpl (include "kyverno.sortedImagePullSecrets" .) $ | nindent 8 }}
      {{- end }}
      {{- with .Values.reportsController.podSecurityContext }}
      securityContext:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- with .Values.reportsController.nodeSelector | default .Values.global.nodeSelector }}
      nodeSelector:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- with .Values.reportsController.tolerations | default .Values.global.tolerations}}
      tolerations:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- with .Values.reportsController.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- with .Values.reportsController.priorityClassName }}
      priorityClassName: {{ . | quote }}
      {{- end }}
      {{- with .Values.reportsController.hostNetwork }}
      hostNetwork: {{ . }}
      {{- end }}
      {{- with .Values.reportsController.dnsPolicy }}
      dnsPolicy: {{ . }}
      {{- end }}
      {{- with .Values.reportsController.dnsConfig }}
      dnsConfig:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- if or .Values.reportsController.antiAffinity.enabled .Values.reportsController.podAffinity .Values.reportsController.nodeAffinity }}
      affinity:
        {{- if .Values.reportsController.antiAffinity.enabled }}
        {{- with .Values.reportsController.podAntiAffinity }}
        podAntiAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
        {{- end }}
        {{- with .Values.reportsController.podAffinity }}
        podAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
        {{- with .Values.reportsController.nodeAffinity }}
        nodeAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
      {{- end }}
      serviceAccountName: {{ template "kyverno.reports-controller.serviceAccountName" . }}
      automountServiceAccountToken: {{ $automountSAToken }}
      containers:
        - name: controller
          image: {{ include "kyverno.reports-controller.image" (dict "globalRegistry" .Values.global.image.registry "image" .Values.reportsController.image "defaultTag" .Chart.AppVersion) | quote }}
          imagePullPolicy: {{ .Values.reportsController.image.pullPolicy }}
          ports:
          - containerPort: {{ .Values.reportsController.server.port }}
            name: https
            protocol: TCP
          - containerPort: {{ .Values.reportsController.metering.port }}
            name: metrics
            protocol: TCP
          {{ if .Values.reportsController.profiling.enabled }}
          - containerPort: {{ .Values.reportsController.profiling.port }}
            name: profiling-port
            protocol: TCP
          {{- end }}
          args:
            {{- if .Values.reportsController.tracing.enabled }}
            - --enableTracing
            - --tracingAddress={{ .Values.reportsController.tracing.address }}
            - --tracingPort={{ .Values.reportsController.tracing.port }}
            {{- with .Values.reportsController.tracing.creds }}
            - --tracingCreds={{ . }}
            {{- end }}
            {{- end }}
            - --disableMetrics={{ .Values.reportsController.metering.disabled }}
            - --openreportsEnabled={{ .Values.openreports.enabled }}
            {{- if not .Values.reportsController.metering.disabled }}
            - --otelConfig={{ .Values.reportsController.metering.config }}
            - --metricsPort={{ .Values.reportsController.metering.port }}
            {{- with .Values.reportsController.metering.collector }}
            - --otelCollector={{ . }}
            {{- end }}
            {{- with .Values.reportsController.metering.creds }}
            - --transportCreds={{ . }}
            {{- end }}
            {{- end }}
            {{- if or .Values.imagePullSecrets .Values.existingImagePullSecrets }}
            - --imagePullSecrets={{- $secretNames := concat (keys .Values.imagePullSecrets | sortAlpha) (.Values.existingImagePullSecrets | sortAlpha) -}}
              {{- join "," $secretNames -}}
            {{- end }}
            - --resyncPeriod={{ .Values.reportsController.resyncPeriod | default .Values.global.resyncPeriod }}
            {{- include "kyverno.features.flags" (pick (mergeOverwrite (deepCopy .Values.features) .Values.reportsController.featuresOverride)
              "reporting"
              "admissionReports"
              "aggregateReports"
              "policyReports"
              "validatingAdmissionPolicyReports"
              "mutatingAdmissionPolicyReports"
              "backgroundScan"
              "configMapCaching"
              "deferredLoading"
              "globalContext"
              "logging"
              "omitEvents"
              "policyExceptions"
              "registryClient"
              "tuf"
            ) | nindent 12 }}
            {{- range $key, $value := .Values.reportsController.extraArgs }}
            {{- if $value }}
            - --{{ $key }}={{ $value }}
            {{- end }}
            {{- end }}
            {{- if .Values.reportsController.profiling.enabled }}
            - --profile=true
            - --profilePort={{ .Values.reportsController.profiling.port }}
            {{- end }}
            {{- if or (not .Values.reportsController.sanityChecks) .Values.crds.reportsServer.enabled }}
            - --reportsCRDsSanityChecks=false
            {{- end }}
          env:
          - name: KYVERNO_SERVICEACCOUNT_NAME
            value: {{ template "kyverno.reports-controller.serviceAccountName" . }}
          - name: KYVERNO_DEPLOYMENT
            value: {{ template "kyverno.reports-controller.name" . }}
          - name: INIT_CONFIG
            value: {{ template "kyverno.config.configMapName" . }}
          - name: METRICS_CONFIG
            value: {{ template "kyverno.config.metricsConfigMapName" . }}
          - name: KYVERNO_POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: KYVERNO_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: TUF_ROOT
            value: {{ .Values.reportsController.tufRootMountPath }}
          {{- with (concat .Values.global.extraEnvVars .Values.reportsController.extraEnvVars) }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- with .Values.reportsController.resources }}
          resources:
            {{- tpl (toYaml .) $ | nindent 12 }}
          {{- end }}
          {{- with .Values.reportsController.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - mountPath: {{ .Values.reportsController.tufRootMountPath }}
              name: sigstore
            {{- if or .Values.reportsController.caCertificates.data .Values.global.caCertificates.data .Values.reportsController.caCertificates.volume .Values.global.caCertificates.volume }}
            - name: ca-certificates
              mountPath: /etc/ssl/certs/ca-certificates.crt
              {{- if or .Values.reportsController.caCertificates.data .Values.global.caCertificates.data }}
              subPath: ca-certificates.crt
              {{- end }}
            {{- end }}
            {{- if not $automountSAToken }}
            - name: serviceaccount-token
              mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              readOnly: true
            {{- end }}
      volumes:
      - name: sigstore
        {{- toYaml (required "A valid .Values.reportsController.sigstoreVolume entry is required" .Values.reportsController.sigstoreVolume) | nindent 8 }}
      {{- if or .Values.reportsController.caCertificates.data .Values.global.caCertificates.data }}
      - name: ca-certificates
        configMap:
          name: {{ include "kyverno.reports-controller.caCertificatesConfigMapName" . }}
          items:
          - key: ca-certificates
            path: ca-certificates.crt
      {{- else if or .Values.reportsController.caCertificates.volume .Values.global.caCertificates.volume }}
      {{- with (.Values.reportsController.caCertificates.volume | default .Values.global.caCertificates.volume) }}
      - name: ca-certificates
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- end }}
      {{- if not $automountSAToken }}
      - name: serviceaccount-token
        projected:
          defaultMode: 0444
          sources:
            - serviceAccountToken:
                expirationSeconds: 3607
                path: token
            - configMap:
                name: kube-root-ca.crt
                items:
                  - key: ca.crt
                    path: ca.crt
            - downwardAPI:
                items:
                  - path: namespace
                    fieldRef:
                      apiVersion: v1
                      fieldPath: metadata.namespace
      {{- end }}
{{- end -}}
{{- end -}}

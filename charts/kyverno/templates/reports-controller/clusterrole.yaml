{{- if .Values.reportsController.enabled -}}
{{- if .Values.reportsController.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.reports-controller.roleName" . }}
  labels:
    {{- include "kyverno.reports-controller.labels" . | nindent 4 }}
aggregationRule:
  clusterRoleSelectors:
    - matchLabels:
        rbac.kyverno.io/aggregate-to-reports-controller: "true"
    - matchLabels:
        {{- include "kyverno.reports-controller.matchLabels" . | nindent 8 }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.reports-controller.roleName" . }}:core
  labels:
    {{- include "kyverno.reports-controller.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - get
  - apiGroups:
      - ''
    resources:
      - configmaps
      - namespaces
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - kyverno.io
    resources:
      - globalcontextentries
      - globalcontextentries/status
      - policyexceptions
      - policies
      - clusterpolicies
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
      - deletecollection
  - apiGroups:
      - policies.kyverno.io
    resources:
      - validatingpolicies
      - validatingpolicies/status
      - imagevalidatingpolicies
      - imagevalidatingpolicies/status
      - generatingpolicies
      - mutatingpolicies
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
      - deletecollection
  - apiGroups:
      - policies.kyverno.io
    resources:
      - policyexceptions
      - policyexceptions/status
    verbs:
      - get
      - list
      - watch
{{- if .Values.features.validatingAdmissionPolicyReports.enabled }}
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - validatingadmissionpolicies
      - validatingadmissionpolicybindings
    verbs:
      - get
      - list
      - watch
{{- end }}
{{- if .Values.features.mutatingAdmissionPolicyReports.enabled }}
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - mutatingadmissionpolicies
      - mutatingadmissionpolicybindings
    verbs:
      - get
      - list
      - watch
{{- end }}
  - apiGroups:
      - reports.kyverno.io
    resources:
      - ephemeralreports
      - clusterephemeralreports
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
      - deletecollection
  - apiGroups:
      - wgpolicyk8s.io
    resources:
      - policyreports
      - policyreports/status
      - clusterpolicyreports
      - clusterpolicyreports/status
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
      - deletecollection
  - apiGroups:
      - openreports.io
    resources:
      - reports
      - reports/status
      - clusterreports
      - clusterreports/status
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
      - deletecollection
  - apiGroups:
      - ''
      - events.k8s.io
    resources:
      - events
    verbs:
      - create
      - patch
{{- with .Values.reportsController.rbac.coreClusterRole.extraResources }}
  {{- toYaml . | nindent 2 }}
{{- end }}
{{- with .Values.reportsController.rbac.clusterRole.extraResources }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.reports-controller.roleName" $ }}:additional
  labels:
    {{- include "kyverno.reports-controller.labels" $ | nindent 4 }}
rules:
  {{- range . }}
  - apiGroups:
      {{- toYaml .apiGroups | nindent 6 }}
    resources:
      {{- toYaml .resources | nindent 6 }}
    verbs:
      - get
      - list
      - watch
  {{- end }}
{{- end }}
{{- end }}
{{- end }}

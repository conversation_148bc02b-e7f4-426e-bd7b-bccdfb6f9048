{{- if .Values.reportsController.enabled -}}
{{- if .Values.reportsController.rbac.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "kyverno.reports-controller.serviceAccountName" . }}
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.reports-controller.labels" . | nindent 4 }}
  {{- with .Values.reportsController.rbac.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: false
{{- end -}}
{{- end -}}

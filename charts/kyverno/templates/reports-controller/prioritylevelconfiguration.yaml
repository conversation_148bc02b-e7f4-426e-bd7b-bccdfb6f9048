{{- if .Values.reportsController.apiPriorityAndFairness }}
apiVersion: {{ template "kyverno.flowcontrol.apiVersion" . }}
kind: PriorityLevelConfiguration
metadata:
  name: {{ template "kyverno.reports-controller.name" . }}
  labels:
    {{- include "kyverno.reports-controller.labels" . | nindent 4 }}
{{- with .Values.reportsController.priorityLevelConfigurationSpec }}
spec:
  {{- tpl (toYaml .) $ | nindent 8 }}
{{- end }}
{{- end }}

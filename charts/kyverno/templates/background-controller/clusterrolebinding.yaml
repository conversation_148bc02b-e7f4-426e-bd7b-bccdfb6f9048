{{- if .Values.backgroundController.enabled -}}
{{- if .Values.backgroundController.rbac.create -}}
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "kyverno.background-controller.roleName" . }}
  labels:
    {{- include "kyverno.background-controller.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "kyverno.background-controller.roleName" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "kyverno.background-controller.serviceAccountName" . }}
  namespace: {{ template "kyverno.namespace" . }}
{{- if .Values.backgroundController.rbac.createViewRoleBinding }}
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "kyverno.background-controller.roleName" . }}:view
  labels:
    {{- include "kyverno.background-controller.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Values.backgroundController.rbac.viewRoleName }}
subjects:
- kind: ServiceAccount
  name: {{ template "kyverno.background-controller.serviceAccountName" . }}
  namespace: {{ template "kyverno.namespace" . }}
{{- end -}}
{{- end -}}
{{- end -}}

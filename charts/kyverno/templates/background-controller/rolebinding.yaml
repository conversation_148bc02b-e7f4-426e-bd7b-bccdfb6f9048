{{- if .Values.backgroundController.enabled -}}
{{- if .Values.backgroundController.rbac.create -}}
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "kyverno.background-controller.roleName" . }}
  labels:
    {{- include "kyverno.background-controller.labels" . | nindent 4 }}
  namespace: {{ template "kyverno.namespace" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "kyverno.background-controller.roleName" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "kyverno.background-controller.serviceAccountName" . }}
    namespace: {{ template "kyverno.namespace" . }}
{{- end -}}
{{- end -}}

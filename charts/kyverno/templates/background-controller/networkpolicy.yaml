{{- if .Values.backgroundController.enabled -}}
{{- if .Values.backgroundController.networkPolicy.enabled -}}
{{- if .Values.backgroundController.metricsService.create -}}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ template "kyverno.background-controller.name" . }}
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.background-controller.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "kyverno.background-controller.matchLabels" . | nindent 6 }}
  policyTypes:
    - Ingress
  {{- if .Values.backgroundController.networkPolicy.ingressFrom }}
  ingress:
    - from:
        {{- toYaml .Values.backgroundController.networkPolicy.ingressFrom | nindent 8 }}
      ports:
        - protocol: TCP
          port: {{ .Values.backgroundController.metricsService.port }}
  {{- else }}
  ingress:
    - {}
  {{- end }}
{{- end -}}
{{- end -}}
{{- end -}}

{{- if .Values.backgroundController.enabled -}}
{{- if .Values.backgroundController.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.background-controller.roleName" . }}
  labels:
    {{- include "kyverno.background-controller.labels" . | nindent 4 }}
aggregationRule:
  clusterRoleSelectors:
    - matchLabels:
        rbac.kyverno.io/aggregate-to-background-controller: "true"
    - matchLabels:
        {{- include "kyverno.background-controller.matchLabels" . | nindent 8 }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.background-controller.roleName" . }}:core
  labels:
    {{- include "kyverno.background-controller.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - get
  - apiGroups:
      - kyverno.io
    resources:
      - policies
      - policies/status
      - clusterpolicies
      - clusterpolicies/status
      - policyexceptions
      - updaterequests
      - updaterequests/status
      - globalcontextentries
      - globalcontextentries/status
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
      - deletecollection
  - apiGroups:
      - policies.kyverno.io
    resources:
      - generatingpolicies
      - mutatingpolicies
      - policyexceptions
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
      - deletecollection
  - apiGroups:
      - policies.kyverno.io
    resources:
      - policyexceptions
    verbs:
      - create
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - ''
    resources:
      - namespaces
      - configmaps
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ''
      - events.k8s.io
    resources:
      - events
    verbs:
      - create
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - reports.kyverno.io
    resources:
      - ephemeralreports
      - clusterephemeralreports
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
      - deletecollection
{{- with .Values.backgroundController.rbac.coreClusterRole.extraResources }}
  {{- toYaml . | nindent 2 }}
{{- end }}
{{- with .Values.backgroundController.rbac.clusterRole.extraResources }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.background-controller.roleName" $ }}:additional
  labels:
    {{- include "kyverno.background-controller.labels" $ | nindent 4 }}
rules:
  {{- toYaml . | nindent 2 }}
{{- end }}
{{- end }}
{{- end }}

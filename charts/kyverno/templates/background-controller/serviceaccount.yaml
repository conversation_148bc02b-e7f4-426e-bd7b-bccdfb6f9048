{{- if .Values.backgroundController.enabled -}}
{{- if .Values.backgroundController.rbac.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "kyverno.background-controller.serviceAccountName" . }}
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.background-controller.labels" . | nindent 4 }}
  {{- with .Values.backgroundController.rbac.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: false
{{- end -}}
{{- end -}}

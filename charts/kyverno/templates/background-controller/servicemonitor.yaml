{{- if .Values.backgroundController.enabled -}}
{{- if .Values.backgroundController.serviceMonitor.enabled -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "kyverno.background-controller.name" . }}
  {{- if .Values.backgroundController.serviceMonitor.namespace }}
  namespace: {{ .Values.backgroundController.serviceMonitor.namespace }}
  {{- else }}
  namespace: {{ template "kyverno.namespace" . }}
  {{- end }}
  {{- with .Values.backgroundController.serviceMonitor.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "kyverno.background-controller.labels" . | nindent 4 }}
    {{- with .Values.backgroundController.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "kyverno.background-controller.matchLabels" . | nindent 6 }}
  namespaceSelector:
    matchNames:
    - {{ template "kyverno.namespace" . }}
  endpoints:
  - port: metrics-port
    interval: {{ .Values.backgroundController.serviceMonitor.interval }}
    scrapeTimeout: {{ .Values.backgroundController.serviceMonitor.scrapeTimeout }}
    {{- if .Values.backgroundController.serviceMonitor.secure }}
    scheme: https
    tlsConfig:
      {{- toYaml .Values.backgroundController.serviceMonitor.tlsConfig | nindent 8 }}
    {{- end }}
    {{- with .Values.backgroundController.serviceMonitor.relabelings }}
    relabelings:
      {{- toYaml . | nindent 6 }}
    {{- end }}
    {{- with .Values.backgroundController.serviceMonitor.metricRelabelings }}
    metricRelabelings:
      {{- toYaml . | nindent 6 }}
    {{- end }}
{{- end -}}
{{- end -}}

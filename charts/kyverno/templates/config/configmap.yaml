{{- if .Values.config.create -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "kyverno.config.configMapName" . }}
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.config.labels" . | nindent 4 }}
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- if .Values.config.preserve }}
    helm.sh/resource-policy: "keep"
    {{- end }}
data:
  enableDefaultRegistryMutation: {{ .Values.config.enableDefaultRegistryMutation | quote }}
  {{- with .Values.config.defaultRegistry }}
  defaultRegistry: {{ . | quote }}
  {{- end }}
  generateSuccessEvents: {{ .Values.config.generateSuccessEvents | quote }}
  {{- with .Values.config.excludeGroups }}
  excludeGroups: {{ join "," . | quote }}
  {{- end -}}
  {{- with .Values.config.excludeUsernames }}
  excludeUsernames: {{ join "," . | quote }}
  {{- end -}}
  {{- with .Values.config.excludeRoles }}
  excludeRoles: {{ join "," . | quote }}
  {{- end -}}
  {{- with .Values.config.excludeClusterRoles }}
  excludeClusterRoles: {{ join "," . | quote }}
  {{- end -}}
  {{- if .Values.config.resourceFilters }}
  resourceFilters: >-
    {{- include "kyverno.config.resourceFilters" . | trim | nindent 4 }}
  {{- end -}}
  {{- with .Values.config.updateRequestThreshold }}
  updateRequestThreshold: {{ . | quote }}
  {{- end -}}
  {{- if and .Values.config.webhooks .Values.config.excludeKyvernoNamespace }}
  webhooks: {{ include "kyverno.config.webhooks" . | quote }}
  {{- else if .Values.config.webhooks }}
  webhooks: {{ .Values.config.webhooks | toJson | quote }}
  {{- else if .Values.config.excludeKyvernoNamespace }}
  webhooks: '{"namespaceSelector": {"matchExpressions": [{"key":"kubernetes.io/metadata.name","operator":"NotIn","values":["{{ include "kyverno.namespace" . }}"]}]}}'
  {{- end -}}
  {{- with .Values.config.webhookAnnotations }}
  webhookAnnotations: {{ toJson . | quote }}
  {{- end }}
  {{- with .Values.config.webhookLabels }}
  webhookLabels: {{ toJson . | quote }}
  {{- end }}
  {{- with .Values.config.matchConditions }}
  matchConditions: {{ toJson . | quote }}
  {{- end }}
{{- end -}}

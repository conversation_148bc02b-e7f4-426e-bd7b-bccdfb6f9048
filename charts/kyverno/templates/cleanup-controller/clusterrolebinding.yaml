{{- if .Values.cleanupController.enabled -}}
{{- if .Values.cleanupController.rbac.create -}}
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "kyverno.cleanup-controller.roleName" . }}
  labels:
    {{- include "kyverno.cleanup-controller.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "kyverno.cleanup-controller.roleName" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "kyverno.cleanup-controller.serviceAccountName" . }}
  namespace: {{ template "kyverno.namespace" . }}
{{- end -}}
{{- end -}}

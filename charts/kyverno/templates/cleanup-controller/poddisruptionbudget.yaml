{{- if .Values.cleanupController.enabled -}}
{{- if or .Values.cleanupController.podDisruptionBudget.enabled (gt (int .Values.cleanupController.replicas) 1) -}}
apiVersion: {{ template "kyverno.pdb.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "kyverno.cleanup-controller.name" . }}
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.cleanup-controller.labels" . | nindent 4 }}
spec:
  {{- include "kyverno.pdb.spec" .Values.cleanupController.podDisruptionBudget | nindent 2 }}
  selector:
    matchLabels:
      {{- include "kyverno.cleanup-controller.matchLabels" . | nindent 6 }}
{{- end -}}
{{- end -}}

{{- if .Values.cleanupController.enabled -}}
{{- if .Values.cleanupController.rbac.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "kyverno.cleanup-controller.serviceAccountName" . }}
  namespace: {{ template "kyverno.namespace" . }}
  {{- if .Values.webhooksCleanup.autoDeleteWebhooks.enabled }}
  {{- if not .Values.templating.enabled }}
  finalizers:
  - kyverno.io/policywebhooks
  - kyverno.io/ttlwebhooks
  {{- end }}
  {{- end }}
  labels:
    {{- include "kyverno.cleanup-controller.labels" . | nindent 4 }}
  {{- with .Values.cleanupController.rbac.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: false
{{- end -}}
{{- end -}}

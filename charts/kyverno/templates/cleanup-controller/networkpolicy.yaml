{{- if .Values.cleanupController.enabled -}}
{{- if .Values.cleanupController.networkPolicy.enabled -}}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ template "kyverno.cleanup-controller.name" . }}
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.cleanup-controller.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "kyverno.cleanup-controller.matchLabels" . | nindent 6 }}
  policyTypes:
    - Ingress
  {{- if .Values.cleanupController.networkPolicy.ingressFrom }}
  ingress:
    - from:
        {{- toYaml .Values.cleanupController.networkPolicy.ingressFrom | nindent 8 }}
      ports:
        - protocol: TCP
          port: 9443 # webhook access
        # Allow prometheus scrapes for metrics
        {{- if .Values.cleanupController.metricsService.create }}
        - protocol: TCP
          port: {{ .Values.cleanupController.metricsService.port }}
        {{- end }}
  {{- else }}
  ingress:
    - {}
  {{- end }}
{{- end -}}
{{- end -}}

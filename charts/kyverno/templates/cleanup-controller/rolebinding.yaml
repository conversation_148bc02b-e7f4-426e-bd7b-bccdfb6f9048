{{- if .Values.cleanupController.enabled -}}
{{- if .Values.cleanupController.rbac.create -}}
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "kyverno.cleanup-controller.roleName" . }}
  {{- if .Values.webhooksCleanup.autoDeleteWebhooks.enabled }}
  {{- if not .Values.templating.enabled }}
  finalizers:
  - kyverno.io/policywebhooks
  - kyverno.io/ttlwebhooks
  {{- end }}
  {{- end }}
  labels:
    {{- include "kyverno.cleanup-controller.labels" . | nindent 4 }}
  namespace: {{ template "kyverno.namespace" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "kyverno.cleanup-controller.roleName" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "kyverno.cleanup-controller.serviceAccountName" . }}
    namespace: {{ template "kyverno.namespace" . }}
{{- end -}}
{{- end -}}

{{- if .Values.cleanupController.enabled -}}
{{- if .Values.cleanupController.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.cleanup-controller.roleName" . }}
  labels:
    {{- include "kyverno.cleanup-controller.labels" . | nindent 4 }}
aggregationRule:
  clusterRoleSelectors:
    - matchLabels:
        rbac.kyverno.io/aggregate-to-cleanup-controller: "true"
    - matchLabels:
        {{- include "kyverno.cleanup-controller.matchLabels" . | nindent 8 }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.cleanup-controller.roleName" . }}:core
  {{- if .Values.webhooksCleanup.autoDeleteWebhooks.enabled }}
  {{- if not .Values.templating.enabled }}
  finalizers:
  - kyverno.io/policywebhooks
  - kyverno.io/ttlwebhooks
  {{- end }}
  {{- end }}
  labels:
    {{- include "kyverno.cleanup-controller.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - get
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - validatingwebhookconfigurations
    verbs:
      - create
      - delete
      - get
      - list
      - update
      - watch
  - apiGroups:
      - ''
    resources:
      - namespaces
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - kyverno.io
    resources:
      - clustercleanuppolicies
      - cleanuppolicies
    verbs:
      - list
      - watch
  - apiGroups:
      - policies.kyverno.io
    resources:
      - deletingpolicies
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - policies.kyverno.io
    resources:
      - deletingpolicies/status
    verbs:
      - update
  - apiGroups:
      - policies.kyverno.io
    resources:
      - policyexceptions
    verbs:
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - kyverno.io
    resources:
      - globalcontextentries
      - globalcontextentries/status
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
      - deletecollection
  - apiGroups:
      - kyverno.io
    resources:
      - clustercleanuppolicies/status
      - cleanuppolicies/status
    verbs:
      - update
  - apiGroups:
      - ''
    resources:
      - configmaps
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ''
      - events.k8s.io
    resources:
      - events
    verbs:
      - create
      - patch
      - update
  - apiGroups:
      - authorization.k8s.io
    resources:
      - subjectaccessreviews
    verbs:
      - create
  {{- if .Values.webhooksCleanup.autoDeleteWebhooks.enabled }}
  {{- if not .Values.templating.enabled }}
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterroles
      - clusterrolebindings
    resourceNames:
      - {{ template "kyverno.cleanup-controller.roleName" . }}
      - {{ template "kyverno.cleanup-controller.roleName" . }}:core
      - {{ template "kyverno.cleanup-controller.roleName" . }}:temporary
    verbs:
      - get
      - patch
      - update
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterroles
      - clusterrolebindings
    verbs:
      - create
      - list
  {{- end }}
  {{- end }}
{{- with .Values.cleanupController.rbac.clusterRole.extraResources }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.cleanup-controller.roleName" $ }}:additional
  labels:
    {{- include "kyverno.cleanup-controller.labels" $ | nindent 4 }}
rules:
  {{- toYaml . | nindent 2 }}
{{- end }}
{{- end }}
{{- end }}

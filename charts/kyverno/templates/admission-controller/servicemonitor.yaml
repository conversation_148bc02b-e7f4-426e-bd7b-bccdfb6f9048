{{- if .Values.admissionController.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "kyverno.admission-controller.name" . }}
  {{- if .Values.admissionController.serviceMonitor.namespace }}
  namespace: {{ .Values.admissionController.serviceMonitor.namespace }}
  {{- else }}
  namespace: {{ template "kyverno.namespace" . }}
  {{- end }}
  {{- with .Values.admissionController.serviceMonitor.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "kyverno.admission-controller.labels" . | nindent 4 }}
    {{- with .Values.admissionController.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "kyverno.admission-controller.matchLabels" . | nindent 6 }}
  namespaceSelector:
    matchNames:
    - {{ template "kyverno.namespace" . }}
  endpoints:
  - port: metrics-port
    interval: {{ .Values.admissionController.serviceMonitor.interval }}
    scrapeTimeout: {{ .Values.admissionController.serviceMonitor.scrapeTimeout }}
    {{- if .Values.admissionController.serviceMonitor.secure }}
    scheme: https
    tlsConfig:
      {{- toYaml .Values.admissionController.serviceMonitor.tlsConfig | nindent 8 }}
    {{- end }}
    {{- with .Values.admissionController.serviceMonitor.relabelings }}
    relabelings:
      {{- toYaml . | nindent 6 }}
    {{- end }}
    {{- with .Values.admissionController.serviceMonitor.metricRelabelings }}
    metricRelabelings:
      {{- toYaml . | nindent 6 }}
    {{- end }}
{{- end -}}

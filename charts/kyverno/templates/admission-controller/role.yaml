{{- if .Values.admissionController.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "kyverno.admission-controller.roleName" . }}
  namespace: {{ template "kyverno.namespace" . }}
  {{- if .Values.webhooksCleanup.autoDeleteWebhooks.enabled }}
  {{- if not .Values.templating.enabled }}
  finalizers:
  - kyverno.io/webhooks
  - kyverno.io/exceptionwebhooks
  - kyverno.io/globalcontextwebhooks
  {{- end }}
  {{- end }}
  labels:
    {{- include "kyverno.admission-controller.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - ''
    resources:
      - secrets
      - serviceaccounts
    verbs:
      - get
      - list
      - watch
      - patch
      - create
      - update
      - delete
  - apiGroups:
      - ''
    resources:
      - configmaps
    verbs:
      - get
      - list
      - watch
    resourceNames:
      - {{ include "kyverno.config.configMapName" . }}
      - {{ include "kyverno.config.metricsConfigMapName" . }}
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - create
      - delete
      - get
      - patch
      - update
  {{- if .Values.webhooksCleanup.autoDeleteWebhooks.enabled }}
  {{- if not .Values.templating.enabled }}
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - roles
      - rolebindings
    resourceNames:
      - {{ template "kyverno.admission-controller.roleName" . }}
      - {{ template "kyverno.admission-controller.roleName" . }}:temporary
    verbs:
      - get
      - patch
      - update
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - roles
      - rolebindings
    verbs:
      - create
  {{- end }}
  {{- end }}
  # Allow update of Kyverno deployment annotations
  - apiGroups:
      - apps
    resources:
      - deployments
      {{- if .Values.webhooksCleanup.enabled }}
      {{- if not .Values.templating.enabled }}
      - deployments/scale
      {{- end }}
      {{- end }}
    verbs:
      - get
      - list
      - watch
      {{- if .Values.webhooksCleanup.enabled }}
      {{- if not .Values.templating.enabled }}
      - patch
      - update
      {{- end }}
      {{- end }}
{{- end -}}

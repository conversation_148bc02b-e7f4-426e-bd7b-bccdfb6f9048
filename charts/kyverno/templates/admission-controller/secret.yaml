{{- if .Values.admissionController.createSelfSignedCert -}}
{{- $ca := genCA (printf "*.%s.svc" (include "kyverno.namespace" .)) 1024 -}}
{{- $svcName := (printf "%s.%s.svc" (include "kyverno.admission-controller.serviceName" .) (include "kyverno.namespace" .)) -}}
{{- $cert := genSignedCert $svcName nil (list $svcName) 1024 $ca -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "kyverno.admission-controller.serviceName" . }}.{{ template "kyverno.namespace" . }}.svc.kyverno-tls-ca
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.admission-controller.labels" . | nindent 4 }}
type: kubernetes.io/tls
data:
  tls.key: {{ $ca.Key | b64enc }}
  tls.crt: {{ $ca.Cert | b64enc }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "kyverno.admission-controller.serviceName" . }}.{{ template "kyverno.namespace" . }}.svc.kyverno-tls-pair
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.admission-controller.labels" . | nindent 4 }}
  annotations:
    self-signed-cert: "true"
type: kubernetes.io/tls
data:
  tls.key: {{ $cert.Key | b64enc }}
  tls.crt: {{ $cert.Cert | b64enc }}
{{- end -}}

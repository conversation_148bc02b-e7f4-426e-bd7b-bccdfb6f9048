---
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kyverno.admission-controller.serviceName" . }}
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.admission-controller.labels" . | nindent 4 }}
  {{- with .Values.admissionController.service.annotations }}
  annotations: {{ tpl (toYaml .) $ | nindent 4 }}
  {{- end }}
spec:
  ports:
  - port: {{ .Values.admissionController.service.port }}
    targetPort: https
    protocol: TCP
    name: https
    appProtocol: https
    {{- if and (eq .Values.admissionController.service.type "NodePort") (not (empty .Values.admissionController.service.nodePort)) }}
    nodePort: {{ .Values.admissionController.service.nodePort }}
    {{- end }}
  selector:
    {{- include "kyverno.admission-controller.matchLabels" . | nindent 4 }}
  type: {{ .Values.admissionController.service.type }}
  {{- if .Values.admissionController.service.trafficDistribution }}
  trafficDistribution: {{ .Values.admissionController.service.trafficDistribution }}
  {{- end }}
{{- if .Values.admissionController.metricsService.create }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kyverno.admission-controller.serviceName" . }}-metrics
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.admission-controller.labels" . | nindent 4 }}
  {{- with .Values.admissionController.metricsService.annotations }}
  annotations: {{ tpl (toYaml .) $ | nindent 4 }}
  {{- end }}
spec:
  ports:
  - port: {{ .Values.admissionController.metricsService.port }}
    targetPort: {{ .Values.admissionController.metering.port }}
    protocol: TCP
    name: metrics-port
    {{- if and (eq .Values.admissionController.metricsService.type "NodePort") (not (empty .Values.admissionController.metricsService.nodePort)) }}
    nodePort: {{ .Values.admissionController.metricsService.nodePort }}
    {{- end }}
  selector:
    {{- include "kyverno.admission-controller.matchLabels" . | nindent 4 }}
  type: {{ .Values.admissionController.metricsService.type }}
  {{- if .Values.admissionController.metricsService.trafficDistribution }}
  trafficDistribution: {{ .Values.admissionController.metricsService.trafficDistribution }}
  {{- end }}
{{- end -}}
{{- if .Values.admissionController.profiling.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kyverno.admission-controller.serviceName" . }}-profiling
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.admission-controller.labels" . | nindent 4 }}
spec:
  ports:
  - port: {{ .Values.admissionController.profiling.port }}
    targetPort: {{ .Values.admissionController.profiling.port }}
    protocol: TCP
    name: profiling-port
    {{- if and (eq .Values.admissionController.profiling.serviceType "NodePort") (not (empty .Values.admissionController.profiling.nodePort)) }}
    nodePort: {{ .Values.admissionController.profiling.nodePort }}
    {{- end }}
  selector:
    {{- include "kyverno.admission-controller.matchLabels" . | nindent 4 }}
  type: {{ .Values.admissionController.profiling.serviceType }}
{{- end -}}

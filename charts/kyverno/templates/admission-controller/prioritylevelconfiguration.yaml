{{- if .Values.admissionController.apiPriorityAndFairness }}
apiVersion: {{ template "kyverno.flowcontrol.apiVersion" . }}
kind: PriorityLevelConfiguration
metadata:
  name: {{ template "kyverno.admission-controller.name" . }}
  labels:
    {{- include "kyverno.admission-controller.labels" . | nindent 4 }}
{{- with .Values.admissionController.priorityLevelConfigurationSpec }}
spec:
  {{- tpl (toYaml .) $ | nindent 2 }}
{{- end }}
{{- end }}

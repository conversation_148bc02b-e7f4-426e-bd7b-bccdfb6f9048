{{- if .Values.admissionController.rbac.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "kyverno.admission-controller.serviceAccountName" . }}
  namespace: {{ template "kyverno.namespace" . }}
  {{- if .Values.webhooksCleanup.autoDeleteWebhooks.enabled }}
  {{- if not .Values.templating.enabled }}
  finalizers:
  - kyverno.io/webhooks
  - kyverno.io/exceptionwebhooks
  - kyverno.io/globalcontextwebhooks
  {{- end }}
  {{- end }}
  labels:
    {{- include "kyverno.admission-controller.labels" . | nindent 4 }}
  {{- with .Values.admissionController.rbac.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: false
{{- end }}

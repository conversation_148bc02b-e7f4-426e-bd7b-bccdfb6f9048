Chart version: {{ .Chart.Version }}
Kyverno version: {{ default .Chart.AppVersion (default .Values.admissionController.container.image.tag .Values.admissionController.initContainer.image.tag) }}

Thank you for installing {{ .Chart.Name }}! Your release is named {{ .Release.Name }}.

The following components have been installed in your cluster:
{{- if .Values.crds.install }}
- CRDs
{{- end }}
- Admission controller
{{- if .Values.reportsController.enabled }}
- Reports controller
{{- end }}
{{- if .Values.cleanupController.enabled }}
- Cleanup controller
{{- end }}
{{- if .Values.backgroundController.enabled }}
- Background controller
{{- end }}
{{- if .Values.grafana.enabled }}
- Grafana dashboard
{{- end }}

{{ if not .Values.admissionController.replicas }}
⚠️  WARNING: Setting the admission controller replica count below 2 means Kyverno is not running in high availability mode.
{{- else if lt (int .Values.admissionController.replicas) 2 }}
⚠️  WARNING: Setting the admission controller replica count below 2 means Kyverno is not running in high availability mode.
{{- end }}

{{- if semverCompare "<1.21.0" .Capabilities.KubeVersion.Version }}
⚠️  WARNING: The minimal Kubernetes version officially supported by Kyverno is 1.21. Earlier versions are untested and Kyverno is not guaranteed to work with Kubernetes {{ .Capabilities.KubeVersion.Version }}.
{{- end }}

{{- with .Values.config.matchConditions }}
⚠️  WARNING: Match conditions require a Kubernetes 1.27+ cluster with `AdmissionWebhookMatchConditions` feature gate enabled.
{{- end }}

{{- with .Values.features.generateValidatingAdmissionPolicy.enabled }}
⚠️  WARNING: Generating ValidatingAdmissionPolicy requires a Kubernetes 1.27+ cluster with `ValidatingAdmissionPolicy` feature gate and `admissionregistration.k8s.io` API group enabled.
{{- end }}

{{- with .Values.features.validatingAdmissionPolicyReports.enabled }}
⚠️  WARNING: Generating reports from ValidatingAdmissionPolicies requires a Kubernetes 1.27+ cluster with `ValidatingAdmissionPolicy` feature gate and `admissionregistration.k8s.io` API group enabled.
{{- end }}

{{- with .Values.features.generateMutatingAdmissionPolicy.enabled }}
⚠️  WARNING: Generating MutatingAdmissionPolicy requires a Kubernetes 1.32+ cluster with `MutatingAdmissionPolicy` feature gate and `admissionregistration.k8s.io` API group enabled.
{{- end }}

{{- with .Values.features.mutatingAdmissionPolicyReports.enabled }}
⚠️  WARNING: Generating reports from MutatingAdmissionPolicies requires a Kubernetes 1.32+ cluster with `MutatingAdmissionPolicy` feature gate and `admissionregistration.k8s.io` API group enabled.
{{- end }}

{{ if not .Values.features.policyExceptions.enabled }}
⚠️  WARNING: PolicyExceptions are disabled by default. To enable them, set '--enablePolicyException' to true.
{{- end }}

💡 Note: There is a trade-off when deciding which approach to take regarding Namespace exclusions. Please see the documentation at https://kyverno.io/docs/installation/#security-vs-operability to understand the risks.

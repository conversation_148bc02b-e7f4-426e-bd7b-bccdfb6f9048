{{- if .Values.policyReportsCleanup.enabled -}}
{{- if not .Values.templating.enabled -}}
{{- $automountSAToken := .Values.admissionController.rbac.serviceAccount.automountServiceAccountToken }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "kyverno.fullname" . }}-clean-reports
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.hooks.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: post-upgrade
    helm.sh/hook-delete-policy: before-hook-creation,hook-succeeded,hook-failed
spec:
  backoffLimit: 2
  template:
    {{- if or .Values.policyReportsCleanup.podAnnotations .Values.policyReportsCleanup.podLabels }}
    metadata:
      {{- with .Values.policyReportsCleanup.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.policyReportsCleanup.podLabels }}
      labels:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- end }}
    spec:
      serviceAccountName: {{ template "kyverno.admission-controller.serviceAccountName" . }}
      automountServiceAccountToken: true
      {{- with .Values.policyReportsCleanup.podSecurityContext }}
      securityContext:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      restartPolicy: Never
      containers:
        - name: kubectl
          image: {{ (include "kyverno.image" (dict "globalRegistry" .Values.global.image.registry "image" .Values.policyReportsCleanup.image "defaultTag" (default .Chart.AppVersion .Values.policyReportsCleanup.image.tag))) | quote }}
          imagePullPolicy: {{ .Values.policyReportsCleanup.image.pullPolicy }}
          command:
            - /bin/bash
            - -c
            - |
              set -euo pipefail
              NAMESPACES=$(kubectl get namespaces --no-headers=true | awk '{print $1}')

              for ns in ${NAMESPACES[@]};
              do
                COUNT=$(kubectl get policyreports.wgpolicyk8s.io -n $ns --no-headers=true | awk '/pol/{print $1}' | wc -l)

                if [ $COUNT -gt 0 ]; then
                  echo "deleting $COUNT policyreports in namespace $ns"
                  kubectl get policyreports.wgpolicyk8s.io -n $ns --no-headers=true | awk '/pol/{print $1}' | xargs kubectl delete -n $ns policyreports.wgpolicyk8s.io
                else
                  echo "no policyreports in namespace $ns"
                fi
              done

              COUNT=$(kubectl get clusterpolicyreports.wgpolicyk8s.io --no-headers=true | awk '/pol/{print $1}' | wc -l)

              if [ $COUNT -gt 0 ]; then
                echo "deleting $COUNT clusterpolicyreports"
                kubectl get clusterpolicyreports.wgpolicyk8s.io --no-headers=true | awk '/pol/{print $1}' | xargs kubectl delete clusterpolicyreports.wgpolicyk8s.io
              else
                echo "no clusterpolicyreports"
              fi
          {{- with .Values.policyReportsCleanup.resources }}
          resources:
            {{- tpl (toYaml .) $ | nindent 12 }}
          {{- end }}
          {{- with .Values.policyReportsCleanup.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- if not $automountSAToken }}
          volumeMounts:
            - name: serviceaccount-token
              mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              readOnly: true
          {{- end }}
      {{- with .Values.policyReportsCleanup.imagePullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- tpl (include "kyverno.sortedImagePullSecrets" .) $ | nindent 8 }}
      {{- end }}
      {{- with .Values.policyReportsCleanup.tolerations | default .Values.global.tolerations}}
      tolerations:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- with .Values.policyReportsCleanup.nodeSelector | default .Values.global.nodeSelector }}
      nodeSelector:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- if or .Values.policyReportsCleanup.podAntiAffinity .Values.policyReportsCleanup.podAffinity .Values.policyReportsCleanup.nodeAffinity }}
      affinity:
        {{- with .Values.policyReportsCleanup.podAntiAffinity }}
        podAntiAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
        {{- with .Values.policyReportsCleanup.podAffinity }}
        podAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
        {{- with .Values.policyReportsCleanup.nodeAffinity }}
        nodeAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
      {{- end }}
      {{- if not $automountSAToken }}
      volumes:
        - name: serviceaccount-token
          projected:
            defaultMode: 0444
            sources:
              - serviceAccountToken:
                  expirationSeconds: 3607
                  path: token
              - configMap:
                  name: kube-root-ca.crt
                  items:
                    - key: ca.crt
                      path: ca.crt
              - downwardAPI:
                  items:
                    - path: namespace
                      fieldRef:
                        apiVersion: v1
                        fieldPath: metadata.namespace
      {{- end }}
{{- end -}}
{{- end -}}

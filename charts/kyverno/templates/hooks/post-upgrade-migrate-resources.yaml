{{- if .Values.crds.migration.enabled -}}
{{- if not .Values.templating.enabled -}}
{{- $automountSAToken := .Values.crds.migration.serviceAccount.automountServiceAccountToken }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "kyverno.fullname" . }}:migrate-resources
  labels:
    {{- include "kyverno.hooks.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: post-upgrade
    helm.sh/hook-delete-policy: before-hook-creation,hook-succeeded,hook-failed
    helm.sh/hook-weight: "100"
rules:
  - apiGroups:
    - kyverno.io
    resources:
    - '*'
    verbs:
    - get
    - list
    - update
  - apiGroups:
    - apiextensions.k8s.io
    resources:
    - customresourcedefinitions
    verbs:
    - get
  - apiGroups:
    - apiextensions.k8s.io
    resources:
    - customresourcedefinitions/status
    verbs:
    - update
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "kyverno.fullname" . }}:migrate-resources
  labels:
    {{- include "kyverno.hooks.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: post-upgrade
    helm.sh/hook-delete-policy: before-hook-creation,hook-succeeded,hook-failed
    helm.sh/hook-weight: "100"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "kyverno.fullname" . }}:migrate-resources
subjects:
  - kind: ServiceAccount
    name: {{ template "kyverno.fullname" . }}-migrate-resources
    namespace: {{ template "kyverno.namespace" . }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "kyverno.fullname" . }}-migrate-resources
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.hooks.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: post-upgrade
    helm.sh/hook-delete-policy: before-hook-creation,hook-succeeded
    helm.sh/hook-weight: "100"
automountServiceAccountToken: false
---
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "kyverno.fullname" . }}-migrate-resources
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.hooks.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: post-upgrade
    helm.sh/hook-delete-policy: before-hook-creation,hook-succeeded,hook-failed
    helm.sh/hook-weight: "200"
spec:
  backoffLimit: 2
  template:
    {{- if or .Values.crds.migration.podAnnotations .Values.crds.migration.podLabels }}
    metadata:
      {{- with .Values.crds.migration.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.crds.migration.podLabels }}
      labels:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- end }}
    spec:
      serviceAccountName: {{ template "kyverno.fullname" . }}-migrate-resources
      automountServiceAccountToken: {{ $automountSAToken }}
      {{- with .Values.crds.migration.podSecurityContext }}
      securityContext:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      restartPolicy: Never
      containers:
        - name: kubectl
          image: {{ (include "kyverno.image" (dict "globalRegistry" .Values.global.image.registry "image" .Values.crds.migration.image "defaultTag" (default .Chart.AppVersion .Values.crds.migration.image.tag))) | quote }}
          imagePullPolicy: {{ .Values.crds.migration.image.pullPolicy }}
          args:
            - migrate
            {{- range .Values.crds.migration.resources }}
            - --resource
            - {{ . }}
            {{- end }}
          {{- with .Values.crds.migration.podResources }}
          resources:
            {{- tpl (toYaml .) $ | nindent 12 }}
          {{- end }}
          {{- with .Values.crds.migration.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- if not $automountSAToken }}
          volumeMounts:
            - name: serviceaccount-token
              mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              readOnly: true
          {{- end }}
      {{- with .Values.crds.migration.imagePullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- tpl (include "kyverno.sortedImagePullSecrets" .) $ | nindent 8 }}
      {{- end }}
      {{- with .Values.crds.migration.tolerations | default .Values.global.tolerations}}
      tolerations:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- with .Values.crds.migration.nodeSelector | default .Values.global.nodeSelector }}
      nodeSelector:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- if or .Values.crds.migration.podAntiAffinity .Values.crds.migration.podAffinity .Values.crds.migration.nodeAffinity }}
      affinity:
        {{- with .Values.crds.migration.podAntiAffinity }}
        podAntiAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
        {{- with .Values.crds.migration.podAffinity }}
        podAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
        {{- with .Values.crds.migration.nodeAffinity }}
        nodeAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
      {{- end }}
      {{- if not $automountSAToken }}
      volumes:
        - name: serviceaccount-token
          projected:
            defaultMode: 0444
            sources:
              - serviceAccountToken:
                  expirationSeconds: 3607
                  path: token
              - configMap:
                  name: kube-root-ca.crt
                  items:
                    - key: ca.crt
                      path: ca.crt
              - downwardAPI:
                  items:
                    - path: namespace
                      fieldRef:
                        apiVersion: v1
                        fieldPath: metadata.namespace
      {{- end }}
{{- end -}}
{{- end -}}

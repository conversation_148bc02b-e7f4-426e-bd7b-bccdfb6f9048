{{- if .Values.webhooksCleanup.enabled -}}
{{- if not .Values.templating.enabled -}}
{{- $automountSAToken := .Values.admissionController.rbac.serviceAccount.automountServiceAccountToken }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "kyverno.fullname" . }}-scale-to-zero
  namespace: {{ template "kyverno.namespace" . }}
  labels:
    {{- include "kyverno.hooks.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: pre-delete
    helm.sh/hook-delete-policy: before-hook-creation,hook-succeeded,hook-failed
    helm.sh/hook-weight: "100"
spec:
  backoffLimit: 2
  template:
    {{- if or .Values.webhooksCleanup.podAnnotations .Values.webhooksCleanup.podLabels }}
    metadata:
      {{- with .Values.webhooksCleanup.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.webhooksCleanup.podLabels }}
      labels:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- end }}
    spec:
      serviceAccountName: {{ template "kyverno.admission-controller.serviceAccountName" . }}
      automountServiceAccountToken: {{ $automountSAToken }}
      {{- with .Values.webhooksCleanup.podSecurityContext }}
      securityContext:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      restartPolicy: Never
      {{- with .Values.webhooksCleanup.imagePullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- tpl (include "kyverno.sortedImagePullSecrets" .) $ | nindent 8 }}
      {{- end }}
      containers:
        - name: kubectl
          image: {{ (include "kyverno.image" (dict "globalRegistry" .Values.global.image.registry "image" .Values.webhooksCleanup.image "defaultTag" (default .Chart.AppVersion .Values.webhooksCleanup.image.tag))) | quote }}
          imagePullPolicy: {{ .Values.webhooksCleanup.image.pullPolicy }}
          command:
            - /bin/bash
            - '-c'
            - |-
              set -euo pipefail
              kubectl scale -n {{ template "kyverno.namespace" . }} deployment -l app.kubernetes.io/part-of={{ template "kyverno.fullname" . }} --replicas=0
              sleep 30
              kubectl delete validatingwebhookconfiguration -l webhook.kyverno.io/managed-by=kyverno
              kubectl delete mutatingwebhookconfiguration -l webhook.kyverno.io/managed-by=kyverno
          {{- with .Values.webhooksCleanup.resources }}
          resources:
            {{- tpl (toYaml .) $ | nindent 12 }}
          {{- end }}
          {{- with .Values.webhooksCleanup.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- if not $automountSAToken }}
          volumeMounts:
            - name: serviceaccount-token
              mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              readOnly: true
          {{- end }}
      {{- with .Values.webhooksCleanup.tolerations | default .Values.global.tolerations}}
      tolerations:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- with .Values.webhooksCleanup.nodeSelector | default .Values.global.nodeSelector }}
      nodeSelector:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      {{- if or .Values.webhooksCleanup.podAntiAffinity .Values.webhooksCleanup.podAffinity .Values.webhooksCleanup.nodeAffinity }}
      affinity:
        {{- with .Values.webhooksCleanup.podAntiAffinity }}
        podAntiAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
        {{- with .Values.webhooksCleanup.podAffinity }}
        podAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
        {{- with .Values.webhooksCleanup.nodeAffinity }}
        nodeAffinity:
          {{- tpl (toYaml .) $ | nindent 10 }}
        {{- end }}
      {{- end }}
      {{- if not $automountSAToken }}
      volumes:
        - name: serviceaccount-token
          projected:
            defaultMode: 0444
            sources:
              - serviceAccountToken:
                  expirationSeconds: 3607
                  path: token
              - configMap:
                  name: kube-root-ca.crt
                  items:
                    - key: ca.crt
                      path: ca.crt
              - downwardAPI:
                  items:
                    - path: namespace
                      fieldRef:
                        apiVersion: v1
                        fieldPath: metadata.namespace
      {{- end }}
{{- end -}}
{{- end -}}

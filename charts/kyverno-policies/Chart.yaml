apiVersion: v2
type: application
name: kyverno-policies
version: v0.0.0
appVersion: latest
icon: https://github.com/kyverno/kyverno/raw/main/img/logo.png
description: Kubernetes Pod Security Standards implemented as Kyverno policies
keywords:
  - kubernetes
  - nirmata
  - policy agent
  - validating webhook
  - admissions controller
home: https://kyverno.io/policies/
sources:
  - https://github.com/kyverno/policies
maintainers:
  - name: kyverno-maintainers
    email: <EMAIL>
kubeVersion: ">=1.25.0-0"
annotations:
  artifacthub.io/operator: "false"
  artifacthub.io/prerelease: "false"
  artifacthub.io/changes: |
    - kind: removed
      description: Remove spec.validationFailureAction field from policies as it is deprecated
    - kind: added
      description: Add spec.validate[*].failureAction field to policies
    - kind: fixed
      description: Fix the merging of policyExclude customizations to avoid wrong overrides
    - kind: added
      description: Add spec.validate[*].allowExistingViolations field to policies

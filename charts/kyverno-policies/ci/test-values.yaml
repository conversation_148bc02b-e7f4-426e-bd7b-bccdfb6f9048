podSecurityStandard: restricted
includeOtherPolicies:
- require-non-root-groups
includeRestrictedPolicies:
- require-run-as-non-root-user
validationFailureActionByPolicy:
  require-non-root-groups: enforce
validationFailureActionOverrides:
  all:
    - action: audit
      namespaces:
        - ingress-nginx
  disallow-host-path:
    - action: audit
      namespaces:
        - fluent
policyExclude:
  disallow-host-path:
    any:
      - resources:
          kinds:
            - Pod
          namespaces:
            - fluent
  require-non-root-groups:
    any:
      - resources:
          kinds:
            - Pod
          namespaces:
            - fluent

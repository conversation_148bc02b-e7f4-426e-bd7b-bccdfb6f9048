{{- $name := "require-non-root-groups" }}
{{- if eq (include "kyverno-policies.podSecurityOther" (merge (dict "name" $name) .)) "true" }}
apiVersion: kyverno.io/v1
kind: {{ .Values.policyKind }}
metadata:
  name: {{ $name }}
  annotations:
    {{- with .Values.autogenControllers }}
    pod-policies.kyverno.io/autogen-controllers: {{ . }}
    {{- end }}
    policies.kyverno.io/category: Sample
    {{- if .Values.podSecuritySeverity }}
    policies.kyverno.io/severity: {{ .Values.podSecuritySeverity | quote }}
    {{- end }}
    policies.kyverno.io/minversion: 1.3.6
    kyverno.io/kyverno-version: {{ default .Chart.AppVersion (include "kyverno-policies.kyvernoVersion" .) }}
    kyverno.io/kubernetes-version: "{{ default .Chart.KubeVersion .Values.kubeVersionOverride }}"
    policies.kyverno.io/subject: Pod
    policies.kyverno.io/description: >-
      Containers should be forbidden from running with a root primary or supplementary GID.
      This policy ensures the `runAsGroup`, `supplementalGroups`, and `fsGroup` fields are set to a number
      greater than zero (i.e., non root). A known issue prevents a policy such as this
      using `anyPattern` from being persisted properly in Kubernetes 1.23.0-1.23.2.
    {{- include "kyverno-policies.customAnnotations" . | nindent 4 }}
  labels: {{ include "kyverno-policies.labels" . | nindent 4 }}
spec:
  background: {{ .Values.background }}
  failurePolicy: {{ .Values.failurePolicy }}
  rules:
    - name: check-runasgroup
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "check-runasgroup") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with merge (index .Values "policyPreconditions" "check-runasgroup") (index .Values "policyPreconditions" $name) }}
      preconditions:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Running with root group IDs is disallowed. The fields
          spec.securityContext.runAsGroup, spec.containers[*].securityContext.runAsGroup,
          spec.initContainers[*].securityContext.runAsGroup, and
          spec.ephemeralContainers[*].securityContext.runAsGroup must be
          set to a value greater than zero.
        anyPattern:
        - spec:
            securityContext:
              runAsGroup: ">0"
            =(ephemeralContainers):
              - =(securityContext):
                  =(runAsGroup): ">0"
            =(initContainers):
              - =(securityContext):
                  =(runAsGroup): ">0"
            containers:
              - =(securityContext):
                  =(runAsGroup): ">0"
        - spec:
            =(ephemeralContainers):
              - securityContext:
                  runAsGroup: ">0"
            =(initContainers):
              - securityContext:
                  runAsGroup: ">0"
            containers:
              - securityContext:
                  runAsGroup: ">0"
    - name: check-supplementalgroups
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "check-supplementalgroups") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with merge (index .Values "policyPreconditions" "check-supplementalgroups") (index .Values "policyPreconditions" $name) }}
      preconditions:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Containers cannot run with a root primary or supplementary GID. The field
          spec.securityContext.supplementalGroups must be unset or
          set to a value greater than zero.
        pattern:
          spec:
            =(securityContext):
              =(supplementalGroups): ">0"
    - name: check-fsgroup
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "check-fsgroup") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Containers cannot run with a root primary or supplementary GID. The field
          spec.securityContext.fsGroup must be unset or set to a value greater than zero.
        pattern:
          spec:
            =(securityContext):
              =(fsGroup): ">0"
{{- end }}

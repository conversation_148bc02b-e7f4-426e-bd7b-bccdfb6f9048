{{- $name := "disallow-capabilities-strict" }}
{{- if eq (include "kyverno-policies.podSecurityRestricted" (merge (dict "name" $name) .)) "true" }}
{{- include "kyverno-policies.supportedKyvernoCheck" (dict "top" . "ver" ">= 1.6.0-0") }}
apiVersion: kyverno.io/v1
kind: {{ .Values.policyKind }}
metadata:
  name: {{ $name }}
  annotations:
    {{- with .Values.autogenControllers }}
    pod-policies.kyverno.io/autogen-controllers: {{ . }}
    {{- end }}
    policies.kyverno.io/title: Disallow Capabilities (Strict)
    policies.kyverno.io/category: Pod Security Standards (Restricted)
    {{- if .Values.podSecuritySeverity }}
    policies.kyverno.io/severity: {{ .Values.podSecuritySeverity | quote }}
    {{- end }}
    policies.kyverno.io/minversion: 1.6.0
    kyverno.io/kyverno-version: {{ default .Chart.AppVersion (include "kyverno-policies.kyvernoVersion" .) }}
    kyverno.io/kubernetes-version: "{{ default .Chart.KubeVersion .Values.kubeVersionOverride }}"
    policies.kyverno.io/subject: Pod
    policies.kyverno.io/description: >-
      Adding capabilities other than `NET_BIND_SERVICE` is disallowed. In addition,
      all containers must explicitly drop `ALL` capabilities.
    {{- include "kyverno-policies.customAnnotations" . | nindent 4 }}
  labels: {{ include "kyverno-policies.labels" . | nindent 4 }}
spec:
  background: {{ .Values.background }}
  failurePolicy: {{ .Values.failurePolicy }}
  rules:
    - name: require-drop-all
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "require-drop-all") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- $preconditions1 :=  merge (index .Values "policyPreconditions" "require-drop-all") (index .Values "policyPreconditions" $name) }}
      {{- if $preconditions1 }}
      {{- with $preconditions1 }}
      preconditions:
        {{- if .all }}
        all:
        - key: "{{`{{ request.operation || 'BACKGROUND' }}`}}"
          operator: NotEquals
          value: DELETE
        {{- toYaml .all | nindent 8 }}
        {{- else }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- else }}
      preconditions:
        all:
        - key: "{{`{{ request.operation || 'BACKGROUND' }}`}}"
          operator: NotEquals
          value: DELETE
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Containers must drop `ALL` capabilities.
        foreach:
          - list: request.object.spec.[ephemeralContainers, initContainers, containers][]
            deny:
              conditions:
                all:
                - key: ALL
                  operator: AnyNotIn
                  value: "{{`{{`}} element.securityContext.capabilities.drop[] || `[]` {{`}}`}}"
    - name: adding-capabilities-strict
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "adding-capabilities-strict") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- $preconditions2 := merge (index .Values "policyPreconditions" "adding-capabilities-strict") (index .Values "policyPreconditions" $name) }}
      {{- if $preconditions2 }}
      {{- with $preconditions2 }}
      preconditions:
        {{- if .all }}
        all:
        - key: "{{`{{ request.operation || 'BACKGROUND' }}`}}"
          operator: NotEquals
          value: DELETE
        {{- toYaml .all | nindent 8 }}
        {{- else }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- else }}
      preconditions:
        all:
        - key: "{{`{{ request.operation || 'BACKGROUND' }}`}}"
          operator: NotEquals
          value: DELETE
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Any capabilities added other than NET_BIND_SERVICE are disallowed.
        foreach:
          - list: request.object.spec.[ephemeralContainers, initContainers, containers][]
            deny:
              conditions:
                all:
                - key: "{{`{{`}} element.securityContext.capabilities.add[] || `[]` {{`}}`}}"
                  operator: AnyNotIn
                  value:
                  - NET_BIND_SERVICE
                  - ''
{{- end }}

{{- $name := "disallow-privilege-escalation" }}
{{- if eq (include "kyverno-policies.podSecurityRestricted" (merge (dict "name" $name) .)) "true" }}
apiVersion: kyverno.io/v1
kind: {{ .Values.policyKind }}
metadata:
  name: {{ $name }}
  annotations:
    {{- with .Values.autogenControllers }}
    pod-policies.kyverno.io/autogen-controllers: {{ . }}
    {{- end }}
    policies.kyverno.io/title: Disallow Privilege Escalation
    policies.kyverno.io/category: Pod Security Standards (Restricted)
    {{- if .Values.podSecuritySeverity }}
    policies.kyverno.io/severity: {{ .Values.podSecuritySeverity | quote }}
    {{- end }}
    policies.kyverno.io/subject: Pod
    kyverno.io/kyverno-version: {{ default .Chart.AppVersion (include "kyverno-policies.kyvernoVersion" .) }}
    kyverno.io/kubernetes-version: "{{ default .Chart.KubeVersion .Values.kubeVersionOverride }}"
    policies.kyverno.io/description: >-
      Privilege escalation, such as via set-user-ID or set-group-ID file mode, should not be allowed.
      This policy ensures the `allowPrivilegeEscalation` field is set to `false`.
    {{- include "kyverno-policies.customAnnotations" . | nindent 4 }}
  labels: {{ include "kyverno-policies.labels" . | nindent 4 }}
spec:
  background: {{ .Values.background }}
  failurePolicy: {{ .Values.failurePolicy }}
  rules:
    - name: privilege-escalation
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "privilege-escalation") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with index .Values "policyPreconditions" $name }}
      preconditions:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Privilege escalation is disallowed. The fields
          spec.containers[*].securityContext.allowPrivilegeEscalation,
          spec.initContainers[*].securityContext.allowPrivilegeEscalation,
          and spec.ephemeralContainers[*].securityContext.allowPrivilegeEscalation
          must be set to `false`.
        pattern:
          spec:
            =(ephemeralContainers):
            - securityContext:
                allowPrivilegeEscalation: "false"
            =(initContainers):
            - securityContext:
                allowPrivilegeEscalation: "false"
            containers:
            - securityContext:
                allowPrivilegeEscalation: "false"
{{- end }}

{{- $name := "restrict-volume-types" }}
{{- if eq (include "kyverno-policies.podSecurityRestricted" (merge (dict "name" $name) .)) "true" }}
{{- include "kyverno-policies.supportedKyvernoCheck" (dict "top" . "ver" ">= 1.6.0-0") }}
apiVersion: kyverno.io/v1
kind: {{ .Values.policyKind }}
metadata:
  name: {{ $name }}
  annotations:
    {{- with .Values.autogenControllers }}
    pod-policies.kyverno.io/autogen-controllers: {{ . }}
    {{- end }}
    policies.kyverno.io/title: Restrict Volume Types
    policies.kyverno.io/category: Pod Security Standards (Restricted)
    {{- if .Values.podSecuritySeverity }}
    policies.kyverno.io/severity: {{ .Values.podSecuritySeverity | quote }}
    {{- end }}
    policies.kyverno.io/subject: Pod,Volume
    policies.kyverno.io/minversion: 1.6.0
    kyverno.io/kubernetes-version: "{{ default .Chart.KubeVersion .Values.kubeVersionOverride }}"
    kyverno.io/kyverno-version: {{ default .Chart.AppVersion (include "kyverno-policies.kyvernoVersion" .) }}
    policies.kyverno.io/description: >-
      In addition to restricting HostPath volumes, the restricted pod security profile
      limits usage of non-core volume types to those defined through PersistentVolumes.
      This policy blocks any other type of volume other than those in the allow list.
    {{- include "kyverno-policies.customAnnotations" . | nindent 4 }}
  labels: {{ include "kyverno-policies.labels" . | nindent 4 }}
spec:
  background: {{ .Values.background }}
  failurePolicy: {{ .Values.failurePolicy }}
  rules:
    - name: restricted-volumes
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "restricted-volumes") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- $preconditions := index .Values "policyPreconditions" $name }}
      {{- if $preconditions }}
      {{- with $preconditions }}
      preconditions:
        {{- if .all }}
        all:
        - key: "{{`{{ request.operation || 'BACKGROUND' }}`}}"
          operator: NotEquals
          value: DELETE
        {{- toYaml .all | nindent 8 }}
        {{- else }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- else }}
      preconditions:
        all:
        - key: "{{`{{ request.operation || 'BACKGROUND' }}`}}"
          operator: NotEquals
          value: DELETE
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Only the following types of volumes may be used: configMap, csi, downwardAPI,
          emptyDir, ephemeral, persistentVolumeClaim, projected, and secret.
        deny:
          conditions:
            all:
            - key: "{{`{{ request.object.spec.volumes[].keys(@)[] || '' }}`}}"
              operator: AnyNotIn
              value:
              - name
              - configMap
              - csi
              - downwardAPI
              - emptyDir
              - ephemeral
              - persistentVolumeClaim
              - projected
              - secret
              - ''
{{- end }}

{{- $name := "restrict-seccomp-strict" }}
{{- if eq (include "kyverno-policies.podSecurityRestricted" (merge (dict "name" $name) .)) "true" }}
apiVersion: kyverno.io/v1
kind: {{ .Values.policyKind }}
metadata:
  name: {{ $name }}
  annotations:
    {{- with .Values.autogenControllers }}
    pod-policies.kyverno.io/autogen-controllers: {{ . }}
    {{- end }}
    policies.kyverno.io/title: Restrict Seccomp (Strict)
    policies.kyverno.io/category: Pod Security Standards (Restricted)
    {{- if .Values.podSecuritySeverity }}
    policies.kyverno.io/severity: {{ .Values.podSecuritySeverity | quote }}
    {{- end }}
    policies.kyverno.io/subject: Pod
    kyverno.io/kyverno-version: {{ default .Chart.AppVersion (include "kyverno-policies.kyvernoVersion" .) }}
    kyverno.io/kubernetes-version: "{{ default .Chart.KubeVersion .Values.kubeVersionOverride }}"
    policies.kyverno.io/description: >-
      The seccomp profile in the Restricted group must not be explicitly set to Unconfined
      but additionally must also not allow an unset value. This policy,
      requiring Kubernetes v1.19 or later, ensures that seccomp is
      set to `RuntimeDefault` or `Localhost`. A known issue prevents a policy such as this
      using `anyPattern` from being persisted properly in Kubernetes 1.23.0-1.23.2.
    {{- include "kyverno-policies.customAnnotations" . | nindent 4 }}
  labels: {{ include "kyverno-policies.labels" . | nindent 4 }}
spec:
  background: {{ .Values.background }}
  failurePolicy: {{ .Values.failurePolicy }}
  rules:
    - name: check-seccomp-strict
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "check-seccomp-strict") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with index .Values "policyPreconditions" $name }}
      preconditions:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Use of custom Seccomp profiles is disallowed. The fields
          spec.securityContext.seccompProfile.type,
          spec.containers[*].securityContext.seccompProfile.type,
          spec.initContainers[*].securityContext.seccompProfile.type, and
          spec.ephemeralContainers[*].securityContext.seccompProfile.type
          must be set to `RuntimeDefault` or `Localhost`.
        anyPattern:
        - spec:
            securityContext:
              seccompProfile:
                type: "RuntimeDefault | Localhost"
            =(ephemeralContainers):
            - =(securityContext):
                =(seccompProfile):
                  =(type): "RuntimeDefault | Localhost"
            =(initContainers):
            - =(securityContext):
                =(seccompProfile):
                  =(type): "RuntimeDefault | Localhost"
            containers:
            - =(securityContext):
                =(seccompProfile):
                  =(type): "RuntimeDefault | Localhost"
        - spec:
            =(ephemeralContainers):
            - securityContext:
                seccompProfile:
                  type: "RuntimeDefault | Localhost"
            =(initContainers):
            - securityContext:
                seccompProfile:
                  type: "RuntimeDefault | Localhost"
            containers:
            - securityContext:
                seccompProfile:
                  type: "RuntimeDefault | Localhost"
{{- end }}

{{- $name := "restrict-seccomp" }}
{{- if eq (include "kyverno-policies.podSecurityBaseline" (merge (dict "name" $name) .)) "true" }}
apiVersion: kyverno.io/v1
kind: {{ .Values.policyKind }}
metadata:
  name: {{ $name }}
  annotations:
    {{- with .Values.autogenControllers }}
    pod-policies.kyverno.io/autogen-controllers: {{ . }}
    {{- end }}
    policies.kyverno.io/title: Restrict Seccomp
    policies.kyverno.io/category: Pod Security Standards (Baseline)
    {{- if .Values.podSecuritySeverity }}
    policies.kyverno.io/severity: {{ .Values.podSecuritySeverity }}
    {{- end }}
    policies.kyverno.io/subject: Pod
    kyverno.io/kyverno-version: {{ default .Chart.AppVersion (include "kyverno-policies.kyvernoVersion" .) }}
    kyverno.io/kubernetes-version: "{{ default .Chart.KubeVersion .Values.kubeVersionOverride }}"
    policies.kyverno.io/description: >-
      The seccomp profile must not be explicitly set to Unconfined. This policy,
      requiring Kubernetes v1.19 or later, ensures that seccomp is unset or
      set to `RuntimeDefault` or `Localhost`.
    {{- include "kyverno-policies.customAnnotations" . | nindent 4 }}
  labels: {{ include "kyverno-policies.labels" . | nindent 4 }}
spec:
  background: {{ .Values.background }}
  failurePolicy: {{ .Values.failurePolicy }}
  rules:
    - name: check-seccomp
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "check-seccomp") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with index .Values "policyPreconditions" $name }}
      preconditions:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Use of custom Seccomp profiles is disallowed. The fields
          spec.securityContext.seccompProfile.type,
          spec.containers[*].securityContext.seccompProfile.type,
          spec.initContainers[*].securityContext.seccompProfile.type, and
          spec.ephemeralContainers[*].securityContext.seccompProfile.type
          must be unset or set to `RuntimeDefault` or `Localhost`.
        pattern:
          spec:
            =(securityContext):
              =(seccompProfile):
                =(type): "RuntimeDefault | Localhost"
            =(ephemeralContainers):
            - =(securityContext):
                =(seccompProfile):
                  =(type): "RuntimeDefault | Localhost"
            =(initContainers):
            - =(securityContext):
                =(seccompProfile):
                  =(type): "RuntimeDefault | Localhost"
            containers:
            - =(securityContext):
                =(seccompProfile):
                  =(type): "RuntimeDefault | Localhost"
{{- end }}

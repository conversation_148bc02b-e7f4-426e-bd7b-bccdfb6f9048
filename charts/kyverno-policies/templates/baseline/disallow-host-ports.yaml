{{- $name := "disallow-host-ports" }}
{{- if eq (include "kyverno-policies.podSecurityBaseline" (merge (dict "name" $name) .)) "true" }}
apiVersion: kyverno.io/v1
kind: {{ .Values.policyKind }}
metadata:
  name: {{ $name }}
  annotations:
    {{- with .Values.autogenControllers }}
    pod-policies.kyverno.io/autogen-controllers: {{ . }}
    {{- end }}
    policies.kyverno.io/title: Disallow hostPorts
    policies.kyverno.io/category: Pod Security Standards (Baseline)
    {{- if .Values.podSecuritySeverity }}
    policies.kyverno.io/severity: {{ .Values.podSecuritySeverity }}
    {{- end }}
    policies.kyverno.io/subject: Pod
    kyverno.io/kyverno-version: {{ default .Chart.AppVersion (include "kyverno-policies.kyvernoVersion" .) }}
    kyverno.io/kubernetes-version: "{{ default .Chart.KubeVersion .Values.kubeVersionOverride }}"
    policies.kyverno.io/description: >-
      Access to host ports allows potential snooping of network traffic and should not be
      allowed, or at minimum restricted to a known list. This policy ensures the `hostPort`
      field is unset or set to `0`.
    {{- include "kyverno-policies.customAnnotations" . | nindent 4 }}
  labels: {{ include "kyverno-policies.labels" . | nindent 4 }}
spec:
  background: {{ .Values.background }}
  failurePolicy: {{ .Values.failurePolicy }}
  rules:
    - name: host-ports-none
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "host-ports-none") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with index .Values "policyPreconditions" $name }}
      preconditions:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Use of host ports is disallowed. The fields spec.containers[*].ports[*].hostPort
          , spec.initContainers[*].ports[*].hostPort, and spec.ephemeralContainers[*].ports[*].hostPort
          must either be unset or set to `0`.
        pattern:
          spec:
            =(ephemeralContainers):
              - =(ports):
                  - =(hostPort): 0
            =(initContainers):
              - =(ports):
                  - =(hostPort): 0
            containers:
              - =(ports):
                  - =(hostPort): 0
{{- end }}

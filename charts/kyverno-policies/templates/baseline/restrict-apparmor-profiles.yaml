{{- $name := "restrict-apparmor-profiles" }}
{{- if eq (include "kyverno-policies.podSecurityBaseline" (merge (dict "name" $name) .)) "true" }}
apiVersion: kyverno.io/v1
kind: {{ .Values.policyKind }}
metadata:
  name: {{ $name }}
  annotations:
    {{- with .Values.autogenControllers }}
    pod-policies.kyverno.io/autogen-controllers: {{ . }}
    {{- end }}
    policies.kyverno.io/title: Restrict AppArmor
    policies.kyverno.io/category: Pod Security Standards (Baseline)
    {{- if .Values.podSecuritySeverity }}
    policies.kyverno.io/severity: {{ .Values.podSecuritySeverity }}
    {{- end }}
    policies.kyverno.io/subject: Pod, Annotation
    policies.kyverno.io/minversion: 1.3.0
    kyverno.io/kyverno-version: {{ default .Chart.AppVersion (include "kyverno-policies.kyvernoVersion" .) }}
    kyverno.io/kubernetes-version: "{{ default .Chart.KubeVersion .Values.kubeVersionOverride }}"
    policies.kyverno.io/description: >-
      On supported hosts, the 'runtime/default' AppArmor profile is applied by default.
      The default policy should prevent overriding or disabling the policy, or restrict
      overrides to an allowed set of profiles. This policy ensures Pods do not
      specify any other AppArmor profiles than `runtime/default` or `localhost/*`.
    {{- include "kyverno-policies.customAnnotations" . | nindent 4 }}
  labels: {{ include "kyverno-policies.labels" . | nindent 4 }}
spec:
  background: {{ .Values.background }}
  failurePolicy: {{ .Values.failurePolicy }}
  rules:
    - name: app-armor
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "app-armor") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with index .Values "policyPreconditions" $name }}
      preconditions:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Specifying other AppArmor profiles is disallowed. The annotation
          `container.apparmor.security.beta.kubernetes.io` if defined
          must not be set to anything other than `runtime/default` or `localhost/*`.
        pattern:
          =(metadata):
            =(annotations):
              =(container.apparmor.security.beta.kubernetes.io/*): "runtime/default | localhost/*"
{{- end }}

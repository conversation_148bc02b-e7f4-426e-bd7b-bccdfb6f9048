{{- $name := "disallow-capabilities" }}
{{- if eq (include "kyverno-policies.podSecurityBaseline" (merge (dict "name" $name) .)) "true" }}
{{- include "kyverno-policies.supportedKyvernoCheck" (dict "top" . "ver" ">= 1.6.0-0") }}
apiVersion: kyverno.io/v1
kind: {{ .Values.policyKind }}
metadata:
  name: {{ $name }}
  annotations:
    {{- with .Values.autogenControllers }}
    pod-policies.kyverno.io/autogen-controllers: {{ . }}
    {{- end }}
    policies.kyverno.io/title: Disallow Capabilities
    policies.kyverno.io/category: Pod Security Standards (Baseline)
    {{- if .Values.podSecuritySeverity }}
    policies.kyverno.io/severity: {{ .Values.podSecuritySeverity }}
    {{- end }}
    kyverno.io/kyverno-version: {{ default .Chart.AppVersion (include "kyverno-policies.kyvernoVersion" .) }}
    policies.kyverno.io/minversion: 1.6.0
    kyverno.io/kubernetes-version: "{{ default .Chart.KubeVersion .Values.kubeVersionOverride }}"
    policies.kyverno.io/subject: Pod
    policies.kyverno.io/description: >-
      Adding capabilities beyond those listed in the policy must be disallowed.
    {{- include "kyverno-policies.customAnnotations" . | nindent 4 }}
  labels: {{ include "kyverno-policies.labels" . | nindent 4 }}
spec:
  background: {{ .Values.background }}
  failurePolicy: {{ .Values.failurePolicy }}
  rules:
    - name: adding-capabilities
      match:
        any:
        - resources:
            kinds:
              - Pod
      {{- with merge (index .Values "policyExclude" "adding-capabilities") (index .Values "policyExclude" $name) }}
      exclude:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- $preconditions := index .Values "policyPreconditions" $name }}
      {{- if $preconditions }}
      {{- with $preconditions }}
      preconditions:
        {{- if .all }}
        all:
        - key: "{{`{{ request.operation || 'BACKGROUND' }}`}}"
          operator: NotEquals
          value: DELETE
        {{- toYaml .all | nindent 8 }}
        {{- else }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- else }}
      preconditions:
        all:
        - key: "{{`{{ request.operation || 'BACKGROUND' }}`}}"
          operator: NotEquals
          value: DELETE
      {{- end }}
      {{- if not (quote .Values.skipBackgroundRequests | empty)  }}
      skipBackgroundRequests: {{ .Values.skipBackgroundRequests }}
      {{- end }}
      validate:
        {{- with index .Values "validationFailureActionByPolicy" $name }}
        failureAction: {{ toYaml . }}
        {{- else }}
        failureAction: {{ .Values.validationFailureAction }}
        {{- end }}
        {{- with concat (index .Values "validationFailureActionOverrides" "all") (default list (index .Values "validationFailureActionOverrides" $name)) }}
        failureActionOverrides: {{ toYaml . | nindent 8 }}
        {{- end }}
        allowExistingViolations: {{ .Values.validationAllowExistingViolations }}
        message: >-
          Any capabilities added beyond the allowed list (AUDIT_WRITE, CHOWN, DAC_OVERRIDE, FOWNER,
          FSETID, KILL, MKNOD, NET_BIND_SERVICE, SETFCAP, SETGID, SETPCAP, SETUID, SYS_CHROOT)
          are disallowed.
        deny:
          conditions:
            all:
            - key: "{{`{{ request.object.spec.[ephemeralContainers, initContainers, containers][].securityContext.capabilities.add[] }}`}}"
              operator: AnyNotIn
              value:
              - AUDIT_WRITE
              - CHOWN
              - DAC_OVERRIDE
              - FOWNER
              - FSETID
              - KILL
              - MKNOD
              - NET_BIND_SERVICE
              - SETFCAP
              - SETGID
              - SETPCAP
              - SETUID
              - SYS_CHROOT
{{- end }}

# -- Policy kind (`ClusterPolicy`, `Policy`)
# Set to `Policy` if you need namespaced policies and not cluster policies
policyKind: ClusterPolicy

# -- Pod Security Standard profile (`baseline`, `restricted`, `privileged`, `custom`).
# For more info https://kyverno.io/policies/pod-security.
podSecurityStandard: baseline

# -- Pod Security Standard (`low`, `medium`, `high`).
podSecuritySeverity: medium

# -- Policies to include when `podSecurityStandard` is `custom`.
podSecurityPolicies: []

# -- Additional policies to include from `other`.
includeOtherPolicies: []
# - require-non-root-groups

# -- Additional policies to include from `restricted`.
includeRestrictedPolicies: []
# - require-run-as-non-root-user

# -- Additional custom policies to include.
customPolicies: []
# - apiVersion: kyverno.io/v1
#   kind: ClusterPolicy
#   metadata: # metadata
#   spec: # spec

# -- API server behavior if the webhook fails to respond ('Ignore', 'Fail')
# For more info: https://kyverno.io/docs/writing-policies/policy-settings/
failurePolicy: Fail

# -- Validation failure action (`Audit`, `Enforce`).
# For more info https://kyverno.io/docs/writing-policies/validate.
validationFailureAction: Audit

# -- Define validationFailureActionByPolicy for specific policies.
# Override the defined `validationFailureAction` with a individual validationFailureAction for individual Policies.
validationFailureActionByPolicy: {}
#  disallow-capabilities-strict: enforce
#  disallow-host-path: enforce
#  disallow-host-ports: enforce

# -- Define validationFailureActionOverrides for specific policies.
# The overrides for `all` will apply to all policies.
validationFailureActionOverrides:
  all: []
  # all:
  #   - action: audit
  #     namespaces:
  #       - ingress-nginx
  # disallow-host-path:
  #   - action: audit
  #     namespaces:
  #       - fluent

# -- Validate already existing resources.
# For more info https://kyverno.io/docs/policy-types/.
validationAllowExistingViolations: true

# -- Exclude resources from individual policies.
# Policies with multiple rules can have individual rules excluded by using the name of the rule as the key in the `policyExclude` map.
policyExclude: {}
  # # Exclude resources from individual policies
  # disallow-host-path:
  #   any:
  #   - resources:
  #       kinds:
  #       - Pod
  #       namespaces:
  #       - fluent
  # # Policies with multiple rules can have individual rules excluded
  # adding-capabilities-strict:
  #   any:
  #   - resources:
  #       kinds:
  #       - Pod
  #       namespaces:
  #       - kube-system
# -- Add preconditions to individual policies.
# Policies with multiple rules can have individual rules excluded by using the name of the rule as the key in the `policyPreconditions` map.
policyPreconditions: {}
  # # Exclude resources from individual policies
  # require-run-as-non-root-user:
  #   all:
  #   - key: "{{ request.object.metadata.name }}"
  #     operator: NotEquals
  #     value: "dcgm-exporter*"
  # # Policies with multiple rules can have individual rules excluded
  # require-drop-all:
  #   any:
  #   - key: "{{ request.object.metadata.name }}"
  #     operator: NotEquals
  #     value: "dcgm-exporter*"
  # adding-capabilities-strict:
  #   all:
  #   - key: "{{ request.object.metadata.name }}"
  #     operator: NotEquals
  #     value: "dcgm-exporter*"

# -- Customize the target Pod controllers for the auto-generated rules. (Eg. `none`, `Deployment`, `DaemonSet,Deployment,StatefulSet`)
# For more info https://kyverno.io/docs/writing-policies/autogen/.
autogenControllers: ""

# -- Name override.
nameOverride:

# -- Additional Annotations.
customAnnotations: {}

# -- Additional labels.
customLabels: {}

# -- Policies background mode
background: true

# -- (bool) SkipBackgroundRequests bypasses admission requests that are sent by the background controller
skipBackgroundRequests: ~

# -- Kyverno version
# The default of "autodetect" will try to determine the currently installed version from the deployment
kyvernoVersion: autodetect

# -- Kubernetes version override
# Override default value of kubeVersion set by release team taken from Chart.yaml with custom value. Ideally range of versions no more than two prior (ex., 1.28-1.31), must be enclosed in quotes.
kubeVersionOverride:

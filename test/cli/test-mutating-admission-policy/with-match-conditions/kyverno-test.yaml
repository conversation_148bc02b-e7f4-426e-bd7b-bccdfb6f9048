apiVersion: kyverno.io/v1
kind: Test
metadata:
  name: test
policies:
- policy.yaml
resources:
- resource.yaml
results:
- isMutatingAdmissionPolicy: true
  kind: ConfigMap
  patchedResources: patched-resource.yaml
  policy: add-label-to-configmap
  resources:
  - matched-cm
  result: pass
- isMutatingAdmissionPolicy: true
  kind: ConfigMap
  policy: add-label-to-configmap
  resources:
  - unmatched-cm
  result: skip

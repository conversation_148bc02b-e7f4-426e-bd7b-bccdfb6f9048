apiVersion: admissionregistration.k8s.io/v1alpha1
kind: MutatingAdmissionPolicy
metadata:
  name: "add-label-to-configmap"
spec:
  matchConstraints:
    resourceRules:
    - apiGroups:   [""]
      apiVersions: ["v1"]
      operations:  ["CREATE"]
      resources:   ["configmaps"]
  failurePolicy: Fail
  reinvocationPolicy: Never
  mutations:
    - patchType: "ApplyConfiguration"
      applyConfiguration:
        expression: >
          object.metadata.?labels["lfx-mentorship"].hasValue() ? 
              Object{} :
              Object{ metadata: Object.metadata{ labels: {"lfx-mentorship": "kyverno"}}}
---
apiVersion: admissionregistration.k8s.io/v1alpha1
kind: MutatingAdmissionPolicyBinding
metadata:
  name: "add-label-to-configmap-binding"
spec:
  policyName: "add-label-to-configmap"
  matchResources:
    namespaceSelector:
      matchExpressions:
      - key: environment
        operator: In
        values:
        - staging
        - production

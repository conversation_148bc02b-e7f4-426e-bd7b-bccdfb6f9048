apiVersion: policies.kyverno.io/v1alpha1
kind: MutatingPolicy
metadata:
  name: add-deployment-labels
spec:
  matchConstraints:
    resourceRules:
    - apiGroups: [ "apps" ]
      apiVersions: [ "v1" ]
      operations: [ "CREATE" ]
      resources: [ "deployments" ]
  mutations:
  - patchType: ApplyConfiguration
    applyConfiguration:
      expression: >
        Object{
          metadata: Object.metadata{
            labels: Object.metadata.labels{
              foo: "bar"
            }
          }
        }  
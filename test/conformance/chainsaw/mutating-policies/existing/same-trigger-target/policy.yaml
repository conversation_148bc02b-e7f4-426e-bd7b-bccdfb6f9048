apiVersion: policies.kyverno.io/v1alpha1
kind: MutatingPolicy
metadata:
  name: test-mpol-same-trigger-target
spec:
  failurePolicy: Fail
  evaluation:
    mutateExisting:
      enabled: true
  matchConstraints:
    resourceRules:
    - apiGroups: [ "" ]
      apiVersions: [ "v1" ]
      operations: [ "CREATE", "UPDATE"]
      resources: [ "namespaces" ]
  mutations:
  - patchType: ApplyConfiguration
    applyConfiguration:
      expression: >
        Object{
          metadata: Object.metadata{
            labels: Object.metadata.labels{
              foo: "bar"
            }
          }
        }
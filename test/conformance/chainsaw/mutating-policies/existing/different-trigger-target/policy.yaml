apiVersion: policies.kyverno.io/v1alpha1
kind: MutatingPolicy
metadata:
  name: test-mpol-different-trigger-target
spec:
  failurePolicy: Fail
  evaluation:
    mutateExisting:
      enabled: true
  matchConstraints:
    resourceRules:
    - apiGroups: [ "" ]
      apiVersions: [ "v1" ]
      operations: [ "CREATE", "UPDATE"]
      resources: [ "namespaces" ]
      resourceNames: ["test-mpol-different-trigger-target-ns"]
  targetMatchConstraints:
    namespaceSelector:
      matchLabels:
        test: "enabled"
    resourceRules:
    - apiGroups: [ "" ]
      apiVersions: [ "v1" ]
      operations: [ "CREATE", "UPDATE"]
      resources: [ "configmaps" ]
  mutations:
  - patchType: ApplyConfiguration
    applyConfiguration:
      expression: >
        Object{
          metadata: Object.metadata{
            labels: Object.metadata.labels{
              foo: "bar"
            }
          }
        } 
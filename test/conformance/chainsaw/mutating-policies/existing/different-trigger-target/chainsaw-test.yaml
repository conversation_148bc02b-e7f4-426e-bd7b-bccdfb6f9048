# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: different-trigger-target
spec:
  steps:
  - name: create namespace and configmaps
    try:
    - create:
        file: test-namespace.yaml
    - assert:
        file: test-namespace.yaml
    - create:
        file: configmaps.yaml
    - assert:
        file: configmaps.yaml
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: trigger mutation by updating namespace
    try:
    - apply:
        file: test-namespace-update.yaml
  - name: verify configmaps are mutated
    try:
    - assert:
        file: configmaps-assert.yaml 
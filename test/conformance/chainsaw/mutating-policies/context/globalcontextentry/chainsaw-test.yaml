apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: mpol-globalcontextentry
spec:
  steps:
  - try:
    - apply:
        file: configmap.yaml
    - apply:
        file: gctxentry.yaml
    - sleep:
        duration: 3s
  - name: create policy
    use:
      template: ../../../_step-templates/create-policy.yaml
      with:
        bindings:
        - name: file
          value: policy.yaml
  - name: wait-mutating-policy-ready
    use:
      template: ../../../_step-templates/mutating-policy-ready.yaml
      with:
        bindings:
        - name: name
          value: test-mpol-globalcontextentry
  - try:
    - sleep:
        duration: 3s
    - create:
        file: pod.yaml
    - assert:
        file: pod-assert.yaml
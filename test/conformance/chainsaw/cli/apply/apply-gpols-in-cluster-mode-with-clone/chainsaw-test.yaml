apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: apply-gpols-in-cluster-mode-with-clone
spec:
  steps:
  - name: step-01
    try:
    - script:
        content: kubectl create namespace test-ns-2
  - name: step-02
    try:
    - script:
        content: kubectl apply -f sources.yaml
  - name: step-03
    try:
    - script:
        content: kyverno apply policy.yaml --cluster
        check:
          (contains($stdout, 'Generation completed successfully.')): true


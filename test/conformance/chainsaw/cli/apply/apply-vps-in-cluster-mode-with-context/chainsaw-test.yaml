apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: apply-vps-in-cluster-mode-with-context
spec:
  steps:
  - name: step-01
    try:
    - script:
        content: kubectl apply -f configmap.yaml
  - name: step-02
    try:
    - script:
        content: kubectl apply -f pods.yaml
  - name: step-03
    try:
    - script:
        content: kyverno apply policy.yaml --cluster
        check:
          (contains($stdout, 'policy check-pod-name-from-configmap -> resource default/Pod/bad-pod failed')): true

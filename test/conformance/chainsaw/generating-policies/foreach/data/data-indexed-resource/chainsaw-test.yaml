# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: data-indexed-resource
spec:
  steps:
  - name: create namespaces
    try:
    - apply:
        file: namespaces.yaml
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create trigger
    try:
    - apply:
        file: trigger.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: check that the networkpolicy is generated in each namespace
    try:
    - assert:
        file: downstream-assert.yaml

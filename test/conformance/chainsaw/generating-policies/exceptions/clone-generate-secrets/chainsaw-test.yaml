# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: clone-generate-secrets-with-exceptions
spec:
  steps:
  - name: create permissions
    try:
    - apply:
        file: permissions.yaml
  - name: create exception
    try:
    - create:
        file: exception.yaml
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create the source
    try:
    - apply:
        file: source.yaml
  - name: create the triggers
    try:
    - apply:
        file: triggers.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: check that secrets are not generated in the excluded namespace
    try:
    - error:
        file: secrets-error.yaml
  - name: check that secrets are generated in the rest of the namespaces
    try:
    - assert:
        file: secrets-assert.yaml

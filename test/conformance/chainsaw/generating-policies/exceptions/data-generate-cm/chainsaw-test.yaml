# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: data-generate-cm-with-exceptions
spec:
  steps:
  - name: create exception
    try:
    - create:
        file: exception.yaml
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create triggers
    try:
    - apply:
        file: triggers.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: check that the configmap is not generated in the excluded namespace
    try:
    - error:
        file: configmap-error.yaml
  - name: check that the configmap is generated in the rest of the namespaces
    try:
    - assert:
        file: configmaps-assert.yaml


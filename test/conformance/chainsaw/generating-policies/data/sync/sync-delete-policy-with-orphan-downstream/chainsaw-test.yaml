# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: sync-delete-policy-with-orphan-downstream
spec:
  steps:
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create namespace
    try:
    - apply:
        file: namespace.yaml
  - name: check that the configmap is generated
    try:
    - assert:
        file: configmap-assert.yaml
  - name: delete the policy
    try:
    - delete:
        ref:
          apiVersion: policies.kyverno.io/v1alpha1
          kind: GeneratingPolicy
          name: sync-delete-policy-with-orphan-downstream
  - name: sleep
    try:
    - sleep:
        duration: 3s
  - name: check that the downstream still exists
    try:
    - assert:
        file: configmap-assert.yaml
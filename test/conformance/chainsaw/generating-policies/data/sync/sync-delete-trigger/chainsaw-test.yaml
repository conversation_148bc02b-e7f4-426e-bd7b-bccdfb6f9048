# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: data-sync-delete-trigger
spec:
  steps:
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create the trigger
    try:
    - apply:
        file: trigger.yaml
  - name: check that the downstream is generated
    try:
    - assert:
        file: downstream-assert.yaml
  - name: delete the trigger
    try:
    - delete:
        ref:
          apiVersion: v1
          kind: ConfigMap
          name: data-sync-delete-trigger
  - name: sleep
    try:
    - sleep:
        duration: 3s
  - name: check that the downstream is deleted
    try:
    - error:
        file: downstream-error.yaml
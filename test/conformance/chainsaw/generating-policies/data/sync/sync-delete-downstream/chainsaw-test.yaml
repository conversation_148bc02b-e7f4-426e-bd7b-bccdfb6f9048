# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: data-sync-delete-downstream
spec:
  steps:
  - name: create permissions
    try:
    - apply:
        file: permissions.yaml
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create the trigger
    try:
    - apply:
        file: namespace.yaml
  - name: check that the configmap is generated
    try:
    - assert:
        file: downstream-assert.yaml
  - name: delete the downstream
    try:
    - delete:
        ref:
          apiVersion: v1
          kind: ConfigMap
          name: data-sync-delete-downstream
          namespace: data-sync-delete-downstream
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: check that the downstream is re-generated
    try:
    - assert:
        file: downstream-assert.yaml

# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: sync-delete-source
spec:
  steps:
  - name: create permissions
    try:
    - apply:
        file: permissions.yaml
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create the source
    try:
    - apply:
        file: source.yaml
  - name: create the trigger
    try:
    - apply:
        file: trigger.yaml
  - name: check that the downstream is generated
    try:
    - assert:
        file: downstream-assert.yaml
  - name: delete the source
    try:
    - delete:
        ref:
          apiVersion: v1
          kind: Secret
          name: sync-delete-source
          namespace: default
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: check that the downstream is deleted
    try:
    - error:
        file: downstream-assert.yaml

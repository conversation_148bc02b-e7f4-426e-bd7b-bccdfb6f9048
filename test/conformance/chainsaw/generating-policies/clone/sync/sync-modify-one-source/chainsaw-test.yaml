# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: sync-modify-one-source
spec:
  steps:
  - name: create permissions
    try:
    - apply:
        file: permissions.yaml
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create the source
    try:
    - apply:
        file: source.yaml
  - name: create the trigger
    try:
    - apply:
        file: trigger.yaml
  - name: check that the downstreams are generated
    try:
    - assert:
        file: downstream1-assert.yaml
    - assert:
        file: downstream2-assert.yaml
    - assert:
        file: downstream3-assert.yaml
  - name: modify one of the sources
    try:
    - apply:
        file: source-modified.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: check that the corresponding downstream is updated
    try:
    - assert:
        file: downstream1-updated-assert.yaml
  - name: check that the other downstreams are not updated
    try:
    - assert:
        file: downstream2-assert.yaml
    - assert:
        file: downstream3-assert.yaml

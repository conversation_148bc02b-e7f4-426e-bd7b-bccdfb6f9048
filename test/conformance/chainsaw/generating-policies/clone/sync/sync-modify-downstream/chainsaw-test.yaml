# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: sync-modify-downstream
spec:
  steps:
  - name: create permissions
    try:
    - apply:
        file: permissions.yaml
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create the source
    try:
    - apply:
        file: source.yaml
  - name: create the trigger
    try:
    - apply:
        file: trigger.yaml
  - name: check that the downstream is generated
    try:
    - assert:
        file: downstream-assert.yaml
  - name: modify the downstream
    try:
    - apply:
        file: downstream-modified.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: check that the downstream is reverted
    try:
    - assert:
        file: downstream-assert.yaml

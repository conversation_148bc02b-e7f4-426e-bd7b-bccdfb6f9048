# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: clone-update-policy-to-disable-sync
spec:
  steps:
  - name: create permissions
    try:
    - apply:
        file: permissions.yaml
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create the source
    try:
    - apply:
        file: source.yaml
  - name: create the trigger
    try:
    - apply:
        file: namespace.yaml
  - name: check that the downstream is generated
    try:
    - assert:
        file: downstream-assert.yaml
  - name: modify the source
    try:
    - apply:
        file: source-modified.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: check that the downstream is updated
    try:
    - assert:
        file: updated-downstream-assert.yaml
  - name: update policy to disable sync
    try:
    - apply:
        file: policy-modified.yaml
  - name: delete the downstream
    try:
    - delete:
        ref:
          apiVersion: v1
          kind: Secret
          name: clone-update-policy-to-disable-sync
          namespace: clone-update-policy-to-disable-sync
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: check that the downstream is not re-generated
    try:
    - error:
        file: downstream-error.yaml

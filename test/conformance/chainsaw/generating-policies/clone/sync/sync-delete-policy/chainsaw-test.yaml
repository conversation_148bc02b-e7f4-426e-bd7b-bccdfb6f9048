# yaml-language-server: $schema=https://raw.githubusercontent.com/kyverno/chainsaw/main/.schemas/json/test-chainsaw-v1alpha1.json
apiVersion: chainsaw.kyverno.io/v1alpha1
kind: Test
metadata:
  name: sync-delete-policy
spec:
  steps:
  - name: create permissions
    try:
    - apply:
        file: permissions.yaml
  - name: create policy
    try:
    - create:
        file: policy.yaml
    - assert:
        file: policy-assert.yaml
  - name: sleep
    try:
    - sleep:
        duration: 5s
  - name: create the source
    try:
    - apply:
        file: source.yaml
  - name: create the trigger
    try:
    - apply:
        file: namespace.yaml
  - name: check that the secret is generated
    try:
    - assert:
        file: downstream-assert.yaml
  - name: delete the policy
    try:
    - delete:
        ref:
          apiVersion: policies.kyverno.io/v1alpha1
          kind: GeneratingPolicy
          name: generate-secret
  - name: sleep
    try:
    - sleep:
        duration: 3s
  - name: check that the downstream still exists
    try:
    - assert:
        file: downstream-assert.yaml

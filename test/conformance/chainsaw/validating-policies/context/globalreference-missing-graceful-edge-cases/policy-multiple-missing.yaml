apiVersion: policies.kyverno.io/v1alpha1
kind: ValidatingPolicy
metadata:
  name: vpol-multiple-missing-refs
spec:
  validationActions:
    - Audit
  matchConstraints:
    resourceRules:
      - apiGroups: [""]
        apiVersions: [v1]
        operations: [CREATE, UPDATE]
        resources: [pods]
  variables:
    - name: count1
      expression: >-
        globalContext.Get("missing-entry-1", "count") 
    - name: count2
      expression: >-
        globalContext.Get("missing-entry-2", "")
    - name: combinedCheck
      expression: >-
        globalContext.Get("missing-entry-1", "count") == null && 
        globalContext.Get("missing-entry-2", "") == null
  validations:
    - expression: >-
        variables.count1 == null
      message: "First missing reference handled gracefully"
    - expression: >-
        variables.count2 == null
      message: "Second missing reference handled gracefully"
    - expression: >-
        variables.combinedCheck == true
      message: "Combined null check works correctly" 
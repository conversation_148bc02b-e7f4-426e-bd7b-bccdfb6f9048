package common

const (
	GeneratePolicyLabel          = "generate.kyverno.io/policy-name"
	GeneratePolicyNamespaceLabel = "generate.kyverno.io/policy-namespace"
	GenerateRuleLabel            = "generate.kyverno.io/rule-name"
	GenerateTriggerNameLabel     = "generate.kyverno.io/trigger-name"
	GenerateTriggerUIDLabel      = "generate.kyverno.io/trigger-uid"
	GenerateTriggerNSLabel       = "generate.kyverno.io/trigger-namespace"
	GenerateTriggerKindLabel     = "generate.kyverno.io/trigger-kind"
	GenerateTriggerVersionLabel  = "generate.kyverno.io/trigger-version"
	GenerateTriggerGroupLabel    = "generate.kyverno.io/trigger-group"
	GenerateSourceNameLabel      = "generate.kyverno.io/source-name"
	GenerateSourceUIDLabel       = "generate.kyverno.io/source-uid"
	GenerateSourceNSLabel        = "generate.kyverno.io/source-namespace"
	GenerateSourceKindLabel      = "generate.kyverno.io/source-kind"
	GenerateSourceVersionLabel   = "generate.kyverno.io/source-version"
	GenerateSourceGroupLabel     = "generate.kyverno.io/source-group"
	GenerateTypeCloneSourceLabel = "generate.kyverno.io/clone-source"
)

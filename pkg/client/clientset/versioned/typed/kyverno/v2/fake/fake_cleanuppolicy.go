/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v2 "github.com/kyverno/kyverno/api/kyverno/v2"
	kyvernov2 "github.com/kyverno/kyverno/pkg/client/clientset/versioned/typed/kyverno/v2"
	gentype "k8s.io/client-go/gentype"
)

// fakeCleanupPolicies implements CleanupPolicyInterface
type fakeCleanupPolicies struct {
	*gentype.FakeClientWithList[*v2.CleanupPolicy, *v2.CleanupPolicyList]
	Fake *FakeKyvernoV2
}

func newFakeCleanupPolicies(fake *FakeKyvernoV2, namespace string) kyvernov2.CleanupPolicyInterface {
	return &fakeCleanupPolicies{
		gentype.NewFakeClientWithList[*v2.CleanupPolicy, *v2.CleanupPolicyList](
			fake.Fake,
			namespace,
			v2.SchemeGroupVersion.WithResource("cleanuppolicies"),
			v2.SchemeGroupVersion.WithKind("CleanupPolicy"),
			func() *v2.CleanupPolicy { return &v2.CleanupPolicy{} },
			func() *v2.CleanupPolicyList { return &v2.CleanupPolicyList{} },
			func(dst, src *v2.CleanupPolicyList) { dst.ListMeta = src.ListMeta },
			func(list *v2.CleanupPolicyList) []*v2.CleanupPolicy { return gentype.ToPointerSlice(list.Items) },
			func(list *v2.CleanupPolicyList, items []*v2.CleanupPolicy) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}

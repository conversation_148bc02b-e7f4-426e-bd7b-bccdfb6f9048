/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v2 "github.com/kyverno/kyverno/api/kyverno/v2"
	kyvernov2 "github.com/kyverno/kyverno/pkg/client/clientset/versioned/typed/kyverno/v2"
	gentype "k8s.io/client-go/gentype"
)

// fakePolicyExceptions implements PolicyExceptionInterface
type fakePolicyExceptions struct {
	*gentype.FakeClientWithList[*v2.PolicyException, *v2.PolicyExceptionList]
	Fake *FakeKyvernoV2
}

func newFakePolicyExceptions(fake *FakeKyvernoV2, namespace string) kyvernov2.PolicyExceptionInterface {
	return &fakePolicyExceptions{
		gentype.NewFakeClientWithList[*v2.PolicyException, *v2.PolicyExceptionList](
			fake.Fake,
			namespace,
			v2.SchemeGroupVersion.WithResource("policyexceptions"),
			v2.SchemeGroupVersion.WithKind("PolicyException"),
			func() *v2.PolicyException { return &v2.PolicyException{} },
			func() *v2.PolicyExceptionList { return &v2.PolicyExceptionList{} },
			func(dst, src *v2.PolicyExceptionList) { dst.ListMeta = src.ListMeta },
			func(list *v2.PolicyExceptionList) []*v2.PolicyException { return gentype.ToPointerSlice(list.Items) },
			func(list *v2.PolicyExceptionList, items []*v2.PolicyException) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}

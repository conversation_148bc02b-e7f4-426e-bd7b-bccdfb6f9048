/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v2 "github.com/kyverno/kyverno/api/kyverno/v2"
	kyvernov2 "github.com/kyverno/kyverno/pkg/client/clientset/versioned/typed/kyverno/v2"
	gentype "k8s.io/client-go/gentype"
)

// fakeUpdateRequests implements UpdateRequestInterface
type fakeUpdateRequests struct {
	*gentype.FakeClientWithList[*v2.UpdateRequest, *v2.UpdateRequestList]
	Fake *FakeKyvernoV2
}

func newFakeUpdateRequests(fake *FakeKyvernoV2, namespace string) kyvernov2.UpdateRequestInterface {
	return &fakeUpdateRequests{
		gentype.NewFakeClientWithList[*v2.UpdateRequest, *v2.UpdateRequestList](
			fake.Fake,
			namespace,
			v2.SchemeGroupVersion.WithResource("updaterequests"),
			v2.SchemeGroupVersion.WithKind("UpdateRequest"),
			func() *v2.UpdateRequest { return &v2.UpdateRequest{} },
			func() *v2.UpdateRequestList { return &v2.UpdateRequestList{} },
			func(dst, src *v2.UpdateRequestList) { dst.ListMeta = src.ListMeta },
			func(list *v2.UpdateRequestList) []*v2.UpdateRequest { return gentype.ToPointerSlice(list.Items) },
			func(list *v2.UpdateRequestList, items []*v2.UpdateRequest) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}

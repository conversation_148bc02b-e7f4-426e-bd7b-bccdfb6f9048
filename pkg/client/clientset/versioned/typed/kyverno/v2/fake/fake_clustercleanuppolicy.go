/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v2 "github.com/kyverno/kyverno/api/kyverno/v2"
	kyvernov2 "github.com/kyverno/kyverno/pkg/client/clientset/versioned/typed/kyverno/v2"
	gentype "k8s.io/client-go/gentype"
)

// fakeClusterCleanupPolicies implements ClusterCleanupPolicyInterface
type fakeClusterCleanupPolicies struct {
	*gentype.FakeClientWithList[*v2.ClusterCleanupPolicy, *v2.ClusterCleanupPolicyList]
	Fake *FakeKyvernoV2
}

func newFakeClusterCleanupPolicies(fake *FakeKyvernoV2) kyvernov2.ClusterCleanupPolicyInterface {
	return &fakeClusterCleanupPolicies{
		gentype.NewFakeClientWithList[*v2.ClusterCleanupPolicy, *v2.ClusterCleanupPolicyList](
			fake.Fake,
			"",
			v2.SchemeGroupVersion.WithResource("clustercleanuppolicies"),
			v2.SchemeGroupVersion.WithKind("ClusterCleanupPolicy"),
			func() *v2.ClusterCleanupPolicy { return &v2.ClusterCleanupPolicy{} },
			func() *v2.ClusterCleanupPolicyList { return &v2.ClusterCleanupPolicyList{} },
			func(dst, src *v2.ClusterCleanupPolicyList) { dst.ListMeta = src.ListMeta },
			func(list *v2.ClusterCleanupPolicyList) []*v2.ClusterCleanupPolicy {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v2.ClusterCleanupPolicyList, items []*v2.ClusterCleanupPolicy) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}

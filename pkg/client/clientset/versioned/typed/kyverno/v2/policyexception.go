/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v2

import (
	context "context"

	kyvernov2 "github.com/kyverno/kyverno/api/kyverno/v2"
	scheme "github.com/kyverno/kyverno/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// PolicyExceptionsGetter has a method to return a PolicyExceptionInterface.
// A group's client should implement this interface.
type PolicyExceptionsGetter interface {
	PolicyExceptions(namespace string) PolicyExceptionInterface
}

// PolicyExceptionInterface has methods to work with PolicyException resources.
type PolicyExceptionInterface interface {
	Create(ctx context.Context, policyException *kyvernov2.PolicyException, opts v1.CreateOptions) (*kyvernov2.PolicyException, error)
	Update(ctx context.Context, policyException *kyvernov2.PolicyException, opts v1.UpdateOptions) (*kyvernov2.PolicyException, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*kyvernov2.PolicyException, error)
	List(ctx context.Context, opts v1.ListOptions) (*kyvernov2.PolicyExceptionList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *kyvernov2.PolicyException, err error)
	PolicyExceptionExpansion
}

// policyExceptions implements PolicyExceptionInterface
type policyExceptions struct {
	*gentype.ClientWithList[*kyvernov2.PolicyException, *kyvernov2.PolicyExceptionList]
}

// newPolicyExceptions returns a PolicyExceptions
func newPolicyExceptions(c *KyvernoV2Client, namespace string) *policyExceptions {
	return &policyExceptions{
		gentype.NewClientWithList[*kyvernov2.PolicyException, *kyvernov2.PolicyExceptionList](
			"policyexceptions",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *kyvernov2.PolicyException { return &kyvernov2.PolicyException{} },
			func() *kyvernov2.PolicyExceptionList { return &kyvernov2.PolicyExceptionList{} },
		),
	}
}

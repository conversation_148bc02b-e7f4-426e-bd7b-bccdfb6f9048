/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v2

import (
	context "context"

	kyvernov2 "github.com/kyverno/kyverno/api/kyverno/v2"
	scheme "github.com/kyverno/kyverno/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// UpdateRequestsGetter has a method to return a UpdateRequestInterface.
// A group's client should implement this interface.
type UpdateRequestsGetter interface {
	UpdateRequests(namespace string) UpdateRequestInterface
}

// UpdateRequestInterface has methods to work with UpdateRequest resources.
type UpdateRequestInterface interface {
	Create(ctx context.Context, updateRequest *kyvernov2.UpdateRequest, opts v1.CreateOptions) (*kyvernov2.UpdateRequest, error)
	Update(ctx context.Context, updateRequest *kyvernov2.UpdateRequest, opts v1.UpdateOptions) (*kyvernov2.UpdateRequest, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, updateRequest *kyvernov2.UpdateRequest, opts v1.UpdateOptions) (*kyvernov2.UpdateRequest, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*kyvernov2.UpdateRequest, error)
	List(ctx context.Context, opts v1.ListOptions) (*kyvernov2.UpdateRequestList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *kyvernov2.UpdateRequest, err error)
	UpdateRequestExpansion
}

// updateRequests implements UpdateRequestInterface
type updateRequests struct {
	*gentype.ClientWithList[*kyvernov2.UpdateRequest, *kyvernov2.UpdateRequestList]
}

// newUpdateRequests returns a UpdateRequests
func newUpdateRequests(c *KyvernoV2Client, namespace string) *updateRequests {
	return &updateRequests{
		gentype.NewClientWithList[*kyvernov2.UpdateRequest, *kyvernov2.UpdateRequestList](
			"updaterequests",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *kyvernov2.UpdateRequest { return &kyvernov2.UpdateRequest{} },
			func() *kyvernov2.UpdateRequestList { return &kyvernov2.UpdateRequestList{} },
		),
	}
}

/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "github.com/kyverno/kyverno/api/kyverno/v1"
	kyvernov1 "github.com/kyverno/kyverno/pkg/client/clientset/versioned/typed/kyverno/v1"
	gentype "k8s.io/client-go/gentype"
)

// fakeClusterPolicies implements ClusterPolicyInterface
type fakeClusterPolicies struct {
	*gentype.FakeClientWithList[*v1.ClusterPolicy, *v1.ClusterPolicyList]
	Fake *FakeKyvernoV1
}

func newFakeClusterPolicies(fake *FakeKyvernoV1) kyvernov1.ClusterPolicyInterface {
	return &fakeClusterPolicies{
		gentype.NewFakeClientWithList[*v1.ClusterPolicy, *v1.ClusterPolicyList](
			fake.Fake,
			"",
			v1.SchemeGroupVersion.WithResource("clusterpolicies"),
			v1.SchemeGroupVersion.WithKind("ClusterPolicy"),
			func() *v1.ClusterPolicy { return &v1.ClusterPolicy{} },
			func() *v1.ClusterPolicyList { return &v1.ClusterPolicyList{} },
			func(dst, src *v1.ClusterPolicyList) { dst.ListMeta = src.ListMeta },
			func(list *v1.ClusterPolicyList) []*v1.ClusterPolicy { return gentype.ToPointerSlice(list.Items) },
			func(list *v1.ClusterPolicyList, items []*v1.ClusterPolicy) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}

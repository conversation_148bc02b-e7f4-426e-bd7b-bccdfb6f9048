/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "github.com/kyverno/kyverno/api/kyverno/v1"
	kyvernov1 "github.com/kyverno/kyverno/pkg/client/clientset/versioned/typed/kyverno/v1"
	gentype "k8s.io/client-go/gentype"
)

// fakePolicies implements PolicyInterface
type fakePolicies struct {
	*gentype.FakeClientWithList[*v1.Policy, *v1.PolicyList]
	Fake *FakeKyvernoV1
}

func newFakePolicies(fake *FakeKyvernoV1, namespace string) kyvernov1.PolicyInterface {
	return &fakePolicies{
		gentype.NewFakeClientWithList[*v1.Policy, *v1.PolicyList](
			fake.Fake,
			namespace,
			v1.SchemeGroupVersion.WithResource("policies"),
			v1.SchemeGroupVersion.WithKind("Policy"),
			func() *v1.Policy { return &v1.Policy{} },
			func() *v1.PolicyList { return &v1.PolicyList{} },
			func(dst, src *v1.PolicyList) { dst.ListMeta = src.ListMeta },
			func(list *v1.PolicyList) []*v1.Policy { return gentype.ToPointerSlice(list.Items) },
			func(list *v1.PolicyList, items []*v1.Policy) { list.Items = gentype.FromPointerSlice(items) },
		),
		fake,
	}
}

/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	context "context"

	kyvernov1 "github.com/kyverno/kyverno/api/kyverno/v1"
	scheme "github.com/kyverno/kyverno/pkg/client/clientset/versioned/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// ClusterPoliciesGetter has a method to return a ClusterPolicyInterface.
// A group's client should implement this interface.
type ClusterPoliciesGetter interface {
	ClusterPolicies() ClusterPolicyInterface
}

// ClusterPolicyInterface has methods to work with ClusterPolicy resources.
type ClusterPolicyInterface interface {
	Create(ctx context.Context, clusterPolicy *kyvernov1.ClusterPolicy, opts metav1.CreateOptions) (*kyvernov1.ClusterPolicy, error)
	Update(ctx context.Context, clusterPolicy *kyvernov1.ClusterPolicy, opts metav1.UpdateOptions) (*kyvernov1.ClusterPolicy, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, clusterPolicy *kyvernov1.ClusterPolicy, opts metav1.UpdateOptions) (*kyvernov1.ClusterPolicy, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*kyvernov1.ClusterPolicy, error)
	List(ctx context.Context, opts metav1.ListOptions) (*kyvernov1.ClusterPolicyList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *kyvernov1.ClusterPolicy, err error)
	ClusterPolicyExpansion
}

// clusterPolicies implements ClusterPolicyInterface
type clusterPolicies struct {
	*gentype.ClientWithList[*kyvernov1.ClusterPolicy, *kyvernov1.ClusterPolicyList]
}

// newClusterPolicies returns a ClusterPolicies
func newClusterPolicies(c *KyvernoV1Client) *clusterPolicies {
	return &clusterPolicies{
		gentype.NewClientWithList[*kyvernov1.ClusterPolicy, *kyvernov1.ClusterPolicyList](
			"clusterpolicies",
			c.RESTClient(),
			scheme.ParameterCodec,
			"",
			func() *kyvernov1.ClusterPolicy { return &kyvernov1.ClusterPolicy{} },
			func() *kyvernov1.ClusterPolicyList { return &kyvernov1.ClusterPolicyList{} },
		),
	}
}

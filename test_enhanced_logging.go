package main

import (
	"fmt"
	"os"

	"github.com/go-logr/logr"
	kyverno "github.com/kyverno/kyverno/api/kyverno/v1"
	"github.com/kyverno/kyverno/pkg/config"
	"github.com/kyverno/kyverno/pkg/engine/context"
	"github.com/kyverno/kyverno/pkg/engine/jmespath"
	"github.com/kyverno/kyverno/pkg/engine/variables"
	"github.com/kyverno/kyverno/pkg/logging"
)

func main() {
	// Setup logging to see the enhanced messages
	err := logging.Setup("text", "default", 3, false)
	if err != nil {
		fmt.Printf("Failed to setup logging: %v\n", err)
		os.Exit(1)
	}

	logger := logging.WithName("test-enhanced-logging")

	// Create a test resource
	resourceRaw := []byte(`
	{
		"metadata": {
			"name": "test-pod",
			"namespace": "default"
		},
		"spec": {
			"containers": [
				{
					"name": "test-container",
					"image": "nginx:latest"
				}
			]
		}
	}
	`)

	// Create context and add resource
	ctx := context.NewContext(jmespath.New(config.NewDefaultConfiguration(false)))
	err = context.AddResource(ctx, resourceRaw)
	if err != nil {
		fmt.Printf("Failed to add resource to context: %v\n", err)
		os.Exit(1)
	}

	// Create conditions that will fail to trigger the enhanced logging
	conditions := []kyverno.AnyAllConditions{
		{
			AnyConditions: []kyverno.Condition{
				{
					RawKey:   kyverno.ToJSON("{{request.object.metadata.name}}"),
					Operator: kyverno.ConditionOperators["Equal"],
					RawValue: kyverno.ToJSON("nonexistent-pod"),
					Message:  "pod name does not match",
				},
				{
					RawKey:   kyverno.ToJSON("{{request.object.spec.containers[0].image}}"),
					Operator: kyverno.ConditionOperators["Equal"],
					RawValue: kyverno.ToJSON("nonexistent:image"),
					Message:  "container image does not match",
				},
			},
		},
		{
			AnyConditions: []kyverno.Condition{
				{
					RawKey:   kyverno.ToJSON("{{request.object.metadata.namespace}}"),
					Operator: kyverno.ConditionOperators["Equal"],
					RawValue: kyverno.ToJSON("nonexistent-namespace"),
					Message:  "namespace does not match",
				},
			},
		},
	}

	fmt.Println("Testing enhanced logging for 'any' block failures...")
	fmt.Println("Expected to see enhanced log messages with context information.")
	fmt.Println()

	// Test with different context types
	contextTypes := []string{
		"precondition",
		"match condition",
		"deny condition",
		"background condition",
	}

	for _, contextType := range contextTypes {
		fmt.Printf("Testing with context type: %s\n", contextType)
		val, msg, err := variables.EvaluateAnyAllConditionsWithContext(logger, ctx, conditions, contextType)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
		} else {
			fmt.Printf("Result: %v, Message: %s\n", val, msg)
		}
		fmt.Println()
	}

	fmt.Println("Test completed. Check the logs above for enhanced context information.")
}

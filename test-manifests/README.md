# Enhanced Logging Test Manifests

This directory contains test manifests to demonstrate the enhanced logging feature for 'any' block condition failures.

## Files

- `policy-enhanced-logging-demo.yaml` - A comprehensive policy with multiple 'any' blocks
- `test-pod-failing.yaml` - A pod that will fail multiple conditions to trigger enhanced logging

## Expected Enhanced Log Output

When applying the failing pod against the policy, you should see enhanced log messages like:

### Before Enhancement (old logs):
```
no condition passed for 'any' block
```

### After Enhancement (new logs):
```
no condition passed for 'any' block for index '0' at 'precondition'
no condition passed for 'any' block for index '0' at 'deny condition'
```

## How to Test

### With Kyverno CLI:
```bash
# Test the policy against the failing pod
kyverno apply policy-enhanced-logging-demo.yaml --resource test-pod-failing.yaml --verbose

# Expected: Policy violations with enhanced context in logs
```

### In a Kubernetes Cluster:
```bash
# Apply the policy
kubectl apply -f policy-enhanced-logging-demo.yaml

# Try to apply the failing pod (should be rejected)
kubectl apply -f test-pod-failing.yaml

# Check Kyverno logs for enhanced messages
kubectl logs -n kyverno-system deployment/kyverno -f | grep "no condition passed"
```

## Expected Results

1. **Multiple Index Reporting**: You should see different index numbers when multiple 'any' blocks fail
2. **Context Information**: Logs should indicate whether failure was in 'precondition', 'deny condition', etc.
3. **Backward Compatibility**: Existing functionality should work unchanged

## Log Examples

### Enhanced Precondition Logging:
```
2025-07-17T10:00:00Z INFO no condition passed for 'any' block for index '0' at 'precondition' 
  any=[{"key":"{{ request.object.metadata.name }}","operator":"Equals","value":"allowed-pod-name","message":"Pod name must be 'allowed-pod-name'"},{"key":"{{ request.object.metadata.labels.environment || '' }}","operator":"Equals","value":"production","message":"Pod must have environment=production label"}]
  policy.name=enhanced-logging-demo
  rule.name=test-preconditions-logging
```

### Enhanced Deny Condition Logging:
```
2025-07-17T10:00:01Z INFO no condition passed for 'any' block for index '0' at 'deny condition'
  any=[{"key":"{{ request.object.spec.containers[?name=='nginx'].image || '' }}","operator":"Contains","value":"latest","message":"nginx container cannot use 'latest' tag"},{"key":"{{ request.object.spec.securityContext.runAsRoot || false }}","operator":"Equals","value":true,"message":"Pod cannot run as root"}]
  policy.name=enhanced-logging-demo
  rule.name=test-deny-conditions-logging
```

This demonstrates how the enhanced logging provides much better debugging context compared to the generic message.

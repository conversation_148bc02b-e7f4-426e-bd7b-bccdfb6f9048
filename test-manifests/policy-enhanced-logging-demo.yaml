apiVersion: kyverno.io/v1
kind: ClusterPolicy
metadata:
  name: enhanced-logging-demo
  annotations:
    policies.kyverno.io/title: Enhanced Logging Demo
    policies.kyverno.io/category: Other
    policies.kyverno.io/severity: medium
    policies.kyverno.io/subject: Pod
    policies.kyverno.io/description: >-
      This policy demonstrates the enhanced logging feature for 'any' block failures.
      It contains multiple 'any' blocks that will fail to show improved debugging context.
spec:
  validationFailureAction: Enforce
  background: false
  rules:
  - name: test-preconditions-logging
    match:
      any:
      - resources:
          kinds:
          - Pod
    preconditions:
      any:
      - key: "{{ request.object.metadata.name }}"
        operator: Equals
        value: "allowed-pod-name"
        message: "Pod name must be 'allowed-pod-name'"
      - key: "{{ request.object.metadata.labels.environment || '' }}"
        operator: Equals
        value: "production"
        message: "Pod must have environment=production label"
    validate:
      message: "Pod validation failed"
      pattern:
        metadata:
          name: "*"
  
  - name: test-deny-conditions-logging
    match:
      any:
      - resources:
          kinds:
          - Pod
    validate:
      deny:
        conditions:
          any:
          - key: "{{ request.object.spec.containers[?name=='nginx'].image || '' }}"
            operator: Contains
            value: "latest"
            message: "nginx container cannot use 'latest' tag"
          - key: "{{ request.object.spec.securityContext.runAsRoot || false }}"
            operator: Equals
            value: true
            message: "Pod cannot run as root"
      message: "Pod contains prohibited configurations"

  - name: test-multiple-any-blocks
    match:
      any:
      - resources:
          kinds:
          - Pod
    preconditions:
      any:
      - key: "{{ request.object.metadata.namespace }}"
        operator: Equals
        value: "allowed-namespace"
        message: "Must be in allowed-namespace"
      - key: "{{ request.object.metadata.labels.team || '' }}"
        operator: Equals
        value: "platform"
        message: "Must have team=platform label"
    validate:
      deny:
        conditions:
          any:
          - key: "{{ request.object.spec.containers[].resources.limits.memory || '' }}"
            operator: Equals
            value: ""
            message: "All containers must have memory limits"
          - key: "{{ request.object.spec.containers[].resources.limits.cpu || '' }}"
            operator: Equals
            value: ""
            message: "All containers must have CPU limits"
      message: "Resource limits are required"

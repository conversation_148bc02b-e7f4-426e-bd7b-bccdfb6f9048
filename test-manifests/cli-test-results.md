# Kyverno CLI Test Results

## Test Command
```bash
KYVERNO_NAMESPACE=kyverno ./cmd/kyverno/kyverno apply test-manifests/policy-enhanced-logging-demo.yaml --resource test-manifests/test-pod-failing.yaml --verbose
```

## Expected Behavior
The CLI should show policy violations with enhanced logging context when 'any' blocks fail.

## CLI Testing Notes
The enhanced logging feature primarily affects server-side logging during policy evaluation. The CLI will show policy violations, but the enhanced log messages with context information are most visible in:

1. **Kyverno server logs** when running in a cluster
2. **Unit test output** when running with verbose logging
3. **Development/debug scenarios** when log level is set to V(3) or higher

## Alternative Testing Methods

### 1. Unit Test Verification
```bash
go test -v ./pkg/engine/variables/ -run Test_Enhanced_Logging_Context
```

### 2. Manual Log Testing
```bash
# Run the verification script we created earlier
go run verify_solution.go
```

### 3. Integration Testing in Cluster
```bash
# Apply policy to cluster
kubectl apply -f test-manifests/policy-enhanced-logging-demo.yaml

# Try to apply failing resource
kubectl apply -f test-manifests/test-pod-failing.yaml

# Check Kyverno logs for enhanced messages
kubectl logs -n kyverno-system deployment/kyverno | grep "no condition passed"
```

## Expected Enhanced Log Output

When the policy evaluates the failing pod, you should see logs like:

```
2025-07-17T10:00:00Z INFO no condition passed for 'any' block for index '0' at 'precondition' 
  any=[{"key":"{{ request.object.metadata.name }}","operator":"Equals","value":"allowed-pod-name"}]
  policy.name=enhanced-logging-demo
  rule.name=test-preconditions-logging

2025-07-17T10:00:01Z INFO no condition passed for 'any' block for index '0' at 'deny condition'
  any=[{"key":"{{ request.object.spec.containers[?name=='nginx'].image || '' }}","operator":"Contains","value":"latest"}]
  policy.name=enhanced-logging-demo
  rule.name=test-deny-conditions-logging
```

This demonstrates the enhanced context compared to the old generic message:
```
no condition passed for 'any' block
```

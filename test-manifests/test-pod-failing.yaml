apiVersion: v1
kind: Pod
metadata:
  name: test-pod-failing
  namespace: default
  labels:
    app: test
    # Missing required labels that will trigger 'any' block failures
spec:
  containers:
  - name: nginx
    image: nginx:latest  # Will trigger deny condition
    # Missing resource limits - will trigger another deny condition
  - name: app
    image: myapp:1.0
    # Also missing resource limits
  securityContext:
    runAsUser: 0  # Will trigger runAsRoot deny condition

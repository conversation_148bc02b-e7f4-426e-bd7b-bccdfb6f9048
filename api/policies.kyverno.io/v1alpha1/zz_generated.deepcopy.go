//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by deepcopy-gen. DO NOT EDIT.

package v1alpha1

import (
	admissionregistrationv1 "k8s.io/api/admissionregistration/v1"
	admissionregistrationv1alpha1 "k8s.io/api/admissionregistration/v1alpha1"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AdmissionConfiguration) DeepCopyInto(out *AdmissionConfiguration) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AdmissionConfiguration.
func (in *AdmissionConfiguration) DeepCopy() *AdmissionConfiguration {
	if in == nil {
		return nil
	}
	out := new(AdmissionConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Attestation) DeepCopyInto(out *Attestation) {
	*out = *in
	if in.InToto != nil {
		in, out := &in.InToto, &out.InToto
		*out = new(InToto)
		**out = **in
	}
	if in.Referrer != nil {
		in, out := &in.Referrer, &out.Referrer
		*out = new(Referrer)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Attestation.
func (in *Attestation) DeepCopy() *Attestation {
	if in == nil {
		return nil
	}
	out := new(Attestation)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Attestor) DeepCopyInto(out *Attestor) {
	*out = *in
	if in.Cosign != nil {
		in, out := &in.Cosign, &out.Cosign
		*out = new(Cosign)
		(*in).DeepCopyInto(*out)
	}
	if in.Notary != nil {
		in, out := &in.Notary, &out.Notary
		*out = new(Notary)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Attestor.
func (in *Attestor) DeepCopy() *Attestor {
	if in == nil {
		return nil
	}
	out := new(Attestor)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackgroundConfiguration) DeepCopyInto(out *BackgroundConfiguration) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackgroundConfiguration.
func (in *BackgroundConfiguration) DeepCopy() *BackgroundConfiguration {
	if in == nil {
		return nil
	}
	out := new(BackgroundConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CTLog) DeepCopyInto(out *CTLog) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CTLog.
func (in *CTLog) DeepCopy() *CTLog {
	if in == nil {
		return nil
	}
	out := new(CTLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Certificate) DeepCopyInto(out *Certificate) {
	*out = *in
	if in.Certificate != nil {
		in, out := &in.Certificate, &out.Certificate
		*out = new(StringOrExpression)
		**out = **in
	}
	if in.CertificateChain != nil {
		in, out := &in.CertificateChain, &out.CertificateChain
		*out = new(StringOrExpression)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Certificate.
func (in *Certificate) DeepCopy() *Certificate {
	if in == nil {
		return nil
	}
	out := new(Certificate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConditionStatus) DeepCopyInto(out *ConditionStatus) {
	*out = *in
	if in.Ready != nil {
		in, out := &in.Ready, &out.Ready
		*out = new(bool)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConditionStatus.
func (in *ConditionStatus) DeepCopy() *ConditionStatus {
	if in == nil {
		return nil
	}
	out := new(ConditionStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Cosign) DeepCopyInto(out *Cosign) {
	*out = *in
	if in.Key != nil {
		in, out := &in.Key, &out.Key
		*out = new(Key)
		**out = **in
	}
	if in.Keyless != nil {
		in, out := &in.Keyless, &out.Keyless
		*out = new(Keyless)
		(*in).DeepCopyInto(*out)
	}
	if in.Certificate != nil {
		in, out := &in.Certificate, &out.Certificate
		*out = new(Certificate)
		(*in).DeepCopyInto(*out)
	}
	if in.Source != nil {
		in, out := &in.Source, &out.Source
		*out = new(Source)
		(*in).DeepCopyInto(*out)
	}
	if in.CTLog != nil {
		in, out := &in.CTLog, &out.CTLog
		*out = new(CTLog)
		**out = **in
	}
	if in.TUF != nil {
		in, out := &in.TUF, &out.TUF
		*out = new(TUF)
		**out = **in
	}
	if in.Annotations != nil {
		in, out := &in.Annotations, &out.Annotations
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Cosign.
func (in *Cosign) DeepCopy() *Cosign {
	if in == nil {
		return nil
	}
	out := new(Cosign)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Credentials) DeepCopyInto(out *Credentials) {
	*out = *in
	if in.Providers != nil {
		in, out := &in.Providers, &out.Providers
		*out = make([]CredentialsProvidersType, len(*in))
		copy(*out, *in)
	}
	if in.Secrets != nil {
		in, out := &in.Secrets, &out.Secrets
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Credentials.
func (in *Credentials) DeepCopy() *Credentials {
	if in == nil {
		return nil
	}
	out := new(Credentials)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DeletingPolicy) DeepCopyInto(out *DeletingPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeletingPolicy.
func (in *DeletingPolicy) DeepCopy() *DeletingPolicy {
	if in == nil {
		return nil
	}
	out := new(DeletingPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *DeletingPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DeletingPolicyList) DeepCopyInto(out *DeletingPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]DeletingPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeletingPolicyList.
func (in *DeletingPolicyList) DeepCopy() *DeletingPolicyList {
	if in == nil {
		return nil
	}
	out := new(DeletingPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *DeletingPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DeletingPolicySpec) DeepCopyInto(out *DeletingPolicySpec) {
	*out = *in
	if in.MatchConstraints != nil {
		in, out := &in.MatchConstraints, &out.MatchConstraints
		*out = new(admissionregistrationv1.MatchResources)
		(*in).DeepCopyInto(*out)
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]admissionregistrationv1.MatchCondition, len(*in))
		copy(*out, *in)
	}
	if in.Variables != nil {
		in, out := &in.Variables, &out.Variables
		*out = make([]admissionregistrationv1.Variable, len(*in))
		copy(*out, *in)
	}
	if in.DeletionPropagationPolicy != nil {
		in, out := &in.DeletionPropagationPolicy, &out.DeletionPropagationPolicy
		*out = new(v1.DeletionPropagation)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeletingPolicySpec.
func (in *DeletingPolicySpec) DeepCopy() *DeletingPolicySpec {
	if in == nil {
		return nil
	}
	out := new(DeletingPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DeletingPolicyStatus) DeepCopyInto(out *DeletingPolicyStatus) {
	*out = *in
	in.ConditionStatus.DeepCopyInto(&out.ConditionStatus)
	in.LastExecutionTime.DeepCopyInto(&out.LastExecutionTime)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeletingPolicyStatus.
func (in *DeletingPolicyStatus) DeepCopy() *DeletingPolicyStatus {
	if in == nil {
		return nil
	}
	out := new(DeletingPolicyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EvaluationConfiguration) DeepCopyInto(out *EvaluationConfiguration) {
	*out = *in
	if in.Admission != nil {
		in, out := &in.Admission, &out.Admission
		*out = new(AdmissionConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.Background != nil {
		in, out := &in.Background, &out.Background
		*out = new(BackgroundConfiguration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EvaluationConfiguration.
func (in *EvaluationConfiguration) DeepCopy() *EvaluationConfiguration {
	if in == nil {
		return nil
	}
	out := new(EvaluationConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GenerateExistingConfiguration) DeepCopyInto(out *GenerateExistingConfiguration) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GenerateExistingConfiguration.
func (in *GenerateExistingConfiguration) DeepCopy() *GenerateExistingConfiguration {
	if in == nil {
		return nil
	}
	out := new(GenerateExistingConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GeneratingPolicy) DeepCopyInto(out *GeneratingPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GeneratingPolicy.
func (in *GeneratingPolicy) DeepCopy() *GeneratingPolicy {
	if in == nil {
		return nil
	}
	out := new(GeneratingPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *GeneratingPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GeneratingPolicyEvaluationConfiguration) DeepCopyInto(out *GeneratingPolicyEvaluationConfiguration) {
	*out = *in
	if in.Admission != nil {
		in, out := &in.Admission, &out.Admission
		*out = new(AdmissionConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.GenerateExistingConfiguration != nil {
		in, out := &in.GenerateExistingConfiguration, &out.GenerateExistingConfiguration
		*out = new(GenerateExistingConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.SynchronizationConfiguration != nil {
		in, out := &in.SynchronizationConfiguration, &out.SynchronizationConfiguration
		*out = new(SynchronizationConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.OrphanDownstreamOnPolicyDelete != nil {
		in, out := &in.OrphanDownstreamOnPolicyDelete, &out.OrphanDownstreamOnPolicyDelete
		*out = new(OrphanDownstreamOnPolicyDeleteConfiguration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GeneratingPolicyEvaluationConfiguration.
func (in *GeneratingPolicyEvaluationConfiguration) DeepCopy() *GeneratingPolicyEvaluationConfiguration {
	if in == nil {
		return nil
	}
	out := new(GeneratingPolicyEvaluationConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GeneratingPolicyList) DeepCopyInto(out *GeneratingPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]GeneratingPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GeneratingPolicyList.
func (in *GeneratingPolicyList) DeepCopy() *GeneratingPolicyList {
	if in == nil {
		return nil
	}
	out := new(GeneratingPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *GeneratingPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GeneratingPolicySpec) DeepCopyInto(out *GeneratingPolicySpec) {
	*out = *in
	if in.MatchConstraints != nil {
		in, out := &in.MatchConstraints, &out.MatchConstraints
		*out = new(admissionregistrationv1.MatchResources)
		(*in).DeepCopyInto(*out)
	}
	if in.MatchConditions != nil {
		in, out := &in.MatchConditions, &out.MatchConditions
		*out = make([]admissionregistrationv1.MatchCondition, len(*in))
		copy(*out, *in)
	}
	if in.Variables != nil {
		in, out := &in.Variables, &out.Variables
		*out = make([]admissionregistrationv1.Variable, len(*in))
		copy(*out, *in)
	}
	if in.EvaluationConfiguration != nil {
		in, out := &in.EvaluationConfiguration, &out.EvaluationConfiguration
		*out = new(GeneratingPolicyEvaluationConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.WebhookConfiguration != nil {
		in, out := &in.WebhookConfiguration, &out.WebhookConfiguration
		*out = new(WebhookConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.Generation != nil {
		in, out := &in.Generation, &out.Generation
		*out = make([]Generation, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GeneratingPolicySpec.
func (in *GeneratingPolicySpec) DeepCopy() *GeneratingPolicySpec {
	if in == nil {
		return nil
	}
	out := new(GeneratingPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GeneratingPolicyStatus) DeepCopyInto(out *GeneratingPolicyStatus) {
	*out = *in
	in.ConditionStatus.DeepCopyInto(&out.ConditionStatus)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GeneratingPolicyStatus.
func (in *GeneratingPolicyStatus) DeepCopy() *GeneratingPolicyStatus {
	if in == nil {
		return nil
	}
	out := new(GeneratingPolicyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Generation) DeepCopyInto(out *Generation) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Generation.
func (in *Generation) DeepCopy() *Generation {
	if in == nil {
		return nil
	}
	out := new(Generation)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Identity) DeepCopyInto(out *Identity) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Identity.
func (in *Identity) DeepCopy() *Identity {
	if in == nil {
		return nil
	}
	out := new(Identity)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageExtractor) DeepCopyInto(out *ImageExtractor) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageExtractor.
func (in *ImageExtractor) DeepCopy() *ImageExtractor {
	if in == nil {
		return nil
	}
	out := new(ImageExtractor)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageValidatingPolicy) DeepCopyInto(out *ImageValidatingPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageValidatingPolicy.
func (in *ImageValidatingPolicy) DeepCopy() *ImageValidatingPolicy {
	if in == nil {
		return nil
	}
	out := new(ImageValidatingPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ImageValidatingPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageValidatingPolicyAutogen) DeepCopyInto(out *ImageValidatingPolicyAutogen) {
	*out = *in
	if in.Targets != nil {
		in, out := &in.Targets, &out.Targets
		*out = make([]Target, len(*in))
		copy(*out, *in)
	}
	if in.Spec != nil {
		in, out := &in.Spec, &out.Spec
		*out = new(ImageValidatingPolicySpec)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageValidatingPolicyAutogen.
func (in *ImageValidatingPolicyAutogen) DeepCopy() *ImageValidatingPolicyAutogen {
	if in == nil {
		return nil
	}
	out := new(ImageValidatingPolicyAutogen)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageValidatingPolicyAutogenConfiguration) DeepCopyInto(out *ImageValidatingPolicyAutogenConfiguration) {
	*out = *in
	if in.PodControllers != nil {
		in, out := &in.PodControllers, &out.PodControllers
		*out = new(PodControllersGenerationConfiguration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageValidatingPolicyAutogenConfiguration.
func (in *ImageValidatingPolicyAutogenConfiguration) DeepCopy() *ImageValidatingPolicyAutogenConfiguration {
	if in == nil {
		return nil
	}
	out := new(ImageValidatingPolicyAutogenConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageValidatingPolicyAutogenStatus) DeepCopyInto(out *ImageValidatingPolicyAutogenStatus) {
	*out = *in
	if in.Configs != nil {
		in, out := &in.Configs, &out.Configs
		*out = make(map[string]ImageValidatingPolicyAutogen, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageValidatingPolicyAutogenStatus.
func (in *ImageValidatingPolicyAutogenStatus) DeepCopy() *ImageValidatingPolicyAutogenStatus {
	if in == nil {
		return nil
	}
	out := new(ImageValidatingPolicyAutogenStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageValidatingPolicyList) DeepCopyInto(out *ImageValidatingPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ImageValidatingPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageValidatingPolicyList.
func (in *ImageValidatingPolicyList) DeepCopy() *ImageValidatingPolicyList {
	if in == nil {
		return nil
	}
	out := new(ImageValidatingPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ImageValidatingPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageValidatingPolicySpec) DeepCopyInto(out *ImageValidatingPolicySpec) {
	*out = *in
	if in.MatchConstraints != nil {
		in, out := &in.MatchConstraints, &out.MatchConstraints
		*out = new(admissionregistrationv1.MatchResources)
		(*in).DeepCopyInto(*out)
	}
	if in.FailurePolicy != nil {
		in, out := &in.FailurePolicy, &out.FailurePolicy
		*out = new(admissionregistrationv1.FailurePolicyType)
		**out = **in
	}
	if in.AuditAnnotations != nil {
		in, out := &in.AuditAnnotations, &out.AuditAnnotations
		*out = make([]admissionregistrationv1.AuditAnnotation, len(*in))
		copy(*out, *in)
	}
	if in.ValidationAction != nil {
		in, out := &in.ValidationAction, &out.ValidationAction
		*out = make([]admissionregistrationv1.ValidationAction, len(*in))
		copy(*out, *in)
	}
	if in.MatchConditions != nil {
		in, out := &in.MatchConditions, &out.MatchConditions
		*out = make([]admissionregistrationv1.MatchCondition, len(*in))
		copy(*out, *in)
	}
	if in.Variables != nil {
		in, out := &in.Variables, &out.Variables
		*out = make([]admissionregistrationv1.Variable, len(*in))
		copy(*out, *in)
	}
	in.ValidationConfigurations.DeepCopyInto(&out.ValidationConfigurations)
	if in.MatchImageReferences != nil {
		in, out := &in.MatchImageReferences, &out.MatchImageReferences
		*out = make([]MatchImageReference, len(*in))
		copy(*out, *in)
	}
	if in.Credentials != nil {
		in, out := &in.Credentials, &out.Credentials
		*out = new(Credentials)
		(*in).DeepCopyInto(*out)
	}
	if in.ImageExtractors != nil {
		in, out := &in.ImageExtractors, &out.ImageExtractors
		*out = make([]ImageExtractor, len(*in))
		copy(*out, *in)
	}
	if in.Attestors != nil {
		in, out := &in.Attestors, &out.Attestors
		*out = make([]Attestor, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Attestations != nil {
		in, out := &in.Attestations, &out.Attestations
		*out = make([]Attestation, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Validations != nil {
		in, out := &in.Validations, &out.Validations
		*out = make([]admissionregistrationv1.Validation, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.WebhookConfiguration != nil {
		in, out := &in.WebhookConfiguration, &out.WebhookConfiguration
		*out = new(WebhookConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.EvaluationConfiguration != nil {
		in, out := &in.EvaluationConfiguration, &out.EvaluationConfiguration
		*out = new(EvaluationConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.AutogenConfiguration != nil {
		in, out := &in.AutogenConfiguration, &out.AutogenConfiguration
		*out = new(ImageValidatingPolicyAutogenConfiguration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageValidatingPolicySpec.
func (in *ImageValidatingPolicySpec) DeepCopy() *ImageValidatingPolicySpec {
	if in == nil {
		return nil
	}
	out := new(ImageValidatingPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageValidatingPolicyStatus) DeepCopyInto(out *ImageValidatingPolicyStatus) {
	*out = *in
	in.ConditionStatus.DeepCopyInto(&out.ConditionStatus)
	in.Autogen.DeepCopyInto(&out.Autogen)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageValidatingPolicyStatus.
func (in *ImageValidatingPolicyStatus) DeepCopy() *ImageValidatingPolicyStatus {
	if in == nil {
		return nil
	}
	out := new(ImageValidatingPolicyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *InToto) DeepCopyInto(out *InToto) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new InToto.
func (in *InToto) DeepCopy() *InToto {
	if in == nil {
		return nil
	}
	out := new(InToto)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Key) DeepCopyInto(out *Key) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Key.
func (in *Key) DeepCopy() *Key {
	if in == nil {
		return nil
	}
	out := new(Key)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Keyless) DeepCopyInto(out *Keyless) {
	*out = *in
	if in.Identities != nil {
		in, out := &in.Identities, &out.Identities
		*out = make([]Identity, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Keyless.
func (in *Keyless) DeepCopy() *Keyless {
	if in == nil {
		return nil
	}
	out := new(Keyless)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MAPGenerationConfiguration) DeepCopyInto(out *MAPGenerationConfiguration) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MAPGenerationConfiguration.
func (in *MAPGenerationConfiguration) DeepCopy() *MAPGenerationConfiguration {
	if in == nil {
		return nil
	}
	out := new(MAPGenerationConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MatchImageReference) DeepCopyInto(out *MatchImageReference) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MatchImageReference.
func (in *MatchImageReference) DeepCopy() *MatchImageReference {
	if in == nil {
		return nil
	}
	out := new(MatchImageReference)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MutateExistingConfiguration) DeepCopyInto(out *MutateExistingConfiguration) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MutateExistingConfiguration.
func (in *MutateExistingConfiguration) DeepCopy() *MutateExistingConfiguration {
	if in == nil {
		return nil
	}
	out := new(MutateExistingConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MutatingPolicy) DeepCopyInto(out *MutatingPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MutatingPolicy.
func (in *MutatingPolicy) DeepCopy() *MutatingPolicy {
	if in == nil {
		return nil
	}
	out := new(MutatingPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *MutatingPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MutatingPolicyAutogen) DeepCopyInto(out *MutatingPolicyAutogen) {
	*out = *in
	if in.Targets != nil {
		in, out := &in.Targets, &out.Targets
		*out = make([]Target, len(*in))
		copy(*out, *in)
	}
	if in.Spec != nil {
		in, out := &in.Spec, &out.Spec
		*out = new(MutatingPolicySpec)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MutatingPolicyAutogen.
func (in *MutatingPolicyAutogen) DeepCopy() *MutatingPolicyAutogen {
	if in == nil {
		return nil
	}
	out := new(MutatingPolicyAutogen)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MutatingPolicyAutogenConfiguration) DeepCopyInto(out *MutatingPolicyAutogenConfiguration) {
	*out = *in
	if in.PodControllers != nil {
		in, out := &in.PodControllers, &out.PodControllers
		*out = new(PodControllersGenerationConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.MutatingAdmissionPolicy != nil {
		in, out := &in.MutatingAdmissionPolicy, &out.MutatingAdmissionPolicy
		*out = new(MAPGenerationConfiguration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MutatingPolicyAutogenConfiguration.
func (in *MutatingPolicyAutogenConfiguration) DeepCopy() *MutatingPolicyAutogenConfiguration {
	if in == nil {
		return nil
	}
	out := new(MutatingPolicyAutogenConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MutatingPolicyAutogenStatus) DeepCopyInto(out *MutatingPolicyAutogenStatus) {
	*out = *in
	if in.Configs != nil {
		in, out := &in.Configs, &out.Configs
		*out = make(map[string]MutatingPolicyAutogen, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MutatingPolicyAutogenStatus.
func (in *MutatingPolicyAutogenStatus) DeepCopy() *MutatingPolicyAutogenStatus {
	if in == nil {
		return nil
	}
	out := new(MutatingPolicyAutogenStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MutatingPolicyEvaluationConfiguration) DeepCopyInto(out *MutatingPolicyEvaluationConfiguration) {
	*out = *in
	if in.Admission != nil {
		in, out := &in.Admission, &out.Admission
		*out = new(AdmissionConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.MutateExistingConfiguration != nil {
		in, out := &in.MutateExistingConfiguration, &out.MutateExistingConfiguration
		*out = new(MutateExistingConfiguration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MutatingPolicyEvaluationConfiguration.
func (in *MutatingPolicyEvaluationConfiguration) DeepCopy() *MutatingPolicyEvaluationConfiguration {
	if in == nil {
		return nil
	}
	out := new(MutatingPolicyEvaluationConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MutatingPolicyList) DeepCopyInto(out *MutatingPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MutatingPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MutatingPolicyList.
func (in *MutatingPolicyList) DeepCopy() *MutatingPolicyList {
	if in == nil {
		return nil
	}
	out := new(MutatingPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *MutatingPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MutatingPolicySpec) DeepCopyInto(out *MutatingPolicySpec) {
	*out = *in
	if in.MatchConstraints != nil {
		in, out := &in.MatchConstraints, &out.MatchConstraints
		*out = new(admissionregistrationv1alpha1.MatchResources)
		(*in).DeepCopyInto(*out)
	}
	if in.FailurePolicy != nil {
		in, out := &in.FailurePolicy, &out.FailurePolicy
		*out = new(admissionregistrationv1alpha1.FailurePolicyType)
		**out = **in
	}
	if in.MatchConditions != nil {
		in, out := &in.MatchConditions, &out.MatchConditions
		*out = make([]admissionregistrationv1alpha1.MatchCondition, len(*in))
		copy(*out, *in)
	}
	if in.Variables != nil {
		in, out := &in.Variables, &out.Variables
		*out = make([]admissionregistrationv1alpha1.Variable, len(*in))
		copy(*out, *in)
	}
	if in.AutogenConfiguration != nil {
		in, out := &in.AutogenConfiguration, &out.AutogenConfiguration
		*out = new(MutatingPolicyAutogenConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.TargetMatchConstraints != nil {
		in, out := &in.TargetMatchConstraints, &out.TargetMatchConstraints
		*out = new(admissionregistrationv1alpha1.MatchResources)
		(*in).DeepCopyInto(*out)
	}
	if in.Mutations != nil {
		in, out := &in.Mutations, &out.Mutations
		*out = make([]admissionregistrationv1alpha1.Mutation, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.WebhookConfiguration != nil {
		in, out := &in.WebhookConfiguration, &out.WebhookConfiguration
		*out = new(WebhookConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.EvaluationConfiguration != nil {
		in, out := &in.EvaluationConfiguration, &out.EvaluationConfiguration
		*out = new(MutatingPolicyEvaluationConfiguration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MutatingPolicySpec.
func (in *MutatingPolicySpec) DeepCopy() *MutatingPolicySpec {
	if in == nil {
		return nil
	}
	out := new(MutatingPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MutatingPolicyStatus) DeepCopyInto(out *MutatingPolicyStatus) {
	*out = *in
	in.ConditionStatus.DeepCopyInto(&out.ConditionStatus)
	in.Autogen.DeepCopyInto(&out.Autogen)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MutatingPolicyStatus.
func (in *MutatingPolicyStatus) DeepCopy() *MutatingPolicyStatus {
	if in == nil {
		return nil
	}
	out := new(MutatingPolicyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Notary) DeepCopyInto(out *Notary) {
	*out = *in
	if in.Certs != nil {
		in, out := &in.Certs, &out.Certs
		*out = new(StringOrExpression)
		**out = **in
	}
	if in.TSACerts != nil {
		in, out := &in.TSACerts, &out.TSACerts
		*out = new(StringOrExpression)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Notary.
func (in *Notary) DeepCopy() *Notary {
	if in == nil {
		return nil
	}
	out := new(Notary)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OrphanDownstreamOnPolicyDeleteConfiguration) DeepCopyInto(out *OrphanDownstreamOnPolicyDeleteConfiguration) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OrphanDownstreamOnPolicyDeleteConfiguration.
func (in *OrphanDownstreamOnPolicyDeleteConfiguration) DeepCopy() *OrphanDownstreamOnPolicyDeleteConfiguration {
	if in == nil {
		return nil
	}
	out := new(OrphanDownstreamOnPolicyDeleteConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PodControllersGenerationConfiguration) DeepCopyInto(out *PodControllersGenerationConfiguration) {
	*out = *in
	if in.Controllers != nil {
		in, out := &in.Controllers, &out.Controllers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PodControllersGenerationConfiguration.
func (in *PodControllersGenerationConfiguration) DeepCopy() *PodControllersGenerationConfiguration {
	if in == nil {
		return nil
	}
	out := new(PodControllersGenerationConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PolicyException) DeepCopyInto(out *PolicyException) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PolicyException.
func (in *PolicyException) DeepCopy() *PolicyException {
	if in == nil {
		return nil
	}
	out := new(PolicyException)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *PolicyException) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PolicyExceptionList) DeepCopyInto(out *PolicyExceptionList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]PolicyException, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PolicyExceptionList.
func (in *PolicyExceptionList) DeepCopy() *PolicyExceptionList {
	if in == nil {
		return nil
	}
	out := new(PolicyExceptionList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *PolicyExceptionList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PolicyExceptionSpec) DeepCopyInto(out *PolicyExceptionSpec) {
	*out = *in
	if in.PolicyRefs != nil {
		in, out := &in.PolicyRefs, &out.PolicyRefs
		*out = make([]PolicyRef, len(*in))
		copy(*out, *in)
	}
	if in.MatchConditions != nil {
		in, out := &in.MatchConditions, &out.MatchConditions
		*out = make([]admissionregistrationv1.MatchCondition, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PolicyExceptionSpec.
func (in *PolicyExceptionSpec) DeepCopy() *PolicyExceptionSpec {
	if in == nil {
		return nil
	}
	out := new(PolicyExceptionSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PolicyRef) DeepCopyInto(out *PolicyRef) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PolicyRef.
func (in *PolicyRef) DeepCopy() *PolicyRef {
	if in == nil {
		return nil
	}
	out := new(PolicyRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Referrer) DeepCopyInto(out *Referrer) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Referrer.
func (in *Referrer) DeepCopy() *Referrer {
	if in == nil {
		return nil
	}
	out := new(Referrer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Source) DeepCopyInto(out *Source) {
	*out = *in
	if in.SignaturePullSecrets != nil {
		in, out := &in.SignaturePullSecrets, &out.SignaturePullSecrets
		*out = make([]corev1.LocalObjectReference, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Source.
func (in *Source) DeepCopy() *Source {
	if in == nil {
		return nil
	}
	out := new(Source)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StringOrExpression) DeepCopyInto(out *StringOrExpression) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StringOrExpression.
func (in *StringOrExpression) DeepCopy() *StringOrExpression {
	if in == nil {
		return nil
	}
	out := new(StringOrExpression)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SynchronizationConfiguration) DeepCopyInto(out *SynchronizationConfiguration) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SynchronizationConfiguration.
func (in *SynchronizationConfiguration) DeepCopy() *SynchronizationConfiguration {
	if in == nil {
		return nil
	}
	out := new(SynchronizationConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TUF) DeepCopyInto(out *TUF) {
	*out = *in
	out.Root = in.Root
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TUF.
func (in *TUF) DeepCopy() *TUF {
	if in == nil {
		return nil
	}
	out := new(TUF)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TUFRoot) DeepCopyInto(out *TUFRoot) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TUFRoot.
func (in *TUFRoot) DeepCopy() *TUFRoot {
	if in == nil {
		return nil
	}
	out := new(TUFRoot)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Target) DeepCopyInto(out *Target) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Target.
func (in *Target) DeepCopy() *Target {
	if in == nil {
		return nil
	}
	out := new(Target)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ValidatingPolicy) DeepCopyInto(out *ValidatingPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ValidatingPolicy.
func (in *ValidatingPolicy) DeepCopy() *ValidatingPolicy {
	if in == nil {
		return nil
	}
	out := new(ValidatingPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ValidatingPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ValidatingPolicyAutogen) DeepCopyInto(out *ValidatingPolicyAutogen) {
	*out = *in
	if in.Targets != nil {
		in, out := &in.Targets, &out.Targets
		*out = make([]Target, len(*in))
		copy(*out, *in)
	}
	if in.Spec != nil {
		in, out := &in.Spec, &out.Spec
		*out = new(ValidatingPolicySpec)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ValidatingPolicyAutogen.
func (in *ValidatingPolicyAutogen) DeepCopy() *ValidatingPolicyAutogen {
	if in == nil {
		return nil
	}
	out := new(ValidatingPolicyAutogen)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ValidatingPolicyAutogenConfiguration) DeepCopyInto(out *ValidatingPolicyAutogenConfiguration) {
	*out = *in
	if in.PodControllers != nil {
		in, out := &in.PodControllers, &out.PodControllers
		*out = new(PodControllersGenerationConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.ValidatingAdmissionPolicy != nil {
		in, out := &in.ValidatingAdmissionPolicy, &out.ValidatingAdmissionPolicy
		*out = new(VapGenerationConfiguration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ValidatingPolicyAutogenConfiguration.
func (in *ValidatingPolicyAutogenConfiguration) DeepCopy() *ValidatingPolicyAutogenConfiguration {
	if in == nil {
		return nil
	}
	out := new(ValidatingPolicyAutogenConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ValidatingPolicyAutogenStatus) DeepCopyInto(out *ValidatingPolicyAutogenStatus) {
	*out = *in
	if in.Configs != nil {
		in, out := &in.Configs, &out.Configs
		*out = make(map[string]ValidatingPolicyAutogen, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ValidatingPolicyAutogenStatus.
func (in *ValidatingPolicyAutogenStatus) DeepCopy() *ValidatingPolicyAutogenStatus {
	if in == nil {
		return nil
	}
	out := new(ValidatingPolicyAutogenStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ValidatingPolicyList) DeepCopyInto(out *ValidatingPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ValidatingPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ValidatingPolicyList.
func (in *ValidatingPolicyList) DeepCopy() *ValidatingPolicyList {
	if in == nil {
		return nil
	}
	out := new(ValidatingPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ValidatingPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ValidatingPolicySpec) DeepCopyInto(out *ValidatingPolicySpec) {
	*out = *in
	if in.MatchConstraints != nil {
		in, out := &in.MatchConstraints, &out.MatchConstraints
		*out = new(admissionregistrationv1.MatchResources)
		(*in).DeepCopyInto(*out)
	}
	if in.Validations != nil {
		in, out := &in.Validations, &out.Validations
		*out = make([]admissionregistrationv1.Validation, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.FailurePolicy != nil {
		in, out := &in.FailurePolicy, &out.FailurePolicy
		*out = new(admissionregistrationv1.FailurePolicyType)
		**out = **in
	}
	if in.AuditAnnotations != nil {
		in, out := &in.AuditAnnotations, &out.AuditAnnotations
		*out = make([]admissionregistrationv1.AuditAnnotation, len(*in))
		copy(*out, *in)
	}
	if in.MatchConditions != nil {
		in, out := &in.MatchConditions, &out.MatchConditions
		*out = make([]admissionregistrationv1.MatchCondition, len(*in))
		copy(*out, *in)
	}
	if in.Variables != nil {
		in, out := &in.Variables, &out.Variables
		*out = make([]admissionregistrationv1.Variable, len(*in))
		copy(*out, *in)
	}
	if in.AutogenConfiguration != nil {
		in, out := &in.AutogenConfiguration, &out.AutogenConfiguration
		*out = new(ValidatingPolicyAutogenConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.ValidationAction != nil {
		in, out := &in.ValidationAction, &out.ValidationAction
		*out = make([]admissionregistrationv1.ValidationAction, len(*in))
		copy(*out, *in)
	}
	if in.WebhookConfiguration != nil {
		in, out := &in.WebhookConfiguration, &out.WebhookConfiguration
		*out = new(WebhookConfiguration)
		(*in).DeepCopyInto(*out)
	}
	if in.EvaluationConfiguration != nil {
		in, out := &in.EvaluationConfiguration, &out.EvaluationConfiguration
		*out = new(EvaluationConfiguration)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ValidatingPolicySpec.
func (in *ValidatingPolicySpec) DeepCopy() *ValidatingPolicySpec {
	if in == nil {
		return nil
	}
	out := new(ValidatingPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ValidatingPolicyStatus) DeepCopyInto(out *ValidatingPolicyStatus) {
	*out = *in
	in.ConditionStatus.DeepCopyInto(&out.ConditionStatus)
	in.Autogen.DeepCopyInto(&out.Autogen)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ValidatingPolicyStatus.
func (in *ValidatingPolicyStatus) DeepCopy() *ValidatingPolicyStatus {
	if in == nil {
		return nil
	}
	out := new(ValidatingPolicyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ValidationConfiguration) DeepCopyInto(out *ValidationConfiguration) {
	*out = *in
	if in.MutateDigest != nil {
		in, out := &in.MutateDigest, &out.MutateDigest
		*out = new(bool)
		**out = **in
	}
	if in.VerifyDigest != nil {
		in, out := &in.VerifyDigest, &out.VerifyDigest
		*out = new(bool)
		**out = **in
	}
	if in.Required != nil {
		in, out := &in.Required, &out.Required
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ValidationConfiguration.
func (in *ValidationConfiguration) DeepCopy() *ValidationConfiguration {
	if in == nil {
		return nil
	}
	out := new(ValidationConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VapGenerationConfiguration) DeepCopyInto(out *VapGenerationConfiguration) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VapGenerationConfiguration.
func (in *VapGenerationConfiguration) DeepCopy() *VapGenerationConfiguration {
	if in == nil {
		return nil
	}
	out := new(VapGenerationConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WebhookConfiguration) DeepCopyInto(out *WebhookConfiguration) {
	*out = *in
	if in.TimeoutSeconds != nil {
		in, out := &in.TimeoutSeconds, &out.TimeoutSeconds
		*out = new(int32)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WebhookConfiguration.
func (in *WebhookConfiguration) DeepCopy() *WebhookConfiguration {
	if in == nil {
		return nil
	}
	out := new(WebhookConfiguration)
	in.DeepCopyInto(out)
	return out
}

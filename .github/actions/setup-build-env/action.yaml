name: Setup build env

description: Clone repo, unshallow, setup go, cache and install tools.

inputs:
  unshallow:
    description: git unshallow
    default: 'true'
  free-disk-space:
    description: free disk space
    default: 'true'

runs:
  using: composite
  steps:
    - uses: jlumbroso/free-disk-space@v1.3.1         # pin to the latest v1.3.1 release
      if: ${{ inputs.free-disk-space == 'true' }}
      with:
        android: true
        docker-images: false
        dotnet: true
        haskell: true
        large-packages: false
        swap-storage: false
        tool-cache: true
    - shell: bash
      if: ${{ inputs.unshallow == 'true' }}
      run: |
        git fetch --prune --unshallow
    - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version-file: go.mod
    - shell: bash
      run: |
        go mod download

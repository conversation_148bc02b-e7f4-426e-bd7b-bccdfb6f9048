name: Runs E2E Tests
description: Runs E2E tests using chainsaw
inputs:
  k8s-version:
    description: Kubernetes version
    required: true
  kind-config:
    description: Kind cluster config
    default: ./scripts/config/kind/default.yaml
  kyverno-configs:
    description: Kyverno configs
    default: standard
  token:
    description: GH token
    required: true
  chainsaw-config:
    description: Chainsaw config
    default: ../../../.chainsaw.yaml
  tests-path:
    description: Tests path
    default: '.'
  chainsaw-tests:
    description: Test regex
    default: ''
  shard-index:
    description: Shard index
    default: '0'
  shard-count:
    description: Shard count
    default: '0'
  upgrade-scenario:
    description: Determines which version to upgrade from
    required: true
runs:
  using: composite
  steps:
    # install tools
    - name: Install helm
      id: helm
      uses: azure/setup-helm@b9e51907a09c216f16ebe8536097933489208112 # v4.3.0
    - name: Install Cosign
      uses: sigstore/cosign-installer@398d4b0eeef1380460a10c8013a76f728fb906ac # v3.9.1
    - name: Install chainsaw
      uses: kyverno/action-install-chainsaw@f2b47b97dc889c12702113753d713f01ec268de5 # v0.2.12
      with:
        verify: true
    # create cluster
    - name: Create kind cluster
      uses: helm/kind-action@a1b0e391336a6ee6713a0583f8c6240d70863de3 # v1.12.0
      with:
        version: v0.29.0
        node_image: kindest/node:${{ inputs.k8s-version }}
        cluster_name: kind
        config: ${{ inputs.kind-config }}
    # deploy kyverno
    - name: Download kyverno images archive
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
      with:
        name: kyverno.tar
    - name: Load kyverno images archive in kind cluster
      shell: bash
      run: |
        set -e
        kind load image-archive kyverno.tar --name kind
    - name: Determine Previous Version
      id: determine-prev-version
      shell: bash
      run: |
        set -ex
        export HELM=${{ steps.helm.outputs.helm-path }}

        $HELM repo add kyverno https://kyverno.github.io/kyverno
        $HELM repo update

        case "${{ inputs.upgrade-scenario }}" in
          "patch")
            export INSTALL_VERSION=$($HELM search repo kyverno/kyverno --devel --versions | awk -F' +' '{print $2}' | awk '{$1=$1};1' | grep -E '^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[0-9]+)?)?$' | awk -F. '!seen[$1"."$2"."$3]++' | head -2 | tail -1)
            ;;
          "minor")
            export INSTALL_VERSION=$($HELM search repo kyverno/kyverno --devel --versions | awk -F' +' '{print $2}' | awk '{$1=$1};1' | grep -E '^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[0-9]+)?)?$' | awk -F. '!seen[$1"."$2]++' | head -2 | tail -1)
            ;;
          "latest")
            export INSTALL_VERSION=$($HELM search repo kyverno/kyverno --devel -o json | jq -r '.[0].version')
            ;;
          *)
            export INSTALL_VERSION=none
            ;;
        esac

        echo "::set-output name=prev_version::$INSTALL_VERSION"
    - name: Checkout Previous Version
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      if: ${{ steps.determine-prev-version.outputs.prev_version != 'none' }}
      with:
        repository: kyverno/kyverno
        ref: kyverno-chart-${{ steps.determine-prev-version.outputs.prev_version }}
        path: prev-version
    - name: Install Kyverno from prev-version
      if: ${{ steps.determine-prev-version.outputs.prev_version != 'none' }}
      shell: bash
      run: |
        set -ex
        export HELM=${{ steps.helm.outputs.helm-path }}
        export USE_CONFIG=${{ inputs.kyverno-configs }}

        cd prev-version

        IFS=',' read -ra CONFIGS <<< "$USE_CONFIG"
        for config in "${CONFIGS[@]}"; do
          if [ ! -f ./scripts/config/$config/kyverno.yaml ]; then
            echo "Skipping installation of Kyverno from prev-version for config: $config"
            exit 0
          fi
        done

        export INSTALL_VERSION=${{ steps.determine-prev-version.outputs.prev_version }}
        echo "Installing Kyverno using Helm with Chart version $INSTALL_VERSION"
        make -C .. kind-install-kyverno-from-repo
    - name: Determine Next Version
      id: determine-next-version
      shell: bash
      run: |
        set -ex
        export HELM=${{ steps.helm.outputs.helm-path }}

        $HELM repo add kyverno https://kyverno.github.io/kyverno
        $HELM repo update

        case "${{ inputs.upgrade-scenario }}" in
          "patch")
            export INSTALL_VERSION=$($HELM search repo kyverno/kyverno --devel --versions | awk -F' +' '{print $2}' | awk '{$1=$1};1' | grep -E '^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[0-9]+)?)?$' | awk -F. '!seen[$1"."$2"."$3]++' | head -1)
            ;;
          "minor")
            export INSTALL_VERSION=$($HELM search repo kyverno/kyverno --devel --versions | awk -F' +' '{print $2}' | awk '{$1=$1};1' | grep -E '^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[0-9]+)?)?$' | awk -F. '!seen[$1"."$2]++' | head -1)
            ;;
          "latest")
            export INSTALL_VERSION=main
            ;;
          *)
            export INSTALL_VERSION=main
            ;;
        esac

        echo "::set-output name=next_version::$INSTALL_VERSION"
    - name: Checkout Next Version
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      if: ${{ steps.determine-next-version.outputs.next_version != 'main' }}
      with:
        repository: kyverno/kyverno
        ref: kyverno-chart-${{ steps.determine-next-version.outputs.next_version }}
        path: next-version
    - name: Install Kyverno from next-version
      if: ${{ steps.determine-next-version.outputs.next_version != 'main' }}
      shell: bash
      run: |
        set -ex
        export HELM=${{ steps.helm.outputs.helm-path }}
        export USE_CONFIG=${{ inputs.kyverno-configs }}

        cd next-version

        IFS=',' read -ra CONFIGS <<< "$USE_CONFIG"
        for config in "${CONFIGS[@]}"; do
          if [ ! -f ./scripts/config/$config/kyverno.yaml ]; then
            echo "Skipping installation of Kyverno from prev-version for config: $config"
            exit 0
          fi
        done

        export INSTALL_VERSION=${{ steps.determine-next-version.outputs.next_version }}
        echo "Installing Kyverno using Helm with Chart version $INSTALL_VERSION"
        make -C .. kind-install-kyverno-from-repo
    - name: Install Kyverno from main
      if: ${{ steps.determine-next-version.outputs.next_version == 'main' }}
      shell: bash
      run: |
        set -ex
        export HELM=${{ steps.helm.outputs.helm-path }}
        export USE_CONFIG=${{ inputs.kyverno-configs }}

        echo "Installing Kyverno from main"
        make kind-install-kyverno
    - name: Wait for kyverno ready
      uses: ./.github/actions/kyverno-wait-ready
    # install kubectl-evict plugin needed for testing eviction subresource trigger
    - name: Install kubectl-evict
      shell: bash
      run: |
        set -e
        GOBIN=$(go env GOPATH)/bin
        go install github.com/ueokande/kubectl-evict@latest
        echo 'Adding kubectl-evict directory to PATH...'
        echo "${GOBIN}" >> "${GITHUB_PATH}"
    # run tests
    - name: Test with Chainsaw
      if: ${{ steps.determine-next-version.outputs.next_version != 'main' }}
      shell: bash
      env:
        GITHUB_TOKEN: ${{ inputs.token }}
      run: |
        set -e
        cd ./next-version/test/conformance/chainsaw
        chainsaw test ${{ inputs.tests-path }} \
          --config ../${{ inputs.chainsaw-config }} \
          --include-test-regex '^chainsaw$/${{ inputs.chainsaw-tests }}' \
          --shard-index ${{ inputs.shard-index }} \
          --shard-count ${{ inputs.shard-count }}
    - name: Test with Chainsaw
      if: ${{ steps.determine-next-version.outputs.next_version == 'main' }}
      shell: bash
      env:
        GITHUB_TOKEN: ${{ inputs.token }}
      run: |
        set -e
        cd ./test/conformance/chainsaw
        chainsaw test ${{ inputs.tests-path }} \
          --config ${{ inputs.chainsaw-config }} \
          --include-test-regex '^chainsaw$/${{ inputs.chainsaw-tests }}' \
          --shard-index ${{ inputs.shard-index }} \
          --shard-count ${{ inputs.shard-count }}
    # debug
    - name: Debug failure
      if: failure()
      uses: ./.github/actions/kyverno-logs

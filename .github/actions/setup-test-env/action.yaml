name: Setup test env

description: Create kind cluster, deploy kyverno, and wait pods are ready.

inputs:
  version:
    description: kubernetes version
    default: v1.30.0
  free-disk-space:
    description: free disk space
    default: 'false'

runs:
  using: composite
  steps:
    - uses: jlumbroso/free-disk-space@54081f138730dfa15788a46383842cd2f914a1be # v1.3.1
      if: ${{ inputs.free-disk-space == 'true' }}
      with:
        tool-cache: true
        android: true
        dotnet: true
        haskell: true
        large-packages: false
        swap-storage: false
    - shell: bash
      run: |
        export KIND_IMAGE=kindest/node:${{ inputs.version }}
        make kind-create-cluster kind-deploy-kyverno
    - uses: ./.github/actions/kyverno-wait-ready

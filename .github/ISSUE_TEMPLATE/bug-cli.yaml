name: "Bug Report: CLI"
description: Report a bug in the Kyverno CLI.
title: "[Bug] [CLI] "
labels: ["bug", "type:cli", "triage"]
body:
  - type: markdown
    attributes:
      value: |
        Please tell us about the bug, but before doing so ensure you have read the documentation on the CLI functionality found [here](https://kyverno.io/docs/kyverno-cli/).
  - type: dropdown
    id: kyverno-version
    attributes:
      label: Kyverno CLI Version
      description: What version of Kyverno are you running? For prior unsupported versions, please add the version in the description.
      options:
        - 1.11.0
        - 1.11.1
        - 1.11.2
        - 1.11.3
        - 1.11.4
        - 1.11.5
        - 1.12.0
        - 1.12.1
        - 1.12.2
        - 1.12.3
        - 1.12.4
        - 1.12.5
        - 1.12.6
        - 1.12.7
        - 1.13.0
        - 1.13.1
        - 1.13.2
        - 1.13.3
        - 1.13.4
        - 1.13.5
        - 1.13.6
        - 1.14.0
        - 1.14.1
    validations:
      required: true
  - type: textarea
    id: bug-description
    attributes:
      label: Description
      description: Describe what happened.
      # placeholder: Tell us what you see!
      # value: "asdf"
    validations:
      required: true
  - type: textarea
    id: bug-reproduce-steps
    attributes:
      label: Steps to reproduce
      description: >-
        What are the exact steps needed to reproduce the bug you experienced? Please provide any and all manifests needed to reproduce your issue, including Kyverno policies and test Kubernetes resources.
        GitHub supports [syntax highlighting](https://docs.github.com/en/github/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks#syntax-highlighting) with code blocks.
      # placeholder: Tell us what you see!
      value: |-
        1. 
    validations:
      required: true
  - type: textarea
    id: bug-expectations
    attributes:
      label: Expected behavior
      description: What did you expect to happen?
      # placeholder: Tell us what you see!
      # value: "asdf"
    validations:
      required: true
  - type: textarea
    id: bug-screenshots
    attributes:
      label: Screenshots
      description: >-
        If you have any screenshots that would help, please paste them below.
        GitHub allows you to copy-and-paste directly from the clipboard into the text area.
        **Please avoid taking screenshots of either log or terminal output**; paste any textual output in the logs section below.
      # placeholder: Tell us what you see!
      # value: "asdf"
    validations:
      required: false
  - type: textarea
    id: logs
    attributes:
      label: Kyverno logs
      description: >-
        Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
        Kyverno CLI logs may be found by passing the -v flag to any command.
      render: Shell
  - type: input
    id: slack
    attributes:
      label: Slack discussion
      description: >-
        If this issue is the result of a discussion thread on Slack, please provide the link to the discussion for reference.
      # placeholder: ex. <EMAIL>
    validations:
      required: false
  - type: checkboxes
    id: troubleshooting
    attributes:
      label: Troubleshooting
      description: >-
        By submitting this issue, you agree that you have performed some basic attempts at researching and solving your problem.
      options:
        - label: I have read and followed the [troubleshooting guide](https://kyverno.io/docs/troubleshooting/).
          required: true
        - label: I have searched other issues in this repository and mine is not recorded.
          required: true

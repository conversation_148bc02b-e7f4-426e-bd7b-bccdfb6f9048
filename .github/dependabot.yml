version: 2
updates:
  - package-ecosystem: gomod
    directories:
      - /
      - /hack/controller-gen/
      - /hack/api-group-resources/
    schedule:
      interval: daily
    rebase-strategy: disabled
    groups:
      kubernetes:
        patterns:
          - k8s.io/*
      sigstore:
        patterns:
          - github.com/sigstore/sigstore/*
      otel:
        patterns:
          - go.opentelemetry.io/*
  - package-ecosystem: github-actions
    directories:
      - /
      - /.github/actions/*/
    schedule:
      interval: daily
    rebase-strategy: disabled
  - package-ecosystem: docker
    directory: /.devcontainer
    schedule:
      interval: daily
    rebase-strategy: disabled

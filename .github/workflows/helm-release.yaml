# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: helm-release

permissions: {}

on:
  push:
    tags:
      - 'kyverno-chart-v*'
      - 'kyverno-policies-chart-v*'
      - 'kyverno-chart-*'
      - 'kyverno-policies-chart-*'

jobs:
  helm-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10
      - name: Setup python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: 3.13.1
      - name: Set up chart-testing
        uses: helm/chart-testing-action@0d28d3144d3a25ea2cc349d6e59901c4ff469b3b # v2.7.0
      - name: Run chart-testing (lint)
        run: ct lint --target-branch=main --check-version-increment=false --validate-maintainers=false

  linter-artifacthub:
    runs-on: ubuntu-latest
    container:
      image: artifacthub/ah
      options: --user root
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Run ah lint
        working-directory: ./charts/
        run: ah lint

  create-release:
    runs-on: ubuntu-latest
    needs: helm-tests
    permissions:
      contents: write
      packages: write
      id-token: write 
      pages: write
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10

      - name: Install Helm
        uses: azure/setup-helm@b9e51907a09c216f16ebe8536097933489208112 # v4.3.0
        with:
          version: v3.10.3

      - name: Install Cosign
        uses: sigstore/cosign-installer@fb28c2b6339dcd94da6e4cbcbc5e888961f6f8c3 # v3.9.0

      - name: Set version
        run: echo "RELEASE_VERSION=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV

      - name: Create charts tmp directory
        run: |
          mkdir charts-tmp
          if [[ "$RELEASE_VERSION" = "kyverno-policies-chart-v"* ]]; then
            cp -a charts/kyverno-policies charts-tmp/kyverno-policies
          fi
          if [[ "$RELEASE_VERSION" = "kyverno-chart-v"* ]]; then
            cp -a charts/kyverno charts-tmp/kyverno
          fi
          if [[ "$RELEASE_VERSION" = "kyverno-policies-chart-"* ]]; then
            cp -a charts/kyverno-policies charts-tmp/kyverno-policies
          fi
          if [[ "$RELEASE_VERSION" = "kyverno-chart-"* ]]; then
            cp -a charts/kyverno charts-tmp/kyverno
          fi

      - name: Run chart-releaser
        uses: stefanprodan/helm-gh-pages@0ad2bb377311d61ac04ad9eb6f252fb68e207260 #v1.7.0
        with:
          token: "${{ secrets.GITHUB_TOKEN }}"
          linting: off
          charts_dir: charts-tmp

      - name: Login to GitHub Container Registry
        run: |
          helm registry login --username ${GITHUB_ACTOR} --password ${{ secrets.GITHUB_TOKEN }} ghcr.io
      
      - name: Publish OCI Charts
        run: |
          for dir in `find charts-tmp -maxdepth 1 -mindepth 1 -type d -print`; do
            chart=${dir##*/}
            echo "Found chart: ${chart}"
            helm package charts-tmp/${chart} --destination .dist
            helm push .dist/${chart}-*.tgz oci://ghcr.io/${{ github.repository_owner }}/charts |& tee .digest
            cosign login --username ${GITHUB_ACTOR} --password ${{ secrets.GITHUB_TOKEN }} ghcr.io
            cosign sign --yes ghcr.io/${{ github.repository_owner }}/charts/${chart}@$(cat .digest | awk -F "[, ]+" '/Digest/{print $NF}')
          done

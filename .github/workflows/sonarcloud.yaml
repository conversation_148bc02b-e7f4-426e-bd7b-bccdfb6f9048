# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: Sonarcloud workflow

permissions: {}

on:
  push:
    branches:
      - main
      - release-*

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  sonarcloud:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Check secret
        id: checksecret
        uses: ./.github/actions/is-defined
        with:
          value: ${{ secrets.SONAR_TOKEN }}
      - name: Setup build env
        if: steps.checksecret.outputs.result == 'true'
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10
        with:
          free-disk-space: false
      - name: SonarCloud Scan
        if: steps.checksecret.outputs.result == 'true'
        uses: sonarsource/sonarcloud-github-action@ffc3010689be73b8e5ae0c57ce35968afd7909e8 # v5.0.0
        env:
          GITHUB_TOKEN: ${{ secrets.ACCESS_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

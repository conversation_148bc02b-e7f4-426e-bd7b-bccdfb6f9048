# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: <PERSON>

permissions: {}

on:
  workflow_dispatch:
  schedule:
    - cron: '23 2 * * *' # Every day at 02:23 UTC

    


jobs:
  nancy-scan:
    runs-on: ubuntu-latest
    name: nancy-scan
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10
      - name: WriteGoList
        run: go list -json -deps ./... > go.list
      - name: <PERSON>
        uses: sonatype-nexus-community/nancy-github-action@726e338312e68ecdd4b4195765f174d3b3ce1533 # v1.0.3
        with:
          output_format: json
          output-file: nancy-results.json
      - name: Parse scan results
        id: parse-results
        run: |
          if [ -s nancy-results.json ]; then
            echo "Vulnerabilities found, creating issue"
            echo "results=found" >> $GITHUB_OUTPUT
          else
            echo "No vulnerabilities found, halting"
            echo "results=nothing" >> $GITHUB_OUTPUT
          fi
      - name: Upload vulnerability scan report
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        if: steps.parse-results.outputs.results == 'found'
        with:
          name: nancy-results.json
          path: nancy-results.json
          if-no-files-found: error

  open-issue:
    runs-on: ubuntu-latest
    if: needs.nancy-scan.result == 'success'
    needs: nancy-scan
    permissions:
      issues: write
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Download scan results
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: nancy-results.json
      - name: Set scan output
        id: set-scan-output
        run: echo "results=$(cat nancy-results.json | jq -c)" >> $GITHUB_OUTPUT
      - uses: JasonEtco/create-an-issue@1b14a70e4d8dc185e5cc76d3bec9eab20257b2c5 # v2.9.2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          RESULTS: ${{ steps.set-scan-output.outputs.results }}
        with:
          filename: .github/ISSUE_TEMPLATE/VULN-TEMPLATE.md
        
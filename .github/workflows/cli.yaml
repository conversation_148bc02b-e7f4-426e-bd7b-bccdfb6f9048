# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: cli

permissions: {}

on:
  pull_request:
    branches:
      - main
      - release-*

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  cli-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10
      - name: Setup TEST_GIT_BRANCH
        run: |
          if [[ ${{ github.event_name }} == "push" ]]
          then
            echo "TEST_GIT_BRANCH=${GITHUB_REF##*/}" >> $GITHUB_ENV
          elif [[ ${{ github.event_name }} == "pull_request" ]]
          then
            echo "TEST_GIT_BRANCH=${{ github.event.pull_request.base.ref }}" >> $GITHUB_ENV
          fi
      - name: Test CLI
        run: |
          VERSION=${{ github.ref_name }} make test-cli
      - name: Test CLI (failures)
        run: |
          CLI_PATH=$PWD/cmd/cli/kubectl-kyverno/kubectl-kyverno
          $CLI_PATH test ./test/cli/test-fail/missing-policy && exit 1 || exit 0
          $CLI_PATH test ./test/cli/test-fail/missing-rule && exit 1 || exit 0
          $CLI_PATH test ./test/cli/test-fail/missing-resource && exit 1 || exit 0

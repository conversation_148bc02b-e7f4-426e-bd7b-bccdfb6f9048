# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: helm-test

permissions: {}

on:
  pull_request:
    branches:
      - main
      - release*
    paths:
      - charts/**
      - .github/workflows/helm-test.yaml

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  helm-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10
      - name: Setup python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: 3.13.1
      - name: Set up chart-testing
        uses: helm/chart-testing-action@0d28d3144d3a25ea2cc349d6e59901c4ff469b3b # v2.7.0
      - name: Run chart-testing (lint)
        run: |
          if [[ $(ct list-changed --target-branch=main) ]];
          then
            ct lint --target-branch=main --check-version-increment=false --validate-maintainers=false
          fi
      - name: Setup test env
        uses: ./.github/actions/setup-test-env
      - name: Helm test
        run: make helm-test
      - name: Debug failure
        if: failure()
        uses: ./.github/actions/kyverno-logs

  linter-artifacthub:
    runs-on: ubuntu-latest
    container:
      image: artifacthub/ah
      options: --user root
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Run ah lint
        working-directory: ./charts/
        run: ah lint

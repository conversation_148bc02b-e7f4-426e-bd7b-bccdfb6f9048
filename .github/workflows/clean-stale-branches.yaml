name: Cleanup Stale Branches

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * *' # This schedule runs the workflow at midnight every day

jobs:
  cleanup-stale-branches:
    runs-on: ubuntu-latest
    steps:
      - name: Cleanup Stale Branches
        uses: cbrgm/cleanup-stale-branches-action@b02c07aba8210792c7c306905e20d345034beaea # v1.1.31
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
          allowed-prefixes: "dependabot/,temp-cherry-pick-,cherry-pick-"
          last-commit-age-days: 7
          dry-run: false
          rate-limit: true

# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: Publish images

permissions: {}

on:
  push:
    branches:
      - main
      - release-*

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  publish-images:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      id-token: write 
    outputs:
      kyverno-digest: ${{ steps.publish-kyverno.outputs.digest }}
      kyverno-init-digest: ${{ steps.publish-kyverno-init.outputs.digest }}
      background-controller-digest: ${{ steps.publish-background-controller.outputs.digest }}
      cleanup-controller-digest: ${{ steps.publish-cleanup-controller.outputs.digest }}
      cli-digest: ${{ steps.publish-cli.outputs.digest }}
      reports-controller-digest: ${{ steps.publish-reports-controller.outputs.digest }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 30
      - name: Run Trivy vulnerability scanner in repo mode
        uses: aquasecurity/trivy-action@dc5a429b52fcf669ce959baa2c2dd26090d2a6c4 # v0.32.0
        with:
          scan-type: 'fs'
          ignore-unfixed: true
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
        env:
          # Trivy is returning TOOMANYREQUESTS
          # See: https://github.com/aquasecurity/trivy-action/issues/389#issuecomment-2385416577
          TRIVY_DB_REPOSITORY: 'public.ecr.aws/aquasecurity/trivy-db:2'
      - name: Install Cosign
        uses: sigstore/cosign-installer@fb28c2b6339dcd94da6e4cbcbc5e888961f6f8c3 # v3.9.0
      - name: Publish kyverno
        id: publish-kyverno
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-kyverno
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: kyverno
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/kyverno
      - name: Publish kyverno-init
        id: publish-kyverno-init
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-kyverno-init
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: kyverno-init
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/kyverno-init
      - name: Publish background-controller
        id: publish-background-controller
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-background-controller
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: background-controller
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/background-controller
      - name: Publish cleanup-controller
        id: publish-cleanup-controller
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-cleanup-controller
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: cleanup-controller
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/cleanup-controller
      - name: Publish cli
        id: publish-cli
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-cli
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: cli
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/cli/kubectl-kyverno
      - name: Publish reports-controller
        id: publish-reports-controller
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-reports-controller
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: reports-controller
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/reports-controller

  generate-kyverno-provenance:
    needs: publish-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/kyverno
      digest: "${{ needs.publish-images.outputs.kyverno-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  generate-kyverno-init-provenance:
    needs: publish-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/kyvernopre
      digest: "${{ needs.publish-images.outputs.kyverno-init-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  generate-background-controller-provenance:
    needs: publish-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/background-controller
      digest: "${{ needs.publish-images.outputs.background-controller-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  generate-cleanup-controller-provenance:
    needs: publish-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/cleanup-controller
      digest: "${{ needs.publish-images.outputs.cleanup-controller-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  generate-kyverno-cli-provenance:
    needs: publish-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/kyverno-cli
      digest: "${{ needs.publish-images.outputs.cli-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  generate-reports-controller-provenance:
    needs: publish-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/reports-controller
      digest: "${{ needs.publish-images.outputs.reports-controller-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: Check actions

permissions: {}

on:
  pull_request:
    branches:
      - main
      - release-*

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Ensure SHA pinned actions
        uses: zgosalvez/github-actions-ensure-sha-pinned-actions@fc87bb5b5a97953d987372e74478de634726b3e5 # v3.0.25
        with:
          # slsa-github-generator requires using a semver tag for reusable workflows. 
          # See: https://github.com/slsa-framework/slsa-github-generator#referencing-slsa-builders-and-generators
          allowlist: |
            slsa-framework/slsa-github-generator

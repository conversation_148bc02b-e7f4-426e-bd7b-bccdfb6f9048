# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: report-on-vulnerabilities

permissions: {}

on:
  workflow_dispatch: {}
  schedule:
    - cron: '23 2 * * *' # Every day at 02:23

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  scan:
    runs-on: ubuntu-latest
    outputs:
      results: ${{ steps.parse-results.outputs.results }}
    steps:    
    - name: Get Branches Name
      id: get-branches
      run: |
        all_branches=$(curl -s 'https://api.github.com/repos/${{ env.IMAGE_NAME }}/branches?per_page=100' | jq -r '.[].name | select(startswith("release-"))' | sort -rV | head -n 2)
        releasebranch1=$(echo "$all_branches" | sed -n 1p)
        releasebranch2=$(echo "$all_branches" | sed -n 2p)

        echo "releasebranch1=$releasebranch1" >> $GITHUB_OUTPUT
        echo "releasebranch2=$releasebranch2" >> $GITHUB_OUTPUT
    
    - name: Scan for vulnerabilities in latest image
      uses: aquasecurity/trivy-action@dc5a429b52fcf669ce959baa2c2dd26090d2a6c4 # v0.32.0
      with: 
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
        format: json
        ignore-unfixed: false
        severity: HIGH,CRITICAL
        output: scan1.json
      env:
        # Trivy is returning TOOMANYREQUESTS
        # See: https://github.com/aquasecurity/trivy-action/issues/389#issuecomment-2385416577
        TRIVY_DB_REPOSITORY: 'public.ecr.aws/aquasecurity/trivy-db:2'
    - name: Scan for vulnerabilities in latest-1 image
      uses: aquasecurity/trivy-action@dc5a429b52fcf669ce959baa2c2dd26090d2a6c4 # v0.32.0
      with: 
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.get-branches.outputs.releasebranch1 }}
        format: json
        ignore-unfixed: false
        severity: HIGH,CRITICAL
        output: scan2.json
      env:
        # Trivy is returning TOOMANYREQUESTS
        # See: https://github.com/aquasecurity/trivy-action/issues/389#issuecomment-2385416577
        TRIVY_DB_REPOSITORY: 'public.ecr.aws/aquasecurity/trivy-db:2'
    - name: Scan for vulnerabilities in latest-2 image
      uses: aquasecurity/trivy-action@dc5a429b52fcf669ce959baa2c2dd26090d2a6c4 # v0.32.0
      with: 
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.get-branches.outputs.releasebranch2 }}
        format: json
        ignore-unfixed: false
        severity: HIGH,CRITICAL
        output: scan3.json
      env:
        # Trivy is returning TOOMANYREQUESTS
        # See: https://github.com/aquasecurity/trivy-action/issues/389#issuecomment-2385416577
        TRIVY_DB_REPOSITORY: 'public.ecr.aws/aquasecurity/trivy-db:2'
    - name: Merge scan results
      id: merge-results
      run: |
        jq -s add scan1.json scan2.json scan3.json > scan.json
        cat scan.json

    - name: Parse scan results
      id: parse-results
      continue-on-error: true
      run: |
        VULNS=$(cat scan.json | jq '.Results[] | select(.Target=="ko-app/kyverno").Vulnerabilities | length')
        if [[ $VULNS -eq 0 ]]
        then
          echo "No vulnerabilities found, halting"
          echo "results=nothing" >> $GITHUB_OUTPUT
        else
          echo "Vulnerabilities found, creating issue"
          echo "results=found" >> $GITHUB_OUTPUT
        fi

    - name: Upload vulnerability scan report
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
      if: steps.parse-results.outputs.results == 'found'
      with:
        name: scan.json
        path: scan.json
        if-no-files-found: error

  open-issue:
    runs-on: ubuntu-latest
    if: needs.scan.outputs.results == 'found'
    needs: scan
    permissions:
      issues: write
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Download scan
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: scan.json
      - name: Set scan output
        id: set-scan-output
        run: echo "results=$(cat scan.json | jq -c)" >> $GITHUB_OUTPUT
      - uses: JasonEtco/create-an-issue@1b14a70e4d8dc185e5cc76d3bec9eab20257b2c5 # v2.9.2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          RESULTS: ${{ steps.set-scan-output.outputs.results }}
        with:
          filename: .github/ISSUE_TEMPLATE/VULN-TEMPLATE.md

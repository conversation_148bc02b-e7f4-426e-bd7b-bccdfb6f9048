# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: PR update

permissions: {}

on:
  push:
    branches:
      - main
      - release-*

jobs:
  autoupdate:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Check secret
        id: checksecret
        uses: ./.github/actions/is-defined
        with:
          value: ${{ secrets.PR_UPDATE_TOKEN }}
      - name: Automatically update PR
        if: steps.checksecret.outputs.result == 'true'
        uses: adRise/update-pr-branch@1982757e254dab9d5bbb8478b35a2b20411270d9 # v0.10.1
        with:
          token: ${{ secrets.PR_UPDATE_TOKEN }}
          base: ${{ github.ref_name }}
          require_auto_merge_enabled: true
          required_approval_count: 1
          require_passed_checks: false
          allow_ongoing_checks: true
          sort: created
          direction: desc

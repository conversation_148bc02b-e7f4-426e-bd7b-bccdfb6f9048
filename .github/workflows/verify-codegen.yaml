# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: Verify codegen

permissions: {}

on:
  pull_request:
    branches:
    - main
    - release-*

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  verify-codegen:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - name: Setup build env
      uses: ./.github/actions/setup-build-env
      timeout-minutes: 10
    - name: Create cluster
      run: make kind-create-cluster
    - name: Verify generated code is up to date
      run: make verify-codegen

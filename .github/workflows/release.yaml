# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: releaser

permissions: {}

on:
  push:
    tags:
      - v*

jobs:
  release-images:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      id-token: write
    outputs:
      kyverno-digest: ${{ steps.release-kyverno.outputs.digest }}
      kyverno-init-digest: ${{ steps.release-kyverno-init.outputs.digest }}
      background-controller-digest: ${{ steps.release-background-controller.outputs.digest }}
      cleanup-controller-digest: ${{ steps.release-cleanup-controller.outputs.digest }}
      cli-digest: ${{ steps.release-cli.outputs.digest }}
      reports-controller-digest: ${{ steps.release-reports-controller.outputs.digest }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 30
      - name: Run Trivy vulnerability scanner in repo mode
        uses: aquasecurity/trivy-action@dc5a429b52fcf669ce959baa2c2dd26090d2a6c4 # v0.32.0
        with:
          scan-type: 'fs'
          ignore-unfixed: true
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
        env:
          # Trivy is returning TOOMANYREQUESTS
          # See: https://github.com/aquasecurity/trivy-action/issues/389#issuecomment-2385416577
          TRIVY_DB_REPOSITORY: 'public.ecr.aws/aquasecurity/trivy-db:2'
      - name: Install Cosign
        uses: sigstore/cosign-installer@fb28c2b6339dcd94da6e4cbcbc5e888961f6f8c3 # v3.9.0
      - name: Publish kyverno
        id: release-kyverno
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-kyverno
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: kyverno
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/kyverno
      - name: Publish kyverno-init
        id: release-kyverno-init
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-kyverno-init
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: kyverno-init
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/kyverno-init
      - name: Publish background-controller
        id: release-background-controller
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-background-controller
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: background-controller
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/background-controller
      - name: Publish cleanup-controller
        id: release-cleanup-controller
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-cleanup-controller
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: cleanup-controller
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/cleanup-controller
      - name: Publish cli
        id: release-cli
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-cli
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: cli
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/cli/kubectl-kyverno
      - name: Publish reports-controller
        id: release-reports-controller
        uses: ./.github/actions/publish-image
        with:
          makefile-target: ko-publish-reports-controller
          registry: ghcr.io
          registry-username: ${{ github.actor }}
          registry-password: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository_owner }}
          version: ${{ github.ref_name }}
          sign-image: true
          sbom-name: reports-controller
          sbom-repository: ghcr.io/${{ github.repository_owner }}/sbom
          signature-repository: ghcr.io/${{ github.repository_owner }}/signatures
          main-path: ./cmd/reports-controller

  generate-kyverno-provenance:
    needs: release-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/kyverno
      digest: "${{ needs.release-images.outputs.kyverno-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  generate-kyverno-init-provenance:
    needs: release-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/kyvernopre
      digest: "${{ needs.release-images.outputs.kyverno-init-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  generate-background-controller-provenance:
    needs: release-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/background-controller
      digest: "${{ needs.release-images.outputs.background-controller-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  generate-cleanup-controller-provenance:
    needs: release-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/cleanup-controller
      digest: "${{ needs.release-images.outputs.cleanup-controller-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  generate-kyverno-cli-provenance:
    needs: release-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/kyverno-cli
      digest: "${{ needs.release-images.outputs.cli-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  generate-reports-controller-provenance:
    needs: release-images
    permissions:
      id-token: write   # To sign the provenance.
      packages: write   # To upload assets to release.
      actions: read     # To read the workflow path.
    # NOTE: The container generator workflow is not officially released as GA.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v2.1.0
    with:
      image: ghcr.io/${{ github.repository_owner }}/reports-controller
      digest: "${{ needs.release-images.outputs.reports-controller-digest }}"
      registry-username: ${{ github.actor }}
    secrets:
      registry-password: ${{ secrets.GITHUB_TOKEN }}

  create-release:
    runs-on: ubuntu-latest
    needs: release-images
    permissions:
      contents: write
      id-token: write
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 30
      - uses: creekorful/goreportcard-action@1f35ced8cdac2cba28c9a2f2288a16aacfd507f9 # v1.0
      - name: Install Cosign
        uses: sigstore/cosign-installer@fb28c2b6339dcd94da6e4cbcbc5e888961f6f8c3 # v3.9.0
      - name: Make Release
        env:
          VERSION: ${{ github.ref_name }}
        run: |
          rm -rf release
          mkdir release
          make release-notes > release/release-notes.out
          cat release/release-notes.out
      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@9c156ee8a17a598857849441385a2041ef570552 # v6.3.0
        with:
          distribution: goreleaser
          version: latest
          args: release --clean --timeout 90m --release-notes=release/release-notes.out
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  push-and-sign-install-manifest:
    runs-on: ubuntu-latest
    needs: create-release
    permissions:
      contents: write # needed to write releases
      id-token: write # needed for keyless signing
      packages: write # needed for ghcr access  
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10
      - name: Setup Flux CLI
        uses: fluxcd/flux2/action@6bf37f6a560fd84982d67f853162e4b3c2235edb # v2.6.4
        with:
          version: 0.35.0
      - name: Install Cosign
        uses: sigstore/cosign-installer@fb28c2b6339dcd94da6e4cbcbc5e888961f6f8c3 # v3.9.0
      - name: Build yaml manifest
        run: VERSION=${{ github.ref_name }} make codegen-manifest-release
      - name: Upload install manifest
        uses: svenstaro/upload-release-action@81c65b7cd4de9b2570615ce3aad67a41de5b1a13 # 2.11.2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: .manifest/release.yaml
          asset_name: install.yaml
          tag: ${{ github.ref }}
      - name: Upload CRD manifest
        uses: svenstaro/upload-release-action@81c65b7cd4de9b2570615ce3aad67a41de5b1a13 # 2.11.2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: config/crds/**/*.yaml
          file_glob: true
          tag: ${{ github.ref }}
      - name: Login to GHCR
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Push manifests to GHCR with Flux
        env:
          CR_PAT_ARTIFACTS:  ${{ secrets.GITHUB_TOKEN }}
        run: |
          set -e
          mkdir -p config/.release-manifests
          cp .manifest/release.yaml config/.release-manifests/install.yaml
          cd config/.release-manifests/ && \
          flux push artifact oci://ghcr.io/${{ github.repository_owner }}/manifests/kyverno:${{ github.ref_name }} \
            --path="." \
            --source="$(git config --get remote.origin.url)" \
            --revision="${{ github.ref_name }}/$(git rev-parse HEAD)"
      - name: Sign manifests in GHCR with Cosign
        run: |
          cosign sign --yes ghcr.io/${{ github.repository_owner }}/manifests/kyverno:${{ github.ref_name }}

  release-cli-via-krew:
    runs-on: ubuntu-latest
    needs: create-release
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10
      - name: Check Tag
        id: check-tag
        run: |
          if [[ ${{ github.event.ref }} =~ ^refs/tags/v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
              echo "match=true" >> $GITHUB_OUTPUT
          fi
      - name: Update new version in krew-index
        if: steps.check-tag.outputs.match == 'true'
        uses: rajatjindal/krew-release-bot@3d9faef30a82761d610544f62afddca00993eef9 # v0.0.47

# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: FOSSA

permissions: {}

on:
  push:
    branches: 
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  fossa-scan:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Check secret
        id: checksecret
        uses: ./.github/actions/is-defined
        with:
          value: ${{ secrets.FOSSA_API_KEY }}
      - name: Setup build env
        if: steps.checksecret.outputs.result == 'true'
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10
        with:
          free-disk-space: false
      - name: Run FOSSA analysis
        if: steps.checksecret.outputs.result == 'true'
        uses: fossas/fossa-action@3ebcea1862c6ffbd5cf1b4d0bd6b3fe7bd6f2cac # v1.7.0
        with:
          api-key: ${{ secrets.FOSSA_API_KEY }}

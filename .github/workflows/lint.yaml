# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: Lint

permissions: {}

on:
  pull_request:
    branches:
      - main
      - release-*

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10
      - name: golangci-lint
        uses: golangci/golangci-lint-action@55c2c1448f86e01eaae002a5a3a9624417608d84 # v6.5.2
        with:
          version: v1.64
          skip-cache: true
      - name: go fmt check
        run: make fmt-check
      - name: goimports check
        run: make imports-check
      - name: Checking unused pkgs using go mod tidy
        run: make unused-package-check
      - name: Go vet
        run: make vet

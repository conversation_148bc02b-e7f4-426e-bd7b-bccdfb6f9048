name: check-milestone-label

permissions: {}

on:
  pull_request:
    branches:
    - main
    types:
    - opened
    - reopened
    - synchronize
    - labeled
    - unlabeled

jobs:
  check_labels:
    name: Check milestone label for pull request
    runs-on: ubuntu-latest
    # dependabot prs do not have to be in milestone
    if: ${{ github.actor != 'dependabot[bot]' }}
    steps:
    - name: require milestone label for pull request
      uses: docker://agilepathway/pull-request-label-checker:latest@sha256:14f766a581beb1c2a43448b8cdc360f810f39101137c87dd55d252bb1a2e85dc
      with:
        prefix_mode: true
        one_of: "milestone"
        repo_token: ${{ secrets.GITHUB_TOKEN }}

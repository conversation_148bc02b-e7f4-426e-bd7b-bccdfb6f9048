# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: Build images

permissions: {}

on:
  pull_request:
    branches:
      - main
      - release-*

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-images:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup build env
        uses: ./.github/actions/setup-build-env
        timeout-minutes: 10
      - name: ko build
        run: VERSION=${{ github.ref_name }} make ko-build-all
      - name: <PERSON>vy Scan Image
        uses: aquasecurity/trivy-action@dc5a429b52fcf669ce959baa2c2dd26090d2a6c4 # v0.32.0
        with:
          scan-type: 'fs'
          ignore-unfixed: true
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
        env:
          # Trivy is returning TOOMANYREQUESTS
          # See: https://github.com/aquasecurity/trivy-action/issues/389#issuecomment-2385416577
          TRIVY_DB_REPOSITORY: 'public.ecr.aws/aquasecurity/trivy-db:2'

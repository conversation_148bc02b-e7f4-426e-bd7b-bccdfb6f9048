# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: Tests

permissions: {}

on:
  push:
    branches:
    - main
    - release-*
  pull_request:
    branches:
    - main
    - release-*

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  tests:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - name: Setup build env
      uses: ./.github/actions/setup-build-env
      timeout-minutes: 10
      with:
        free-disk-space: true
    - name: Unit test
      run: make test-unit
    - name: Upload coverage
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
      with:
        name: coverage.out
        path: coverage.out
        retention-days: 1
        if-no-files-found: error

  upload-to-codecov:
    needs:
    - tests
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - name: Download coverage
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
      with:
        name: coverage.out
    - name: Upload Report to Codecov
      uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24 # v5.4.3
      with:
        files: ./coverage.out
        fail_ci_if_error: true
        verbose: true
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
